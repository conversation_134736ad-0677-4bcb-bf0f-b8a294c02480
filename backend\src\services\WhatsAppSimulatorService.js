import { EventEmitter } from 'events';
import { Logger } from '../utils/Logger.js';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * WhatsApp Simulator Service - Simula integração WhatsApp para desenvolvimento
 * Permite testar todas as funcionalidades sem necessidade de Docker/WPPConnect Server
 */
export class WhatsAppSimulatorService extends EventEmitter {
  constructor() {
    super();
    
    // Configurações básicas
    this.sessionName = process.env.WHATSAPP_SESSION_NAME || 'vereadora-rafaela';
    this.isConnected = false;
    this.qrCode = null;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 3;
    
    // Logger
    this.logger = new Logger();
    
    // Simulação de dados
    this.simulatedContacts = [
      {
        id: '<EMAIL>',
        name: '<PERSON>',
        phone: '(84) 99999-9999',
        lastMessage: 'O<PERSON>á, preciso de ajuda com documentos',
        lastMessageTime: new Date(Date.now() - 300000), // 5 min atrás
        messageCount: 3
      },
      {
        id: '<EMAIL>',
        name: 'Maria Santos',
        phone: '(84) 88888-8888',
        lastMessage: 'Bom dia! Como faço para agendar consulta?',
        lastMessageTime: new Date(Date.now() - 600000), // 10 min atrás
        messageCount: 1
      },
      {
        id: '<EMAIL>',
        name: 'Pedro Costa',
        phone: '(84) 77777-7777',
        lastMessage: 'Obrigado pela ajuda!',
        lastMessageTime: new Date(Date.now() - 1800000), // 30 min atrás
        messageCount: 5
      }
    ];
    
    this.simulatedMessages = [
      {
        id: 'msg_001',
        from: '<EMAIL>',
        body: 'Olá, preciso de ajuda com documentos',
        timestamp: Date.now() - 300000,
        isFromMe: false,
        notifyName: 'João Silva'
      },
      {
        id: 'msg_002',
        from: '<EMAIL>',
        body: 'Bom dia! Como faço para agendar consulta?',
        timestamp: Date.now() - 600000,
        isFromMe: false,
        notifyName: 'Maria Santos'
      }
    ];
    
    // Callbacks para eventos
    this.onMessage = null;
    this.onReady = null;
    this.onDisconnected = null;
    this.onQRCode = null;
    this.onStatusChange = null;
    
    // Estado da simulação
    this.simulationMode = true;
    this.autoConnect = true; // Habilitar auto-connect
    this.connectionDelay = 3000; // 3 segundos para simular conexão
  }

  async initialize() {
    this.logger.info('🔄 Inicializando WhatsApp Simulator Service...');
    
    try {
      // Gerar QR Code simulado
      await this.generateSimulatedQRCode();
      
      // Simular processo de conexão
      if (this.autoConnect) {
        // Usar setImmediate para evitar problemas de timing
        setImmediate(() => {
          this.simulateConnection();
        });
      }
      
      this.logger.info('✅ WhatsApp Simulator Service inicializado');
      return true;
      
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar WhatsApp Simulator:', error.message);
      return false;
    }
  }

  async generateSimulatedQRCode() {
    try {
      // Gerar QR Code simulado
      const qrData = `whatsapp-simulator-${this.sessionName}-${Date.now()}`;
      const qrCodeBase64 = await QRCode.toDataURL(qrData, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      
      // Salvar QR Code como arquivo
      const qrCodePath = path.join(__dirname, '../../public/qr-code.png');
      const base64Data = qrCodeBase64.replace(/^data:image\/png;base64,/, '');
      
      // Garantir que o diretório existe
      const publicDir = path.dirname(qrCodePath);
      if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
      }
      
      fs.writeFileSync(qrCodePath, base64Data, 'base64');
      
      this.qrCode = {
        base64: qrCodeBase64,
        path: qrCodePath,
        timestamp: new Date().toISOString(),
        attempt: this.connectionAttempts + 1
      };
      
      this.logger.info('📱 QR Code simulado gerado');
      
      // Emitir evento de QR Code
      if (this.onQRCode) {
        this.onQRCode(this.qrCode);
      }
      
      this.emit('qrcode', this.qrCode);
      
    } catch (error) {
      this.logger.error('❌ Erro ao gerar QR Code simulado:', error.message);
    }
  }

  async simulateConnection() {
    this.logger.info('🔄 Simulando conexão WhatsApp...');

    this.connectionAttempts++;

    // Simular sucesso na conexão
    setTimeout(() => {
      try {
        this.isConnected = true;
        this.qrCode = null;

        this.logger.info('✅ WhatsApp Simulator conectado com sucesso!');

        // Emitir eventos com try-catch
        try {
          if (this.onReady) {
            this.onReady();
          }
        } catch (error) {
          this.logger.error('❌ Erro no callback onReady:', error.message);
        }

        try {
          if (this.onStatusChange) {
            this.onStatusChange('connected');
          }
        } catch (error) {
          this.logger.error('❌ Erro no callback onStatusChange:', error.message);
        }

        try {
          this.emit('ready');
          this.emit('connected');
        } catch (error) {
          this.logger.error('❌ Erro ao emitir eventos:', error.message);
        }

        // Simular recebimento de mensagens após conexão
        try {
          this.startMessageSimulation();
        } catch (error) {
          this.logger.error('❌ Erro ao iniciar simulação de mensagens:', error.message);
        }

      } catch (error) {
        this.logger.error('❌ Erro na simulação de conexão:', error.message);
      }
    }, 2000); // 2 segundos para simular processo de conexão
  }

  startMessageSimulation() {
    // Simular recebimento de mensagens a cada 60 segundos (menos frequente)
    this.messageInterval = setInterval(() => {
      if (this.isConnected && this.onMessage) {
        try {
          const randomMessage = this.generateRandomMessage();
          this.logger.info(`📨 Simulando mensagem recebida de ${randomMessage.notifyName}`);
          this.onMessage(randomMessage);
        } catch (error) {
          this.logger.error('❌ Erro ao simular mensagem:', error.message);
        }
      }
    }, 60000);
  }

  generateRandomMessage() {
    const messages = [
      'Olá! Preciso de informações sobre saúde pública.',
      'Bom dia! Como posso agendar uma consulta?',
      'Existe algum programa de auxílio para famílias?',
      'Gostaria de saber sobre os projetos da vereadora.',
      'Como faço para solicitar melhorias no meu bairro?',
      'Preciso de ajuda com documentação.',
      'Quando será a próxima sessão da câmara?'
    ];
    
    const names = ['Ana Costa', 'Carlos Silva', 'Fernanda Lima', 'Roberto Santos', 'Juliana Oliveira'];
    const phones = ['84999887766', '84988776655', '84977665544', '84966554433', '84955443322'];
    
    const randomIndex = Math.floor(Math.random() * messages.length);
    const nameIndex = Math.floor(Math.random() * names.length);
    
    return {
      id: `msg_sim_${Date.now()}`,
      from: `55${phones[nameIndex]}@c.us`,
      body: messages[randomIndex],
      timestamp: Date.now(),
      isFromMe: false,
      notifyName: names[nameIndex],
      type: 'chat'
    };
  }

  // Métodos de compatibilidade com WhatsAppHttpService
  async sendMessage(to, message) {
    if (!this.isConnected) {
      throw new Error('WhatsApp não está conectado');
    }
    
    this.logger.info(`📤 Simulando envio de mensagem para ${to}: ${message}`);
    
    // Simular delay de envio
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      success: true,
      messageId: `sim_${Date.now()}`,
      timestamp: new Date().toISOString()
    };
  }

  async getChats() {
    return this.simulatedContacts;
  }

  async getChatMessages(chatId, limit = 50) {
    return this.simulatedMessages.filter(msg => 
      msg.from === chatId
    ).slice(0, limit);
  }

  getConnectionStatus() {
    let status = 'Desconectado';
    let statusCode = 'disconnected';

    if (this.isConnected) {
      status = 'Conectado (Simulador)';
      statusCode = 'connected';
    } else if (this.qrCode) {
      status = 'Aguardando QR Code (Simulador)';
      statusCode = 'waiting_qr';
    } else if (this.connectionAttempts > 0) {
      status = 'Conectando... (Simulador)';
      statusCode = 'connecting';
    }

    return {
      isConnected: this.isConnected,
      sessionName: this.sessionName,
      qrCode: this.qrCode,
      hasQRCode: !!this.qrCode,
      qrCodeUrl: this.qrCode ? '/qr-code.png' : null,
      connectionAttempts: this.connectionAttempts,
      maxAttempts: this.maxConnectionAttempts,
      status,
      statusCode,
      timestamp: new Date().toISOString(),
      simulationMode: true
    };
  }

  // Métodos para configurar callbacks
  setOnMessage(callback) {
    this.onMessage = callback;
  }

  setOnReady(callback) {
    this.onReady = callback;
  }

  setOnDisconnected(callback) {
    this.onDisconnected = callback;
  }

  setOnQRCode(callback) {
    this.onQRCode = callback;
  }

  setOnStatusChange(callback) {
    this.onStatusChange = callback;
  }

  async stop() {
    this.logger.info('🛑 Parando WhatsApp Simulator...');
    this.isConnected = false;
    this.qrCode = null;

    // Limpar intervalos
    if (this.messageInterval) {
      clearInterval(this.messageInterval);
      this.messageInterval = null;
    }

    if (this.onDisconnected) {
      this.onDisconnected();
    }

    this.emit('disconnected');
    this.logger.info('✅ WhatsApp Simulator parado');
  }

  // Método para forçar reconexão
  async reconnect() {
    await this.stop();
    await this.initialize();
  }

  // Método para simular desconexão
  simulateDisconnection() {
    this.logger.warn('⚠️ Simulando desconexão WhatsApp...');
    this.isConnected = false;
    
    if (this.onDisconnected) {
      this.onDisconnected();
    }
    
    this.emit('disconnected');
    
    // Tentar reconectar após 10 segundos
    setTimeout(() => {
      this.simulateConnection();
    }, 10000);
  }
}

export default WhatsAppSimulatorService;
