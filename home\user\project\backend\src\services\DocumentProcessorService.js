
// Adicionar ao supportedFormats
this.supportedFormats = {
  // ... formatos existentes
  'jpg': { processor: 'processImage', maxSize: 10 * 1024 * 1024 },
  'jpeg': { processor: 'processImage', maxSize: 10 * 1024 * 1024 },
  'png': { processor: 'processImage', maxSize: 10 * 1024 * 1024 },
  'gif': { processor: 'processImage', maxSize: 10 * 1024 * 1024 },
  'webp': { processor: 'processImage', maxSize: 10 * 1024 * 1024 }
};

// Novo método para processar imagens
async processImage(filePath) {
  try {
    const buffer = await fs.readFile(filePath);
    const base64Image = buffer.toString('base64');
    
    // Usar Gemini para extrair texto da imagem (OCR)
    const extractedText = await this.extractTextFromImage(base64Image);
    
    return {
      text: extractedText,
      metadata: {
        type: 'image',
        size: buffer.length,
        base64: base64Image.substring(0, 100) + '...', // Preview
        hasText: extractedText.length > 0
      }
    };
  } catch (error) {
    throw new Error(`Erro ao processar imagem: ${error.message}`);
  }
}

async extractTextFromImage(base64Image) {
  // Implementar chamada para Gemini Vision
  const response = await this.geminiService.analyzeImage(base64Image, {
    prompt: "Extraia todo o texto visível nesta imagem. Se for um documento oficial, preserve a formatação e estrutura."
  });
  
  return response.text;
}

// Melhorar o processamento com IA
async processWithAI(content, fileInfo) {
  try {
    const aiAnalysis = await this.geminiService.analyzeDocument(content.text, {
      documentType: fileInfo.extension,
      context: 'municipal_legislation_parnamirim',
      extractEntities: true,
      summarize: true,
      categorize: true
    });
    
    return {
      ...content,
      aiAnalysis: {
        summary: aiAnalysis.summary,
        entities: aiAnalysis.entities,
        category: aiAnalysis.category,
        relevance: aiAnalysis.relevance,
        keyTopics: aiAnalysis.keyTopics
      }
    };
  } catch (error) {
    this.logger.warn('⚠️ Análise de IA falhou, continuando sem ela:', error);
    return content;
  }
}

// Melhorar o processamento de PDF
async processPDF(filePath) {
  try {
    const buffer = await fs.readFile(filePath);
    const data = await pdf(buffer);
    
    // Extrair imagens do PDF se houver
    const images = await this.extractImagesFromPDF(buffer);
    let imageTexts = '';
    
    if (images.length > 0) {
      for (const image of images) {
        const imageText = await this.extractTextFromImage(image.base64);
        imageTexts += `\n[IMAGEM ${image.page}]: ${imageText}\n`;
      }
    }
    
    return {
      text: data.text + imageTexts,
      metadata: {
        pages: data.numpages,
        info: data.info,
        version: data.version,
        hasImages: images.length > 0,
        imageCount: images.length
      }
    };
  } catch (error) {
    throw new Error(`Erro ao processar PDF: ${error.message}`);
  }
}
