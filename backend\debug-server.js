#!/usr/bin/env node

/**
 * Servidor de Debug
 * Para identificar problemas na inicialização
 */

console.log('🔍 Iniciando debug do servidor...');

// Capturar todos os erros
process.on('uncaughtException', (error) => {
  console.error('❌ UNCAUGHT EXCEPTION:', error);
  console.error('Stack:', error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ UNHANDLED REJECTION:', reason);
  console.error('Promise:', promise);
  process.exit(1);
});

async function debugImports() {
  console.log('🔍 Testando importações...');

  try {
    console.log('1. Express...');
    const express = await import('express');
    console.log('✅ Express OK');

    console.log('2. Dotenv...');
    const dotenv = await import('dotenv');
    dotenv.config();
    console.log('✅ Dotenv OK');

    console.log('3. ServerConfig...');
    const { ServerConfig } = await import('./src/config/ServerConfig.js');
    const config = new ServerConfig();
    console.log('✅ ServerConfig OK');

    console.log('4. Logger...');
    const { Logger } = await import('./src/utils/Logger.js');
    const logger = new Logger();
    console.log('✅ Logger OK');

    console.log('5. ServiceManager...');
    const { ServiceManager } = await import('./src/services/ServiceManager.js');
    const serviceManager = new ServiceManager(config, logger);
    console.log('✅ ServiceManager OK');

    console.log('6. Testando inicialização de serviços individuais...');
    
    console.log('6.1. Persistence...');
    try {
      await serviceManager.initializeService('persistence');
      console.log('✅ Persistence OK');
    } catch (error) {
      console.log('❌ Persistence falhou:', error.message);
    }

    console.log('6.2. Session...');
    try {
      await serviceManager.initializeService('session');
      console.log('✅ Session OK');
    } catch (error) {
      console.log('❌ Session falhou:', error.message);
    }

    console.log('6.3. RAG...');
    try {
      await serviceManager.initializeService('rag');
      console.log('✅ RAG OK');
    } catch (error) {
      console.log('❌ RAG falhou:', error.message);
    }

    console.log('7. Testando servidor Express básico...');
    const app = express.default();
    
    app.get('/', (req, res) => {
      res.json({ message: 'Debug server OK' });
    });

    const server = app.listen(3001, () => {
      console.log('✅ Servidor Express funcionando na porta 3001');
      console.log('🎉 Debug concluído com sucesso!');
      
      // Parar servidor após 5 segundos
      setTimeout(() => {
        server.close();
        console.log('✅ Debug finalizado');
        process.exit(0);
      }, 5000);
    });

  } catch (error) {
    console.error('❌ Erro durante debug:', error);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

debugImports();
