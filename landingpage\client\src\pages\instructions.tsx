import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Shield, Database, Code, ArrowRight, CheckCircle2, AlertCircle, User } from "lucide-react";

export default function Instructions() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <Shield className="mx-auto h-16 w-16 text-primary mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Sistema de Login Seguro
          </h1>
          <p className="text-gray-600 text-lg">
            Configurado para integração com Supabase
          </p>
        </div>

        {/* Demo Instructions */}
        <Card className="mb-8 bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5 text-blue-600" />
              <span>Teste a Demonstração</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg border border-blue-200">
                <h3 className="font-semibold text-gray-900 mb-2">Usuário de Teste</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">Email:</Badge>
                    <code className="bg-gray-100 px-2 py-1 rounded"><EMAIL></code>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">Senha:</Badge>
                    <code className="bg-gray-100 px-2 py-1 rounded">123456</code>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2 text-sm text-blue-700">
                <CheckCircle2 className="h-4 w-4" />
                <span>Use essas credenciais para testar o sistema de login</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Integration Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-green-600" />
              <span>Integração com Supabase</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-green-100 text-green-600 rounded-full p-1 mt-1">
                    <div className="w-4 h-4 flex items-center justify-center text-xs font-bold">1</div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Configure sua DATABASE_URL</h3>
                    <p className="text-gray-600 text-sm mt-1">
                      Adicione a URL de conexão do Supabase nos segredos do Replit
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-green-100 text-green-600 rounded-full p-1 mt-1">
                    <div className="w-4 h-4 flex items-center justify-center text-xs font-bold">2</div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Atualize o arquivo storage.ts</h3>
                    <p className="text-gray-600 text-sm mt-1">
                      Substitua a implementação em memória pela conexão real com o banco
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-green-100 text-green-600 rounded-full p-1 mt-1">
                    <div className="w-4 h-4 flex items-center justify-center text-xs font-bold">3</div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Execute as migrações</h3>
                    <p className="text-gray-600 text-sm mt-1">
                      Use <code className="bg-gray-100 px-2 py-1 rounded">npm run db:push</code> para criar as tabelas
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-amber-800">Configuração Atual</h4>
                    <p className="text-amber-700 text-sm mt-1">
                      Atualmente usando armazenamento em memória para demonstração. 
                      Os dados são perdidos quando o servidor é reiniciado.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-purple-600" />
              <span>Recursos de Segurança</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Senhas criptografadas com bcrypt</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Sessões com tokens seguros</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Cookies HTTP-only</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Validação de dados com Zod</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Expiração automática de sessões</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Proteção contra ataques CSRF</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Details */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Code className="h-5 w-5 text-blue-600" />
              <span>Detalhes Técnicos</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Tecnologias Utilizadas</h3>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary">React 18</Badge>
                  <Badge variant="secondary">TypeScript</Badge>
                  <Badge variant="secondary">Express.js</Badge>
                  <Badge variant="secondary">Drizzle ORM</Badge>
                  <Badge variant="secondary">Tailwind CSS</Badge>
                  <Badge variant="secondary">shadcn/ui</Badge>
                  <Badge variant="secondary">Zod</Badge>
                  <Badge variant="secondary">bcrypt</Badge>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Arquitetura</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <ArrowRight className="h-3 w-3" />
                    <span>Frontend: React com TanStack Query para gerenciamento de estado</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ArrowRight className="h-3 w-3" />
                    <span>Backend: Express.js com middleware de autenticação</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ArrowRight className="h-3 w-3" />
                    <span>Database: PostgreSQL com Drizzle ORM</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ArrowRight className="h-3 w-3" />
                    <span>Deploy: Pronto para Replit Deployments</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Button */}
        <div className="text-center">
          <Button asChild className="bg-primary hover:bg-primary/90 text-white px-8 py-3">
            <a href="/login">
              Testar Sistema de Login
              <ArrowRight className="ml-2 h-4 w-4" />
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}