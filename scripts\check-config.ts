#!/usr/bin/env tsx

/**
 * Script para verificar configurações do Supabase e Gemini
 * Execute com: npx tsx scripts/check-config.ts
 */

import { supabase, isSupabaseConfigured, supabaseUtils } from '../config/supabase';
import { isGeminiConfigured, geminiUtils } from '../config/gemini';

interface ConfigCheck {
  name: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  details?: any;
}

class ConfigChecker {
  private checks: ConfigCheck[] = [];

  private addCheck(name: string, status: 'success' | 'warning' | 'error', message: string, details?: any) {
    this.checks.push({ name, status, message, details });
  }

  private getStatusIcon(status: string): string {
    switch (status) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return '❓';
    }
  }

  async checkEnvironmentVariables() {
    console.log('\n🔍 Verificando Variáveis de Ambiente...\n');

    // Verificar Supabase
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (supabaseUrl && supabaseKey) {
      this.addCheck(
        'Supabase Environment',
        'success',
        'Variáveis do Supabase configuradas',
        { url: supabaseUrl.substring(0, 30) + '...', hasKey: !!supabaseKey }
      );
    } else {
      this.addCheck(
        'Supabase Environment',
        'error',
        'Variáveis do Supabase não configuradas',
        { 
          missingUrl: !supabaseUrl,
          missingKey: !supabaseKey,
          help: 'Configure VITE_SUPABASE_URL e VITE_SUPABASE_ANON_KEY no arquivo .env'
        }
      );
    }

    // Verificar Gemini
    const geminiKey = import.meta.env.VITE_GEMINI_API_KEY;

    if (geminiKey) {
      this.addCheck(
        'Gemini Environment',
        'success',
        'API Key do Gemini configurada',
        { keyPrefix: geminiKey.substring(0, 10) + '...' }
      );
    } else {
      this.addCheck(
        'Gemini Environment',
        'error',
        'API Key do Gemini não configurada',
        { help: 'Configure VITE_GEMINI_API_KEY no arquivo .env' }
      );
    }

    // Verificar outras variáveis importantes
    const vereadoraName = import.meta.env.VITE_VEREADORA_NAME;
    const municipio = import.meta.env.VITE_MUNICIPIO;

    if (vereadoraName && municipio) {
      this.addCheck(
        'App Configuration',
        'success',
        'Configurações da aplicação definidas',
        { vereadora: vereadoraName, municipio }
      );
    } else {
      this.addCheck(
        'App Configuration',
        'warning',
        'Algumas configurações da aplicação não definidas',
        { 
          missingName: !vereadoraName,
          missingMunicipio: !municipio,
          help: 'Configure VITE_VEREADORA_NAME e VITE_MUNICIPIO para personalização completa'
        }
      );
    }
  }

  async checkSupabaseConnection() {
    console.log('\n🗄️ Verificando Conexão com Supabase...\n');

    if (!isSupabaseConfigured) {
      this.addCheck(
        'Supabase Connection',
        'error',
        'Supabase não configurado',
        { help: 'Configure as variáveis de ambiente do Supabase' }
      );
      return;
    }

    try {
      const isConnected = await supabaseUtils.testConnection();
      
      if (isConnected) {
        this.addCheck(
          'Supabase Connection',
          'success',
          'Conexão com Supabase estabelecida'
        );

        // Verificar estatísticas
        try {
          const stats = await supabaseUtils.getStats();
          this.addCheck(
            'Supabase Database',
            'success',
            'Banco de dados acessível',
            stats
          );
        } catch (error) {
          this.addCheck(
            'Supabase Database',
            'warning',
            'Conexão OK, mas erro ao obter estatísticas',
            { error: error instanceof Error ? error.message : 'Erro desconhecido' }
          );
        }

      } else {
        this.addCheck(
          'Supabase Connection',
          'error',
          'Falha na conexão com Supabase',
          { 
            help: 'Verifique URL e API key, ou se o projeto está ativo',
            troubleshooting: [
              'Confirme se a URL está correta (sem barra no final)',
              'Verifique se a anon key está correta',
              'Confirme se o projeto Supabase está ativo',
              'Verifique se as políticas RLS estão configuradas'
            ]
          }
        );
      }

    } catch (error) {
      this.addCheck(
        'Supabase Connection',
        'error',
        'Erro ao testar conexão com Supabase',
        { 
          error: error instanceof Error ? error.message : 'Erro desconhecido',
          help: 'Verifique as configurações de rede e CORS'
        }
      );
    }
  }

  async checkGeminiConnection() {
    console.log('\n🤖 Verificando Conexão com Gemini...\n');

    if (!isGeminiConfigured) {
      this.addCheck(
        'Gemini Connection',
        'error',
        'Gemini não configurado',
        { help: 'Configure VITE_GEMINI_API_KEY no arquivo .env' }
      );
      return;
    }

    try {
      const isConnected = await geminiUtils.testConnection();
      
      if (isConnected) {
        this.addCheck(
          'Gemini Connection',
          'success',
          'Conexão com Gemini estabelecida'
        );

        // Testar contagem de tokens
        try {
          const tokenCount = await geminiUtils.countTokens('Teste de contagem de tokens');
          this.addCheck(
            'Gemini Token Counting',
            'success',
            'Contagem de tokens funcionando',
            { testTokens: tokenCount }
          );
        } catch (error) {
          this.addCheck(
            'Gemini Token Counting',
            'warning',
            'Erro na contagem de tokens',
            { error: error instanceof Error ? error.message : 'Erro desconhecido' }
          );
        }

        // Testar embeddings
        try {
          const embedding = await geminiUtils.generateEmbedding('Teste de embedding');
          this.addCheck(
            'Gemini Embeddings',
            'success',
            'Geração de embeddings funcionando',
            { embeddingDimensions: embedding.length }
          );
        } catch (error) {
          this.addCheck(
            'Gemini Embeddings',
            'warning',
            'Erro na geração de embeddings',
            { error: error instanceof Error ? error.message : 'Erro desconhecido' }
          );
        }

      } else {
        this.addCheck(
          'Gemini Connection',
          'error',
          'Falha na conexão com Gemini',
          { 
            help: 'Verifique a API key e cotas',
            troubleshooting: [
              'Confirme se a API key está correta',
              'Verifique se a API está habilitada no Google Cloud',
              'Confirme se há cota disponível',
              'Verifique restrições de domínio'
            ]
          }
        );
      }

    } catch (error) {
      this.addCheck(
        'Gemini Connection',
        'error',
        'Erro ao testar conexão com Gemini',
        { 
          error: error instanceof Error ? error.message : 'Erro desconhecido',
          help: 'Verifique a API key e configurações de rede'
        }
      );
    }
  }

  async checkIntegration() {
    console.log('\n🔗 Verificando Integração...\n');

    if (isSupabaseConfigured && isGeminiConfigured) {
      this.addCheck(
        'Full Integration',
        'success',
        'Supabase e Gemini configurados - Sistema RAG completo disponível',
        { 
          features: [
            'Persistência de dados',
            'Geração de respostas com IA',
            'Busca semântica',
            'Análise de documentos'
          ]
        }
      );
    } else if (isSupabaseConfigured) {
      this.addCheck(
        'Partial Integration',
        'warning',
        'Apenas Supabase configurado - Funcionalidade limitada',
        { 
          available: ['Persistência de dados', 'Histórico de conversas'],
          missing: ['IA avançada', 'Busca semântica', 'Análise de documentos'],
          help: 'Configure Gemini para funcionalidade completa'
        }
      );
    } else if (isGeminiConfigured) {
      this.addCheck(
        'Partial Integration',
        'warning',
        'Apenas Gemini configurado - Dados não persistem',
        { 
          available: ['IA avançada', 'Geração de respostas'],
          missing: ['Persistência', 'Histórico', 'Busca em documentos'],
          help: 'Configure Supabase para persistência de dados'
        }
      );
    } else {
      this.addCheck(
        'No Integration',
        'error',
        'Nenhum serviço configurado - Sistema funcionará apenas offline',
        { 
          available: ['Interface básica', 'Respostas pré-definidas'],
          missing: ['IA', 'Persistência', 'Busca semântica'],
          help: 'Configure Supabase e Gemini para funcionalidade completa'
        }
      );
    }
  }

  printResults() {
    console.log('\n📊 Resultado da Verificação:\n');
    console.log('='.repeat(80));

    let successCount = 0;
    let warningCount = 0;
    let errorCount = 0;

    this.checks.forEach(check => {
      const icon = this.getStatusIcon(check.status);
      console.log(`${icon} ${check.name}: ${check.message}`);
      
      if (check.details) {
        console.log(`   Detalhes:`, check.details);
      }
      console.log('');

      switch (check.status) {
        case 'success': successCount++; break;
        case 'warning': warningCount++; break;
        case 'error': errorCount++; break;
      }
    });

    console.log('='.repeat(80));
    console.log(`📈 Resumo: ${successCount} sucessos, ${warningCount} avisos, ${errorCount} erros`);

    if (errorCount === 0 && warningCount === 0) {
      console.log('\n🎉 Todas as configurações estão corretas! Sistema pronto para uso.');
    } else if (errorCount === 0) {
      console.log('\n✅ Configurações básicas OK. Alguns avisos podem ser ignorados.');
    } else {
      console.log('\n⚠️ Há erros que precisam ser corrigidos antes do uso completo.');
      console.log('\n📖 Consulte o guia: docs/SETUP_SUPABASE_GEMINI.md');
    }
  }

  async run() {
    console.log('🏛️ Verificador de Configuração - Sistema RAG Vereadora Rafaela de Nilda');
    console.log('='.repeat(80));

    await this.checkEnvironmentVariables();
    await this.checkSupabaseConnection();
    await this.checkGeminiConnection();
    await this.checkIntegration();

    this.printResults();
  }
}

// Executar verificação se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new ConfigChecker();
  checker.run().catch(console.error);
}

export { ConfigChecker };
