# 🚫 Sistema de Filtragem Inteligente de Mensagens WhatsApp

## 📋 Objetivo
Implementar um sistema que **ignora atualizações de status** e **responde apenas a mensagens novas**, usando mensagens antigas apenas para **contexto histórico**.

## 🎯 Funcionalidades Implementadas

### 1. **Controle de Sessão Ativa**
- ✅ **Timestamp de início**: Marca quando a sessão WhatsApp fica ativa
- ✅ **Flag de sessão**: Controla se deve processar mensagens novas
- ✅ **Logs informativos**: Mostra quando a sessão é iniciada

```javascript
// WhatsAppService.js
this.sessionStartTime = Date.now();
this.isSessionActive = true;
```

### 2. **Filtragem por Tipo de Mensagem**
- ✅ **Tipos ignorados**: e2e_notification, gp2, system, protocol, revoked, etc.
- ✅ **Status ignorados**: Atualizações de status do WhatsApp
- ✅ **Mensagens vazias**: Filtra mensagens sem conteúdo relevante

```javascript
shouldIgnoreMessage(message) {
  const ignoredTypes = [
    'e2e_notification',     // Notificações de criptografia
    'gp2',                  // Notificações de grupo
    'notification_template', // Templates de notificação
    'hsm',                  // Mensagens de template
    'system',               // Mensagens do sistema
    'protocol',             // Mensagens de protocolo
    'revoked',              // Mensagens revogadas
    'ciphertext',           // Texto cifrado
    'unknown'               // Tipo desconhecido
  ];
  
  return ignoredTypes.includes(message.type) || message.isStatus === true;
}
```

### 3. **Filtragem por Timestamp**
- ✅ **Mensagens novas**: Apenas mensagens após `sessionStartTime` são processadas
- ✅ **Mensagens antigas**: Usadas apenas para contexto histórico
- ✅ **Conversão de timestamp**: Suporte a timestamps em segundos e milissegundos

```javascript
isNewMessage(message) {
  if (!this.isSessionActive || !this.sessionStartTime) {
    return false; // Sessão não ativa = mensagem para contexto
  }
  
  let messageTime = message.timestamp;
  if (messageTime < 1000000000000) {
    messageTime = messageTime * 1000; // Converter segundos para milissegundos
  }
  
  return messageTime >= this.sessionStartTime;
}
```

### 4. **Contexto Histórico Inteligente**
- ✅ **Obtenção de histórico**: Carrega últimas 10 mensagens relevantes
- ✅ **Filtragem de contexto**: Apenas mensagens de texto válidas
- ✅ **Integração com RAG**: Contexto enviado para o sistema de IA

```javascript
async getConversationContext(userPhone) {
  const messagesFile = `data/sessions/messages/${userPhone}.json`;
  const messages = JSON.parse(await fs.readFile(messagesFile, 'utf8'));
  
  return messages
    .filter(msg => 
      msg.type === 'chat' && 
      msg.body && 
      msg.body.trim().length > 0 &&
      !msg.isStatus
    )
    .slice(-10) // Últimas 10 mensagens
    .map(msg => ({
      from: msg.fromMe ? 'Vereadora Rafaela' : 'Cidadão',
      text: msg.body,
      timestamp: msg.timestamp
    }));
}
```

## 🔄 Fluxo de Processamento

### **Mensagem Recebida**
1. **Log detalhado** da mensagem recebida
2. **Filtrar mensagens próprias** (`fromMe: true`)
3. **Filtrar grupos** (se desabilitado)
4. **Filtrar por tipo** (status, notificações, etc.)
5. **Verificar timestamp** (nova vs antiga)
6. **Processar ou ignorar**

### **Mensagens Novas** ✅
- **São processadas** pelo sistema de IA
- **Geram respostas** automáticas
- **Incluem contexto histórico** para melhor compreensão

### **Mensagens Antigas** 📜
- **São ignoradas** para resposta
- **São salvas** para contexto futuro
- **Aparecem nos logs** como "MENSAGEM ANTIGA IGNORADA"

## 📊 Logs Informativos

### **Mensagem Nova Detectada**
```
✅ MENSAGEM NOVA DETECTADA - Será processada e respondida
🕐 Mensagem timestamp: 2025-07-02T18:15:30.000Z, Sessão iniciada: 2025-07-02T18:10:00.000Z
```

### **Mensagem Ignorada por Tipo**
```
🚫 MENSAGEM IGNORADA - Tipo: e2e_notification, Status: false, Motivo: Atualização de status/notificação
```

### **Mensagem Antiga Ignorada**
```
📜 MENSAGEM ANTIGA IGNORADA - Usada apenas para contexto. Timestamp: 2025-07-02T17:30:00.000Z
```

## 🎯 Benefícios

### **1. Eficiência**
- ❌ Não responde a notificações desnecessárias
- ❌ Não processa mensagens antigas repetidamente
- ✅ Foca apenas em mensagens novas relevantes

### **2. Contexto Inteligente**
- 📜 Usa histórico para entender a conversa
- 🧠 IA tem contexto completo sem responder a tudo
- 💬 Respostas mais coerentes e contextualizadas

### **3. Experiência do Usuário**
- 🚫 Evita spam de respostas automáticas
- ⚡ Respostas apenas quando necessário
- 🎯 Interação mais natural e humana

## 🔧 Configuração

### **Variáveis de Ambiente**
```env
ALLOW_GROUP_MESSAGES=false  # Permitir mensagens de grupo
AUTO_REPLY_ENABLED=true     # Respostas automáticas ativas
BUSINESS_HOURS_ONLY=false   # Apenas horário comercial
```

### **Tipos de Mensagem Suportados**
- ✅ `chat` - Mensagens de texto normais
- ✅ `image` - Imagens com legenda
- ✅ `audio` - Áudios (se configurado)
- ❌ `e2e_notification` - Notificações de criptografia
- ❌ `system` - Mensagens do sistema
- ❌ `protocol` - Mensagens de protocolo

## 🚀 Status da Implementação

- ✅ **Filtragem por tipo** implementada
- ✅ **Filtragem por timestamp** implementada  
- ✅ **Contexto histórico** implementado
- ✅ **Logs informativos** implementados
- ✅ **Integração com RAG** implementada
- ✅ **Testes básicos** realizados

## 📝 Próximos Passos

1. **Testar com mensagens reais** do WhatsApp
2. **Ajustar filtros** conforme necessário
3. **Monitorar logs** para verificar eficácia
4. **Otimizar contexto histórico** se necessário
