import { embeddingService } from './embeddingService';
import { databaseService } from './databaseService';
import { cacheService } from './cacheService';
import type { DocumentChunk, EmbeddingResult } from '../types';

interface SearchResult {
  chunk: DocumentChunk;
  similarity: number;
  relevanceScore: number;
}

interface SearchOptions {
  limit?: number;
  minSimilarity?: number;
  boostRecent?: boolean;
  documentTypes?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

class SemanticSearchService {
  
  async searchSimilarChunks(
    query: string, 
    options: SearchOptions = {}
  ): Promise<DocumentChunk[]> {
    const {
      limit = 5,
      minSimilarity = 0.3,
      boostRecent = true,
      documentTypes,
      dateRange
    } = options;

    try {
      console.log(`🔍 Busca semântica: "${query}" (limite: ${limit})`);
      
      // 1. Verificar cache de chunks primeiro
      const cacheKey = this.generateCacheKey(query, options);
      const cachedResults = await cacheService.getChunks(cacheKey, limit);
      if (cachedResults) {
        console.log('⚡ Resultados encontrados no cache');
        return cachedResults;
      }

      // 2. Gerar embedding da query
      const queryEmbedding = await embeddingService.generateEmbedding(query);
      
      // 3. Buscar todos os chunks disponíveis
      const allChunks = await databaseService.getDocumentChunks();

      console.log(`📊 Total de chunks na base: ${allChunks.length}`);

      if (allChunks.length === 0) {
        console.log('📭 Nenhum chunk encontrado na base');
        return [];
      }

      // Log de debug para verificar tipos de embedding
      const embeddingTypes = allChunks.slice(0, 3).map(chunk => ({
        id: chunk.id,
        embeddingType: typeof chunk.embedding,
        isArray: Array.isArray(chunk.embedding),
        length: Array.isArray(chunk.embedding) ? chunk.embedding.length : 'N/A'
      }));
      console.log('🔍 Tipos de embedding encontrados:', embeddingTypes);

      // Debug: mostrar alguns chunks
      if (allChunks.length > 0) {
        console.log('📋 Exemplo de chunk encontrado:', {
          id: allChunks[0].id,
          document_id: allChunks[0].document_id,
          content_preview: allChunks[0].content.substring(0, 100) + '...',
          document_name: allChunks[0].metadata?.document_name
        });
      }

      // 4. Calcular similaridades
      const searchResults: SearchResult[] = [];
      
      for (const chunk of allChunks) {
        // Aplicar filtros se especificados
        if (!this.passesFilters(chunk, { documentTypes, dateRange })) {
          continue;
        }

        // Verificar e corrigir embedding se necessário
        let chunkEmbedding = chunk.embedding;

        // Se embedding for string, tentar converter para array
        if (typeof chunkEmbedding === 'string') {
          try {
            chunkEmbedding = JSON.parse(chunkEmbedding);
          } catch (error) {
            console.warn('⚠️ Erro ao converter embedding de string para array:', {
              id: chunk.id,
              error: error.message
            });
            continue;
          }
        }

        // Verificar se o chunk tem embedding válido
        if (!chunkEmbedding || !Array.isArray(chunkEmbedding) || chunkEmbedding.length === 0) {
          console.warn('⚠️ Chunk sem embedding válido:', {
            id: chunk.id,
            document_id: chunk.document_id,
            embeddingType: typeof chunkEmbedding,
            embeddingLength: Array.isArray(chunkEmbedding) ? chunkEmbedding.length : 'N/A'
          });
          continue;
        }

        const similarity = await this.calculateSimilarity(
          queryEmbedding.embedding,
          chunkEmbedding
        );

        if (similarity >= minSimilarity) {
          // Criar chunk com embedding corrigido
          const correctedChunk = {
            ...chunk,
            embedding: chunkEmbedding
          };

          const relevanceScore = this.calculateRelevanceScore(
            correctedChunk,
            similarity,
            query,
            boostRecent
          );

          searchResults.push({
            chunk: correctedChunk,
            similarity,
            relevanceScore
          });
        }
      }

      // 5. Ordenar por relevância e similaridade
      searchResults.sort((a, b) => {
        // Primeiro por relevância, depois por similaridade
        if (Math.abs(a.relevanceScore - b.relevanceScore) > 0.1) {
          return b.relevanceScore - a.relevanceScore;
        }
        return b.similarity - a.similarity;
      });

      // 6. Retornar top resultados
      const topResults = searchResults
        .slice(0, limit)
        .map(result => result.chunk);

      console.log(`✅ Encontrados ${topResults.length} chunks relevantes`);
      console.log(`📊 Similaridades: ${searchResults.slice(0, 3).map(r => r.similarity.toFixed(3)).join(', ')}`);

      // Debug: mostrar detalhes dos top resultados
      searchResults.slice(0, 3).forEach((result, index) => {
        console.log(`📋 Resultado ${index + 1}:`, {
          similarity: result.similarity.toFixed(3),
          relevance: result.relevanceScore.toFixed(3),
          document: result.chunk.metadata?.document_name,
          preview: result.chunk.content.substring(0, 100) + '...'
        });
      });

      // 7. Salvar no cache
      await cacheService.setChunks(cacheKey, limit, topResults);

      return topResults;

    } catch (error) {
      console.error('❌ Erro na busca semântica:', error);
      return [];
    }
  }

  private async calculateSimilarity(
    embedding1: number[],
    embedding2: number[]
  ): Promise<number> {
    // Validar embeddings
    if (!embedding1 || !Array.isArray(embedding1) || embedding1.length === 0) {
      console.warn('⚠️ Embedding1 inválido:', embedding1);
      return 0;
    }

    if (!embedding2 || !Array.isArray(embedding2) || embedding2.length === 0) {
      console.warn('⚠️ Embedding2 inválido:', embedding2);
      return 0;
    }

    // Gerar hashes para cache
    const hash1 = this.hashEmbedding(embedding1);
    const hash2 = this.hashEmbedding(embedding2);

    // Verificar cache de similaridade
    const cachedSimilarity = await cacheService.getSimilarity(hash1, hash2);
    if (cachedSimilarity !== null) {
      return cachedSimilarity;
    }

    // Calcular similaridade de cosseno
    const similarity = this.cosineSimilarity(embedding1, embedding2);

    // Salvar no cache
    await cacheService.setSimilarity(hash1, hash2, similarity);

    return similarity;
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      console.warn('⚠️ Embeddings com tamanhos diferentes');
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    
    if (magnitude === 0) {
      return 0;
    }

    return dotProduct / magnitude;
  }

  private calculateRelevanceScore(
    chunk: DocumentChunk,
    similarity: number,
    query: string,
    boostRecent: boolean
  ): number {
    let score = similarity;

    // Boost baseado em palavras-chave exatas
    const queryWords = query.toLowerCase().split(/\s+/);
    const chunkWords = chunk.content.toLowerCase();
    
    let exactMatches = 0;
    for (const word of queryWords) {
      if (word.length > 3 && chunkWords.includes(word)) {
        exactMatches++;
      }
    }
    
    const exactMatchBoost = (exactMatches / queryWords.length) * 0.2;
    score += exactMatchBoost;

    // Boost para documentos recentes (se habilitado)
    if (boostRecent && chunk.metadata.document_date) {
      const docDate = new Date(chunk.metadata.document_date);
      const daysSinceDoc = (Date.now() - docDate.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceDoc < 30) {
        score += 0.1; // Boost para documentos dos últimos 30 dias
      } else if (daysSinceDoc < 90) {
        score += 0.05; // Boost menor para documentos dos últimos 90 dias
      }
    }

    // Boost baseado na posição do chunk (primeiros chunks são mais importantes)
    if (chunk.chunk_index < 3) {
      score += 0.05;
    }

    // Penalidade para chunks muito pequenos
    if (chunk.content.length < 100) {
      score -= 0.1;
    }

    // Boost para chunks com metadados importantes
    if (chunk.metadata.section && chunk.metadata.section.includes('conclus')) {
      score += 0.05;
    }

    return Math.max(0, Math.min(1, score));
  }

  private passesFilters(
    chunk: DocumentChunk,
    filters: {
      documentTypes?: string[];
      dateRange?: { start: Date; end: Date };
    }
  ): boolean {
    // Filtro por tipo de documento
    if (filters.documentTypes && filters.documentTypes.length > 0) {
      const docType = chunk.metadata.document_type;
      if (!docType || !filters.documentTypes.includes(docType)) {
        return false;
      }
    }

    // Filtro por data
    if (filters.dateRange) {
      const docDate = chunk.metadata.document_date 
        ? new Date(chunk.metadata.document_date)
        : null;
      
      if (!docDate) return false;
      
      if (docDate < filters.dateRange.start || docDate > filters.dateRange.end) {
        return false;
      }
    }

    return true;
  }

  private generateCacheKey(query: string, options: SearchOptions): string {
    const normalizedQuery = query.toLowerCase().trim().replace(/\s+/g, ' ');
    const optionsStr = JSON.stringify(options);
    return `search_${this.hashString(normalizedQuery + optionsStr)}`;
  }

  private hashEmbedding(embedding: number[]): string {
    // Validar se embedding é um array válido
    if (!embedding || !Array.isArray(embedding) || embedding.length === 0) {
      console.warn('⚠️ Embedding inválido para hash:', embedding);
      return 'invalid_embedding';
    }

    // Criar hash simples do embedding para cache
    const sample = embedding.slice(0, 10).map(n => Math.round(n * 1000));
    return this.hashString(sample.join(','));
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  // Método para busca híbrida (semântica + palavra-chave)
  async hybridSearch(
    query: string,
    options: SearchOptions = {}
  ): Promise<DocumentChunk[]> {
    const semanticResults = await this.searchSimilarChunks(query, options);
    
    // Busca adicional por palavras-chave para garantir cobertura
    const keywordResults = await this.keywordSearch(query, options);
    
    // Combinar e deduplificar resultados
    const combined = this.combineResults(semanticResults, keywordResults, options.limit || 5);
    
    return combined;
  }

  private async keywordSearch(
    query: string,
    options: SearchOptions
  ): Promise<DocumentChunk[]> {
    const allChunks = await databaseService.getDocumentChunks();
    const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    
    const results: { chunk: DocumentChunk; score: number }[] = [];
    
    for (const chunk of allChunks) {
      if (!this.passesFilters(chunk, options)) continue;
      
      const content = chunk.content.toLowerCase();
      let score = 0;
      
      for (const word of queryWords) {
        const matches = (content.match(new RegExp(word, 'g')) || []).length;
        score += matches * (word.length / 10); // Palavras maiores têm mais peso
      }
      
      if (score > 0) {
        results.push({ chunk, score });
      }
    }
    
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, Math.ceil((options.limit || 5) / 2))
      .map(r => r.chunk);
  }

  private combineResults(
    semanticResults: DocumentChunk[],
    keywordResults: DocumentChunk[],
    limit: number
  ): DocumentChunk[] {
    const seen = new Set<string>();
    const combined: DocumentChunk[] = [];
    
    // Priorizar resultados semânticos
    for (const chunk of semanticResults) {
      if (!seen.has(chunk.id) && combined.length < limit) {
        combined.push(chunk);
        seen.add(chunk.id);
      }
    }
    
    // Adicionar resultados de palavra-chave únicos
    for (const chunk of keywordResults) {
      if (!seen.has(chunk.id) && combined.length < limit) {
        combined.push(chunk);
        seen.add(chunk.id);
      }
    }
    
    return combined;
  }

  // Método para análise de qualidade da busca
  analyzeSearchQuality(query: string, results: DocumentChunk[]): {
    averageSimilarity: number;
    diversityScore: number;
    coverageScore: number;
  } {
    if (results.length === 0) {
      return { averageSimilarity: 0, diversityScore: 0, coverageScore: 0 };
    }

    // Calcular diversidade (documentos únicos)
    const uniqueDocs = new Set(results.map(r => r.document_id));
    const diversityScore = uniqueDocs.size / results.length;

    // Calcular cobertura (palavras da query encontradas)
    const queryWords = query.toLowerCase().split(/\s+/);
    const foundWords = new Set<string>();
    
    for (const result of results) {
      const content = result.content.toLowerCase();
      for (const word of queryWords) {
        if (content.includes(word)) {
          foundWords.add(word);
        }
      }
    }
    
    const coverageScore = foundWords.size / queryWords.length;

    return {
      averageSimilarity: 0.8, // Placeholder - seria calculado com embeddings reais
      diversityScore,
      coverageScore
    };
  }
}

export const semanticSearchService = new SemanticSearchService();
