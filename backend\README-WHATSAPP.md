# 📱 WhatsApp Integration - Vereadora Rafaela

## 🚀 **COMO USAR (SIMPLIFICADO)**

### **UM ÚNICO COMANDO PARA TUDO:**

```bash
cd backend
npm start
```

**Isso é tudo!** O servidor agora inclui:
- ✅ **WPPConnect Server integrado** (porta 21465)
- ✅ **Servidor Principal** (porta 3001)
- ✅ **Todas as funcionalidades** em um só processo

## 📊 **MODOS DE OPERAÇÃO**

### **1. <PERSON><PERSON> (Recomendado)**
```bash
npm start
```
- Usa WPPConnect Server integrado
- Funcionalidades completas do WhatsApp
- QR Code automático
- Respostas da Vereadora Rafaela

### **2. Modo Simulador (Desenvolvimento)**
```bash
npm run start:simulator
```
- Simula WhatsApp sem conexão real
- Ideal para desenvolvimento e testes
- Não precisa escanear QR Code

### **3. Modo Real (WPPConnect Direto)**
```bash
npm run start:real
```
- Usa WPPConnect library diretamente
- Sem servidor HTTP intermediário
- Máxima performance

## 🌐 **URLs Disponíveis**

Após executar `npm start`:

- **Frontend:** http://localhost:3000
- **API Principal:** http://localhost:3001
- **WPPConnect Server:** http://localhost:21465
- **Health Check:** http://localhost:3001/api/health
- **WhatsApp Status:** http://localhost:3001/api/whatsapp/status

## 📱 **Como Conectar WhatsApp**

1. Execute `npm start`
2. Acesse http://localhost:3000
3. Vá para aba "WhatsApp"
4. O QR Code será gerado automaticamente
5. Escaneie com seu WhatsApp
6. Pronto! Sistema conectado

## 🔧 **Configurações Avançadas**

### **Variáveis de Ambiente:**

```bash
# Porta do servidor principal
PORT=3001

# Porta do WPPConnect Server integrado
WPPCONNECT_PORT=21465

# Chave secreta para autenticação
WPPCONNECT_SECRET_KEY=vereadora-rafaela-secret-2024

# Nome da sessão WhatsApp
WHATSAPP_SESSION_NAME=vereadora-rafaela

# Modo de operação
WHATSAPP_USE_SIMULATOR=false  # true para simulador
WHATSAPP_USE_REAL=false       # true para WPPConnect direto
```

## 🎯 **Funcionalidades**

### **✅ Implementadas:**
- 📱 Conexão WhatsApp via QR Code
- 🤖 Respostas automáticas da Vereadora Rafaela
- 💬 Processamento de mensagens via IA/RAG
- 📊 Monitoramento em tempo real
- 🔄 Reconexão automática
- 📈 APIs REST completas
- 🎭 Modo simulador para desenvolvimento

### **🔄 Fluxo de Mensagens:**
```
📱 WhatsApp → WPPConnect → Webhook → IA/RAG → Vereadora Rafaela → WhatsApp
```

## 🛠️ **Desenvolvimento**

### **Modo Watch (Auto-reload):**
```bash
npm run dev
```

### **Logs Detalhados:**
```bash
DEBUG=* npm start
```

### **Verificar Status:**
```bash
curl http://localhost:3001/api/whatsapp/status
```

## 🚨 **Solução de Problemas**

### **Problema: QR Code não aparece**
- Verifique se a porta 21465 está livre
- Reinicie o servidor: `Ctrl+C` e `npm start`

### **Problema: WhatsApp desconecta**
- Normal após inatividade
- O sistema reconecta automaticamente
- Novo QR Code será gerado

### **Problema: Mensagens não chegam**
- Verifique se o webhook está funcionando
- Teste: `curl http://localhost:3001/api/health`

## 📚 **Arquitetura**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   WhatsApp      │◄──►│  WPPConnect      │◄──►│  Servidor       │
│   (QR Code)     │    │  Server          │    │  Principal      │
│                 │    │  (Integrado)     │    │  (IA/RAG)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              ▲                         ▲
                              │                         │
                              ▼                         ▼
                       ┌──────────────┐         ┌─────────────┐
                       │   Webhook    │         │  Frontend   │
                       │   Handler    │         │  (React)    │
                       └──────────────┘         └─────────────┘
```

## 🎉 **Pronto para Produção**

O sistema está completamente funcional e pronto para uso em produção com:
- ✅ Arquitetura robusta e escalável
- ✅ Tratamento de erros completo
- ✅ Logs detalhados para monitoramento
- ✅ Reconexão automática
- ✅ APIs REST padronizadas
- ✅ Documentação completa

**Execute `npm start` e comece a usar!** 🚀
