# ========================================
# CONFIGURAÇÕES DE PRODUÇÃO
# Backend Vereadora Rafaela de Nilda v2.0
# ========================================

# Configurações Básicas do Servidor
PORT=3001
HOST=0.0.0.0
NODE_ENV=production
BODY_LIMIT=10mb
SERVER_TIMEOUT=30000

# Configurações de CORS (Produção)
CORS_ORIGINS=https://vereadorarafaela.com.br,https://www.vereadorarafaela.com.br
CORS_CREDENTIALS=true
FRONTEND_URL=https://vereadorarafaela.com.br

# Configurações de Segurança (Produção)
ENABLE_CSP=true
ENABLE_ORIGIN_VERIFICATION=true
ADMIN_API_KEY=your-secure-admin-key-here
WHATSAPP_API_KEY=your-secure-whatsapp-key-here

# Configurações de Rate Limiting (Produção)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=50
RATE_LIMIT_SKIP_SUCCESS=false

# ========================================
# CONFIGURAÇÕES DO WHATSAPP (PRODUÇÃO)
# ========================================

# Configurações Básicas do WhatsApp
WHATSAPP_SESSION_NAME=vereadora-rafaela-prod
WHATSAPP_AUTO_CLOSE=false
WHATSAPP_DISABLE_WELCOME=false

# Seleção do Serviço WhatsApp (Produção)
WHATSAPP_USE_SIMULATOR=false
WHATSAPP_USE_REAL=false
WHATSAPP_USE_HTTP=true           # Recomendado para produção

# Configurações de Reconexão
WHATSAPP_MAX_CONNECTION_ATTEMPTS=3
WHATSAPP_RECONNECT_DELAY=60000
WHATSAPP_MAX_RECONNECT_ATTEMPTS=5

# ========================================
# CONFIGURAÇÕES DO WPPCONNECT SERVER
# ========================================

# Configurações Básicas do WPPConnect Server
WPPCONNECT_SERVER_URL=http://localhost:21465
WPPCONNECT_PORT=21465
WPPCONNECT_SECRET_KEY=your-secure-wppconnect-secret-here
WEBHOOK_URL=https://vereadorarafaela.com.br/api/webhook/whatsapp

# Configurações de Timeout
WPPCONNECT_REQUEST_TIMEOUT=30000
WPPCONNECT_HEALTH_CHECK_INTERVAL=60000

# ========================================
# CONFIGURAÇÕES DO RAG
# ========================================

# Configurações Básicas do RAG
RAG_ENABLED=true
RAG_CACHE_ENABLED=true
RAG_MAX_DOCUMENTS=500
RAG_CHUNK_SIZE=1000
RAG_CHUNK_OVERLAP=200

# ========================================
# CONFIGURAÇÕES DO GEMINI AI AVANÇADO
# ========================================

# API do Google Gemini
GEMINI_API_KEY=your-gemini-api-key-here
GEMINI_MODEL=gemini-1.5-pro
GEMINI_FALLBACK_MODEL=gemini-1.5-flash

# Configurações de Geração
GEMINI_TEMPERATURE=0.8
GEMINI_TOP_K=40
GEMINI_TOP_P=0.95
GEMINI_MAX_TOKENS=1024

# Configurações de Cache
GEMINI_CACHE_ENABLED=true
GEMINI_CACHE_SIZE=100
GEMINI_CACHE_TTL=3600000

# Configurações de Retry
GEMINI_MAX_RETRIES=3
GEMINI_RETRY_DELAY=1000
GEMINI_RETRY_BACKOFF=2.0

# Configurações de Timeout
GEMINI_REQUEST_TIMEOUT=30000
GEMINI_CONNECTION_TIMEOUT=10000

# ========================================
# CONFIGURAÇÕES DO SUPABASE COMPLETO
# ========================================

# Configurações Básicas do Supabase
SUPABASE_URL=your-supabase-project-url-here
SUPABASE_ANON_KEY=your-supabase-anon-key-here
SUPABASE_SERVICE_KEY=your-supabase-service-key-here

# Configurações de Conexão
SUPABASE_AUTO_REFRESH_TOKEN=true
SUPABASE_PERSIST_SESSION=true
SUPABASE_DETECT_SESSION_IN_URL=false

# Configurações de Realtime
SUPABASE_REALTIME_ENABLED=true
SUPABASE_EVENTS_PER_SECOND=10

# Configurações de Cache
SUPABASE_CACHE_ENABLED=true
SUPABASE_CACHE_SIZE=500
SUPABASE_CACHE_TTL=300000

# Configurações de Backup
SUPABASE_AUTO_BACKUP=true
SUPABASE_BACKUP_INTERVAL=86400000

# ========================================
# CONFIGURAÇÕES DE LOGGING (PRODUÇÃO)
# ========================================

# Configurações de Log
LOG_LEVEL=info
ENABLE_FILE_LOGGING=true
LOG_DIRECTORY=/var/log/vereadora-rafaela
LOG_MAX_FILE_SIZE=50m
LOG_MAX_FILES=10

# ========================================
# CONFIGURAÇÕES DE CACHE (PRODUÇÃO)
# ========================================

# Configurações de Cache
CACHE_ENABLED=true
CACHE_TTL=7200
CACHE_MAX_SIZE=5000

# ========================================
# CONFIGURAÇÕES DE MONITORAMENTO
# ========================================

# Configurações de Monitoramento
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true

# ========================================
# CONFIGURAÇÕES DE ANTI-BAN (PRODUÇÃO)
# ========================================

# Configurações de Anti-Ban
ANTIBAN_ENABLED=true
ANTIBAN_MIN_DELAY=2000
ANTIBAN_MAX_DELAY=8000
ANTIBAN_RISK_THRESHOLD=50

# ========================================
# CONFIGURAÇÕES DE BACKUP (PRODUÇÃO)
# ========================================

# Configurações de Backup
BACKUP_ENABLED=true
BACKUP_INTERVAL=43200000
BACKUP_MAX_FILES=14
BACKUP_DIRECTORY=/var/backups/vereadora-rafaela

# ========================================
# CONFIGURAÇÕES DE SESSÃO (PRODUÇÃO)
# ========================================

# Configurações de Sessão
SESSION_DIRECTORY=/var/lib/vereadora-rafaela/sessions
SESSION_AUTO_CLEANUP=true
SESSION_CLEANUP_INTERVAL=3600000
SESSION_MAX_AGE=**********
