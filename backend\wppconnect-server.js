import { spawn } from 'child_process';
import { EventEmitter } from 'events';
import axios from 'axios';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * WPPConnect Server Integrado
 * Gerencia o WPPConnect Server como parte do backend principal
 */
export default class WPPConnectServer extends EventEmitter {
  constructor() {
    super();
    
    // Configurações
    this.port = process.env.WPPCONNECT_PORT || 21465;
    this.secretKey = process.env.WPPCONNECT_SECRET_KEY || 'vereadora-rafaela-secret-2024';
    this.webhookUrl = process.env.WEBHOOK_URL || 'http://localhost:3001/api/webhook/whatsapp';
    this.sessionName = process.env.WHATSAPP_SESSION_NAME || 'vereadora-rafaela';
    
    // Estado
    this.isRunning = false;
    this.process = null;
    this.startAttempts = 0;
    this.maxStartAttempts = 3;
    
    // Diretórios
    this.dataDir = path.join(__dirname, 'wppconnect-data');
    this.sessionsDir = path.join(this.dataDir, 'sessions');
    this.tokensDir = path.join(this.dataDir, 'tokens');
    this.downloadsDir = path.join(this.dataDir, 'downloads');
    this.uploadsDir = path.join(this.dataDir, 'uploads');
  }

  async createDirectories() {
    const dirs = [
      this.dataDir,
      this.sessionsDir,
      this.tokensDir,
      this.downloadsDir,
      this.uploadsDir
    ];

    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
        console.log(`✅ Diretório criado/verificado: ${path.relative(__dirname, dir)}`);
      } catch (error) {
        console.warn(`⚠️ Erro ao criar diretório ${dir}:`, error.message);
      }
    }
  }

  async checkDockerAvailable() {
    try {
      const { spawn } = await import('child_process');
      return new Promise((resolve) => {
        const process = spawn('docker', ['--version'], { stdio: 'pipe' });
        process.on('close', (code) => resolve(code === 0));
        process.on('error', () => resolve(false));
      });
    } catch (error) {
      return false;
    }
  }

  async startWithDocker() {
    console.log('🐳 Iniciando WPPConnect Server com Docker...');
    
    // Criar diretórios necessários
    await this.createDirectories();
    
    const composeFile = path.join(__dirname, '..', 'docker-compose.wppconnect.yml');
    
    return new Promise((resolve, reject) => {
      this.process = spawn('docker-compose', [
        '-f', composeFile,
        'up', '-d'
      ], {
        stdio: 'pipe',
        cwd: path.dirname(composeFile)
      });

      let output = '';
      this.process.stdout?.on('data', (data) => {
        output += data.toString();
        console.log('📋 Docker:', data.toString().trim());
      });

      this.process.stderr?.on('data', (data) => {
        output += data.toString();
        console.error('❌ Docker Error:', data.toString().trim());
      });

      this.process.on('close', (code) => {
        if (code === 0) {
          this.isRunning = true;
          console.log('✅ WPPConnect Server iniciado com Docker');
          resolve();
        } else {
          console.error(`❌ Docker falhou com código: ${code}`);
          reject(new Error(`Docker process exited with code ${code}`));
        }
      });

      this.process.on('error', (error) => {
        console.error('❌ Erro ao iniciar Docker:', error);
        reject(error);
      });
    });
  }

  async startWithNPM() {
    console.log('📦 Iniciando WPPConnect com biblioteca direta...');

    // Verificar se @wppconnect-team/wppconnect está instalado
    try {
      await import('@wppconnect-team/wppconnect');
      console.log('✅ WPPConnect biblioteca encontrada');

      // Criar diretórios necessários
      await this.createDirectories();

      // Simular servidor HTTP básico para compatibilidade
      this.isRunning = true;
      console.log('✅ WPPConnect Server simulado iniciado');
      return Promise.resolve();

    } catch (error) {
      throw new Error('WPPConnect não está instalado. Execute: npm install @wppconnect-team/wppconnect');
    }
  }

  async start() {
    if (this.isRunning) {
      console.log('ℹ️ WPPConnect Server já está rodando');
      return;
    }

    this.startAttempts++;
    
    if (this.startAttempts > this.maxStartAttempts) {
      throw new Error('Máximo de tentativas de inicialização atingido');
    }

    try {
      // Tentar com Docker primeiro
      if (await this.checkDockerAvailable()) {
        await this.startWithDocker();
      } else {
        // Fallback para NPM
        console.log('⚠️ Docker não disponível, usando NPM...');
        await this.startWithNPM();
      }

      // Aguardar servidor ficar disponível
      await this.waitForServer();
      
      this.emit('started');
      console.log(`🚀 WPPConnect Server disponível em http://localhost:${this.port}`);
      
    } catch (error) {
      console.error('❌ Erro ao iniciar WPPConnect Server:', error.message);
      
      if (this.startAttempts < this.maxStartAttempts) {
        console.log(`🔄 Tentativa ${this.startAttempts + 1}/${this.maxStartAttempts} em 5 segundos...`);
        setTimeout(() => this.start(), 5000);
      } else {
        this.emit('error', error);
        throw error;
      }
    }
  }

  async waitForServer(timeout = 30000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await axios.get(`http://localhost:${this.port}/api/${this.sessionName}/status`, {
          timeout: 2000
        });
        
        if (response.status === 200) {
          console.log('✅ WPPConnect Server respondendo');
          return true;
        }
      } catch (error) {
        // Servidor ainda não está pronto
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    throw new Error('Timeout aguardando WPPConnect Server ficar disponível');
  }

  async stop() {
    if (!this.isRunning) {
      console.log('ℹ️ WPPConnect Server já está parado');
      return;
    }

    console.log('🛑 Parando WPPConnect Server...');

    if (this.process) {
      this.process.kill('SIGTERM');
      
      // Aguardar processo terminar
      await new Promise((resolve) => {
        this.process.on('close', resolve);
        setTimeout(resolve, 5000); // Timeout de 5s
      });
    }

    this.isRunning = false;
    this.process = null;
    this.emit('stopped');
    console.log('✅ WPPConnect Server parado');
  }

  async restart() {
    console.log('🔄 Reiniciando WPPConnect Server...');
    await this.stop();
    await new Promise(resolve => setTimeout(resolve, 2000));
    await this.start();
  }

  async getStatus() {
    try {
      const response = await axios.get(`http://localhost:${this.port}/api/${this.sessionName}/status`, {
        timeout: 5000
      });
      return response.data;
    } catch (error) {
      return { error: error.message, running: false };
    }
  }

  async healthCheck() {
    try {
      const status = await this.getStatus();
      return !status.error;
    } catch (error) {
      return false;
    }
  }
}
