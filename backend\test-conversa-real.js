#!/usr/bin/env node

/**
 * Teste de Conversa Real com Vereadora Rafaela
 */

async function testConversation() {
  console.log('🗣️ Testando conversa real com Vereadora Rafaela...\n');

  const queries = [
    'Olá Vereadora Rafaela! Como você está?',
    'Sou morador de Parnamirim e estou preocupado com a falta de iluminação na minha rua. Como você pode me ajudar?',
    'Minha filha precisa de uma vaga na creche municipal. Qual o procedimento?',
    'Obrigado pela ajuda, Vereadora! Você é muito atenciosa.'
  ];

  for (let i = 0; i < queries.length; i++) {
    const query = queries[i];
    
    console.log(`\n💬 Pergunta ${i + 1}:`);
    console.log(`"${query}"`);
    console.log('\n🤖 Vereadora Rafaela responde:');
    
    try {
      const response = await fetch('http://localhost:3002/api/test-gemini', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      
      console.log(`"${data.response}"`);
      console.log(`\n⏱️ Tempo de resposta: ${data.metadata.processingTime}ms`);
      console.log(`🧠 Modo: ${data.metadata.mode}`);
      
      // Pausa entre perguntas
      if (i < queries.length - 1) {
        console.log('\n' + '='.repeat(80));
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } catch (error) {
      console.error(`❌ Erro: ${error.message}`);
    }
  }

  console.log('\n🎉 Teste de conversa concluído!');
}

testConversation();
