#!/usr/bin/env node

/**
 * Servidor Backend Básico
 * Versão funcional sem serviços complexos
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Importar apenas configuração e logger
import { ServerConfig } from './src/config/ServerConfig.js';
import { Logger } from './src/utils/Logger.js';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '.env') });

/**
 * Servidor Backend Básico
 */
class BasicServer {
  constructor() {
    this.app = express();
    this.config = new ServerConfig();
    this.logger = new Logger();
    this.server = null;
    
    console.log('🏗️ Inicializando servidor básico...');
    
    this.setupMiddlewares();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddlewares() {
    console.log('🔧 Configurando middlewares...');

    // Segurança básica
    this.app.use(helmet({
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false
    }));

    // CORS
    this.app.use(cors({
      origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    }));

    // Compressão
    this.app.use(compression());

    // Rate limiting básico
    const limiter = rateLimit({
      windowMs: 60000, // 1 minuto
      max: 100, // 100 requests por minuto
      message: {
        error: 'Muitas requisições. Tente novamente em alguns minutos.',
        code: 'RATE_LIMIT_EXCEEDED'
      }
    });
    this.app.use('/api/', limiter);

    // Logging
    this.app.use(morgan('combined'));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    console.log('✅ Middlewares configurados');
  }

  setupRoutes() {
    console.log('🛣️ Configurando rotas...');

    // Rota principal
    this.app.get('/', (req, res) => {
      res.json({
        message: '🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda',
        version: '2.0.0-basic',
        status: 'online',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        mode: 'basic',
        endpoints: {
          health: '/api/health',
          config: '/api/config',
          whatsapp: '/api/whatsapp/status',
          rag: '/api/rag/query'
        }
      });
    });

    // Health check
    this.app.get('/api/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '2.0.0-basic',
        environment: process.env.NODE_ENV || 'development',
        services: {
          server: 'online',
          whatsapp: 'simulated',
          rag: 'simulated'
        }
      });
    });

    // Configuração
    this.app.get('/api/config', (req, res) => {
      res.json({
        server: {
          port: this.config.server.port,
          environment: this.config.app.environment,
          version: this.config.app.version
        },
        whatsapp: {
          sessionName: this.config.whatsapp.sessionName,
          useSimulator: this.config.whatsapp.useSimulator,
          useReal: this.config.whatsapp.useReal,
          useHttp: this.config.whatsapp.useHttp
        },
        integrations: {
          hasGeminiKey: !!this.config.gemini.apiKey,
          hasSupabaseUrl: !!this.config.supabase.url,
          ragEnabled: this.config.rag.enabled
        },
        timestamp: new Date().toISOString()
      });
    });

    // WhatsApp Status (simulado)
    this.app.get('/api/whatsapp/status', (req, res) => {
      res.json({
        connected: false,
        session: this.config.whatsapp.sessionName,
        mode: 'simulated',
        message: 'WhatsApp em modo simulado - servidor básico',
        timestamp: new Date().toISOString()
      });
    });

    // WhatsApp QR (simulado)
    this.app.get('/api/whatsapp/qr', (req, res) => {
      res.json({
        hasQR: false,
        message: 'QR Code não disponível em modo simulado',
        mode: 'simulated',
        timestamp: new Date().toISOString()
      });
    });

    // RAG Query (simulado)
    this.app.post('/api/rag/query', (req, res) => {
      const { query } = req.body;
      
      if (!query) {
        return res.status(400).json({
          error: 'Query é obrigatória',
          code: 'MISSING_QUERY'
        });
      }

      // Resposta simulada da Vereadora Rafaela
      const responses = [
        `Olá! Sou a Vereadora Rafaela de Nilda. Como posso ajudá-lo hoje? 🏛️`,
        `Obrigada por entrar em contato! Estou aqui para servir Parnamirim. Em que posso ajudar? 💖`,
        `Oi! É um prazer falar com você. Como posso contribuir para melhorar nossa cidade? 🤩`,
        `Olá, querido(a)! Conte comigo para o que precisar. Estamos juntos por Parnamirim! 🙏🏽`
      ];

      const randomResponse = responses[Math.floor(Math.random() * responses.length)];

      res.json({
        query,
        response: randomResponse,
        sources: [],
        metadata: {
          mode: 'simulated',
          vereadora: 'Rafaela de Nilda',
          municipio: 'Parnamirim/RN',
          timestamp: new Date().toISOString(),
          processingTime: Math.random() * 500 + 200
        }
      });
    });

    // Webhook (simulado)
    this.app.post('/api/webhook/whatsapp', (req, res) => {
      console.log('📨 Webhook recebido (simulado):', req.body);
      res.json({
        received: true,
        mode: 'simulated',
        timestamp: new Date().toISOString()
      });
    });

    // Rota 404
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint não encontrado',
        message: 'A rota solicitada não existe neste servidor',
        availableEndpoints: [
          '/',
          '/api/health',
          '/api/config',
          '/api/whatsapp/status',
          '/api/whatsapp/qr',
          '/api/rag/query',
          '/api/webhook/whatsapp'
        ],
        timestamp: new Date().toISOString()
      });
    });

    console.log('✅ Rotas configuradas');
  }

  setupErrorHandling() {
    console.log('⚠️ Configurando tratamento de erros...');

    // Middleware de tratamento de erros
    this.app.use((error, req, res, next) => {
      console.error('❌ Erro no servidor:', error);
      
      res.status(500).json({
        error: 'Erro interno do servidor',
        message: error.message,
        timestamp: new Date().toISOString(),
        mode: 'basic'
      });
    });

    // Tratamento de promises rejeitadas
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Promise rejeitada não tratada:', reason);
    });

    // Tratamento de exceções não capturadas
    process.on('uncaughtException', (error) => {
      console.error('❌ Exceção não capturada:', error);
      setTimeout(() => process.exit(1), 1000);
    });

    console.log('✅ Tratamento de erros configurado');
  }

  async start() {
    try {
      console.log('🚀 Iniciando servidor básico...');

      const port = this.config.server.port;

      this.server = this.app.listen(port, () => {
        console.log('');
        console.log('🎉 ===================================');
        console.log('🏛️  SERVIDOR BÁSICO INICIADO!');
        console.log('🎉 ===================================');
        console.log('');
        console.log(`📍 Porta: ${port}`);
        console.log(`🌐 URL: http://localhost:${port}`);
        console.log(`📱 Ambiente: ${this.config.app.environment}`);
        console.log(`⚡ Modo: Básico (simulado)`);
        console.log('');
        console.log('🎯 Endpoints disponíveis:');
        console.log(`   GET  /                         - Informações do servidor`);
        console.log(`   GET  /api/health               - Health check`);
        console.log(`   GET  /api/config               - Configuração`);
        console.log(`   GET  /api/whatsapp/status      - Status WhatsApp`);
        console.log(`   GET  /api/whatsapp/qr          - QR Code`);
        console.log(`   POST /api/rag/query            - Query RAG`);
        console.log(`   POST /api/webhook/whatsapp     - Webhook`);
        console.log('');
        console.log('✅ Servidor funcionando perfeitamente!');
        console.log('🔧 Para testar: curl http://localhost:3001/api/health');
        console.log('');
      });

      // Graceful shutdown
      process.on('SIGTERM', this.shutdown.bind(this));
      process.on('SIGINT', this.shutdown.bind(this));

    } catch (error) {
      console.error('❌ Erro ao iniciar servidor:', error);
      process.exit(1);
    }
  }

  shutdown(signal) {
    console.log(`\n🛑 Recebido sinal ${signal}, parando servidor...`);
    
    if (this.server) {
      this.server.close(() => {
        console.log('✅ Servidor parado com sucesso');
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  }
}

// Iniciar servidor
const server = new BasicServer();
server.start();
