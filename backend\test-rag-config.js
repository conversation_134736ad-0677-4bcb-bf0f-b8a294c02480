#!/usr/bin/env node

/**
 * Script de Teste para Configuração RAG
 * Testa Gemini AI e Supabase
 */

import dotenv from 'dotenv';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { createClient } from '@supabase/supabase-js';

// Carregar variáveis de ambiente
dotenv.config();

console.log('🧪 Testando Configuração RAG...\n');

async function testGemini() {
  console.log('🧠 Testando Gemini AI...');
  
  const apiKey = process.env.GEMINI_API_KEY;
  
  if (!apiKey) {
    console.log('❌ GEMINI_API_KEY não configurada');
    return false;
  }
  
  try {
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const prompt = `Você é a Vereadora Rafaela de Ni<PERSON>/RN. 
    Responda de forma calorosa: "Ol<PERSON>, como posso ajudar nossa cidade?"`;
    
    console.log('🔄 Enviando prompt de teste...');
    const result = await model.generateContent(prompt);
    const response = result.response.text();
    
    console.log('✅ Gemini AI funcionando!');
    console.log('📝 Resposta:', response.substring(0, 100) + '...');
    return true;
    
  } catch (error) {
    console.log('❌ Erro no Gemini:', error.message);
    return false;
  }
}

async function testSupabase() {
  console.log('\n🗄️ Testando Supabase...');
  
  const url = process.env.SUPABASE_URL;
  const key = process.env.SUPABASE_ANON_KEY;
  
  if (!url || !key) {
    console.log('❌ SUPABASE_URL ou SUPABASE_ANON_KEY não configuradas');
    return false;
  }
  
  try {
    const supabase = createClient(url, key);
    
    console.log('🔄 Testando conexão...');
    
    // Testar conexão básica
    const { data, error } = await supabase
      .from('documents')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ Erro na consulta:', error.message);
      return false;
    }
    
    console.log('✅ Supabase funcionando!');
    console.log('📊 Conexão estabelecida com sucesso');
    return true;
    
  } catch (error) {
    console.log('❌ Erro no Supabase:', error.message);
    return false;
  }
}

async function testRAGEndpoint() {
  console.log('\n🎯 Testando Endpoint RAG...');
  
  try {
    const response = await fetch('http://localhost:3001/api/rag/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: 'Olá Vereadora Rafaela, como você pode ajudar Parnamirim?'
      })
    });
    
    if (!response.ok) {
      console.log('❌ Servidor não está rodando ou endpoint com erro');
      return false;
    }
    
    const data = await response.json();
    
    console.log('✅ Endpoint RAG funcionando!');
    console.log('📝 Modo:', data.metadata?.mode || 'desconhecido');
    console.log('📝 Resposta:', data.response?.substring(0, 100) + '...');
    return true;
    
  } catch (error) {
    console.log('❌ Erro ao testar endpoint:', error.message);
    console.log('💡 Certifique-se de que o servidor está rodando: node server-enhanced.js');
    return false;
  }
}

async function runTests() {
  console.log('🔍 Verificando configurações...\n');
  
  // Verificar variáveis de ambiente
  console.log('📋 Variáveis de ambiente:');
  console.log(`   GEMINI_API_KEY: ${process.env.GEMINI_API_KEY ? '✅ Configurada' : '❌ Não configurada'}`);
  console.log(`   SUPABASE_URL: ${process.env.SUPABASE_URL ? '✅ Configurada' : '❌ Não configurada'}`);
  console.log(`   SUPABASE_ANON_KEY: ${process.env.SUPABASE_ANON_KEY ? '✅ Configurada' : '❌ Não configurada'}`);
  console.log('');
  
  // Executar testes
  const results = {
    gemini: await testGemini(),
    supabase: await testSupabase(),
    endpoint: await testRAGEndpoint()
  };
  
  // Resumo
  console.log('\n📊 RESUMO DOS TESTES:');
  console.log('========================');
  console.log(`🧠 Gemini AI: ${results.gemini ? '✅ OK' : '❌ FALHOU'}`);
  console.log(`🗄️ Supabase: ${results.supabase ? '✅ OK' : '❌ FALHOU'}`);
  console.log(`🎯 Endpoint RAG: ${results.endpoint ? '✅ OK' : '❌ FALHOU'}`);
  console.log('');
  
  // Recomendações
  if (!results.gemini && !results.supabase) {
    console.log('🔧 PRÓXIMOS PASSOS:');
    console.log('1. Configure o Gemini AI (veja CONFIGURAR_RAG.md)');
    console.log('2. Configure o Supabase (veja CONFIGURAR_RAG.md)');
    console.log('3. Execute novamente este teste');
  } else if (!results.gemini) {
    console.log('🔧 PRÓXIMO PASSO:');
    console.log('1. Configure o Gemini AI (veja CONFIGURAR_RAG.md)');
  } else if (!results.supabase) {
    console.log('🔧 PRÓXIMO PASSO:');
    console.log('1. Configure o Supabase (veja CONFIGURAR_RAG.md)');
  } else if (results.gemini && results.supabase) {
    console.log('🎉 PARABÉNS!');
    console.log('✅ Sistema RAG completamente configurado!');
    console.log('🚀 Pronto para usar com IA real e banco de dados!');
  }
  
  if (!results.endpoint) {
    console.log('\n💡 Para testar o endpoint:');
    console.log('1. Execute: node server-enhanced.js');
    console.log('2. Execute novamente: node test-rag-config.js');
  }
  
  console.log('');
}

runTests().catch(console.error);
