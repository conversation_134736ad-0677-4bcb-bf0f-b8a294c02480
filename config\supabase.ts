import { createClient, SupabaseClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Verificar se as variáveis estão configuradas
export const isSupabaseConfigured = !!(supabaseUrl && supabaseAnonKey);

// Configurações do cliente Supabase
const supabaseConfig = {
  auth: {
    persistSession: false,
    autoRefreshToken: true,
    detectSessionInUrl: false
  },
  db: {
    schema: 'public'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  },
  global: {
    headers: {
      'X-Client-Info': 'vereadora-rafaela-rag-system'
    }
  }
};

// Criar cliente apenas se configurado, senão usar null
export const supabase: SupabaseClient | null = isSupabaseConfigured
  ? createClient(supabaseUrl, supabaseAnonKey, supabaseConfig)
  : null;

// Configurações específicas para o projeto
export const SUPABASE_CONFIG = {
  tables: {
    conversations: 'conversations',
    messages: 'messages',
    documents: 'documents',
    document_chunks: 'document_chunks',
    interaction_logs: 'interaction_logs'
  },
  storage: {
    documents: 'documents'
  },
  functions: {
    searchSimilarChunks: 'search_similar_chunks',
    generateSummary: 'generate_summary'
  },
  channels: {
    conversations: 'conversations_channel',
    documents: 'documents_channel'
  }
} as const;

// Tipos para TypeScript
export interface Database {
  public: {
    Tables: {
      conversations: {
        Row: {
          id: string;
          title: string;
          created_at: string;
          updated_at: string;
          message_count: number;
        };
        Insert: {
          id?: string;
          title: string;
          created_at?: string;
          updated_at?: string;
          message_count?: number;
        };
        Update: {
          id?: string;
          title?: string;
          created_at?: string;
          updated_at?: string;
          message_count?: number;
        };
      };
      messages: {
        Row: {
          id: string;
          conversation_id: string;
          content: string;
          sender: 'user' | 'assistant';
          timestamp: string;
          sources: any;
          confidence: number;
        };
        Insert: {
          id: string;
          conversation_id: string;
          content: string;
          sender: 'user' | 'assistant';
          timestamp?: string;
          sources?: any;
          confidence?: number;
        };
        Update: {
          id?: string;
          conversation_id?: string;
          content?: string;
          sender?: 'user' | 'assistant';
          timestamp?: string;
          sources?: any;
          confidence?: number;
        };
      };
      documents: {
        Row: {
          id: string;
          name: string;
          type: string;
          size: number;
          upload_date: string;
          status: 'processing' | 'ready' | 'error';
          chunks_count: number;
        };
        Insert: {
          id?: string;
          name: string;
          type: string;
          size: number;
          upload_date?: string;
          status?: 'processing' | 'ready' | 'error';
          chunks_count?: number;
        };
        Update: {
          id?: string;
          name?: string;
          type?: string;
          size?: number;
          upload_date?: string;
          status?: 'processing' | 'ready' | 'error';
          chunks_count?: number;
        };
      };
      document_chunks: {
        Row: {
          id: string;
          document_id: string;
          content: string;
          chunk_index: number;
          embedding: number[];
          metadata: any;
          created_at: string;
        };
        Insert: {
          id?: string;
          document_id: string;
          content: string;
          chunk_index: number;
          embedding?: number[];
          metadata?: any;
          created_at?: string;
        };
        Update: {
          id?: string;
          document_id?: string;
          content?: string;
          chunk_index?: number;
          embedding?: number[];
          metadata?: any;
          created_at?: string;
        };
      };
      interaction_logs: {
        Row: {
          id: string;
          type: string;
          timestamp: string;
          metadata: any;
        };
        Insert: {
          id?: string;
          type: string;
          timestamp?: string;
          metadata?: any;
        };
        Update: {
          id?: string;
          type?: string;
          timestamp?: string;
          metadata?: any;
        };
      };
    };
  };
}

// Utilitários para Supabase
export const supabaseUtils = {
  // Verificar conexão
  async testConnection(): Promise<boolean> {
    if (!supabase) return false;

    try {
      const { error } = await supabase.from('conversations').select('count').limit(1);
      return !error;
    } catch {
      return false;
    }
  },

  // Obter estatísticas do banco
  async getStats() {
    if (!supabase) return null;

    try {
      const [conversations, messages, documents] = await Promise.all([
        supabase.from('conversations').select('count'),
        supabase.from('messages').select('count'),
        supabase.from('documents').select('count')
      ]);

      return {
        conversations: conversations.count || 0,
        messages: messages.count || 0,
        documents: documents.count || 0,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas:', error);
      return null;
    }
  },

  // Limpar dados antigos
  async cleanup(daysOld: number = 30) {
    if (!supabase) return false;

    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      await supabase
        .from('interaction_logs')
        .delete()
        .lt('timestamp', cutoffDate.toISOString());

      return true;
    } catch (error) {
      console.error('Erro na limpeza:', error);
      return false;
    }
  }
};