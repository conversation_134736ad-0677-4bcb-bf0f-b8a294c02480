#!/bin/bash

# Script para instalar dependências do Supabase e Gemini
# Execute com: chmod +x scripts/install-dependencies.sh && ./scripts/install-dependencies.sh

echo "🏛️ Instalando dependências para Supabase e Gemini..."
echo "=================================================="

# Verificar se npm está instalado
if ! command -v npm &> /dev/null; then
    echo "❌ npm não encontrado. Instale Node.js primeiro."
    exit 1
fi

echo "📦 Instalando dependências do Supabase..."

# Instalar Supabase
npm install @supabase/supabase-js

echo "✅ Supabase instalado"

echo "🤖 Instalando dependências do Gemini..."

# Instalar Google Generative AI
npm install @google/generative-ai

echo "✅ Gemini AI instalado"

echo "🔧 Instalando dependências adicionais..."

# Instalar dependências para TypeScript (se necessário)
npm install --save-dev @types/node tsx

echo "✅ Dependências adicionais instaladas"

echo ""
echo "🎉 Todas as dependências foram instaladas com sucesso!"
echo ""
echo "📋 Próximos passos:"
echo "1. Configure o arquivo .env com suas credenciais"
echo "2. Execute: npm run dev"
echo "3. Verifique o status na interface"
echo ""
echo "📖 Para mais informações, consulte: docs/SETUP_SUPABASE_GEMINI.md"
