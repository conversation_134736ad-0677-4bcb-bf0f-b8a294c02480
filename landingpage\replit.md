# Project Overview

This is a full-stack web application built with React, TypeScript, and Express.js. It features a modern frontend using shadcn/ui components and a PostgreSQL database with Drizzle ORM for data management.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack React Query for server state management
- **Routing**: Wouter for lightweight client-side routing
- **Form Handling**: React Hook Form with Zod validation

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Session-based authentication with bcrypt password hashing
- **API**: RESTful API structure
- **Environment**: Node.js with ES modules

## Key Components

### Database Schema
Located in `shared/schema.ts`:
- **Users Table**: Stores user credentials (email, password) with timestamps
- **Sessions Table**: Manages user sessions with tokens and expiration
- **Validation**: Zod schemas for type-safe data validation

### Authentication System
- Session-based authentication with HTTP-only cookies
- Password hashing using bcrypt
- Session management with configurable expiration (1 day default, 7 days for "remember me")
- Protected routes and middleware

### UI Components
- Complete shadcn/ui component library implementation
- Custom styling with Tailwind CSS variables
- Responsive design with mobile-first approach
- Dark mode support built-in

### API Structure
- RESTful endpoints under `/api` prefix
- Authentication endpoints (`/api/auth/login`, `/api/auth/logout`, `/api/auth/me`)
- Error handling middleware
- Request/response logging

## Data Flow

1. **Client Authentication**: User submits login credentials via React Hook Form
2. **Server Validation**: Express validates credentials against PostgreSQL database
3. **Session Creation**: Server creates session token and sets HTTP-only cookie
4. **State Management**: React Query manages authentication state and API calls
5. **Protected Routes**: Authentication context provides user state across components

## External Dependencies

### Key Libraries
- **@neondatabase/serverless**: PostgreSQL database connection
- **@radix-ui/react-**: Headless UI components for accessibility
- **@tanstack/react-query**: Server state management
- **drizzle-orm**: Type-safe database ORM
- **bcrypt**: Password hashing
- **zod**: Schema validation

### Development Tools
- **Vite**: Development server and build tool
- **TypeScript**: Static type checking
- **ESLint**: Code linting (configured via package.json)
- **PostCSS**: CSS processing with Tailwind

## Deployment Strategy

### Build Process
- Frontend: Vite builds to `dist/public` directory
- Backend: ESBuild bundles server to `dist/index.js`
- Database: Drizzle migrations in `migrations/` directory

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string (required)
- `NODE_ENV`: Environment flag (development/production)

### Scripts
- `dev`: Development server with hot reload
- `build`: Production build for both frontend and backend
- `start`: Production server
- `db:push`: Push database schema changes

## User Preferences

Preferred communication style: Simple, everyday language.

## Current Status

A página de login segura está completa e funcional com:
- Sistema de autenticação com email/senha
- Interface em português
- Demonstração funcional com usuário de teste
- Pronto para integração com Supabase

### Usuário de Teste (Demonstração)
- **Email**: <EMAIL>
- **Senha**: 123456

## Próximos Passos para Integração Supabase

1. **Configurar DATABASE_URL**: Adicionar a URL de conexão do Supabase nos secrets
2. **Atualizar storage.ts**: Substituir implementação em memória pela conexão real
3. **Executar migrações**: Usar `npm run db:push` para criar as tabelas
4. **Testar conexão**: Verificar se a autenticação funciona com o banco real

## Páginas Disponíveis

- `/` - Página inicial (requer login)
- `/login` - Página de login
- `/instructions` - Instruções e detalhes técnicos

## Changelog

- July 03, 2025: Sistema de login seguro implementado com demonstração funcional
- July 03, 2025: Criadas páginas de instruções e painel do usuário
- July 03, 2025: Configuração para integração com Supabase preparada