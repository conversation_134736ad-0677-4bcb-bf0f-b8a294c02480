import NodeCache from 'node-cache';

export const cache = new NodeCache({ stdTTL: 600 }); // TTL de 10 minutos

// File: /home/<USER>/project/src/services/RAGService.js
import { cache } from '../utils/cache.js';

export class RAGService {
  async generateResponse(query) {
    const cacheKey = `rag_${query}`;
    const cachedResult = cache.get(cacheKey);
    
    if (cachedResult) {
      return cachedResult;
    }
    
    // Gerar resposta
    const response = await this.actuallyGenerateResponse(query);
    
    cache.set(cacheKey, response);
    return response;
  }
  
  async actuallyGenerateResponse(query) {
    // L&oacute;gica original de gera&ccedil;&atilde;o de resposta
  }
}