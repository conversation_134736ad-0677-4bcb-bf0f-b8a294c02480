import { Logger } from '../utils/Logger.js';

export class HumanBehaviorService {
  constructor() {
    this.logger = new Logger();
    
    // Configurações de comportamento humano
    this.config = {
      // Delays entre ações (em ms)
      typing: {
        min: 1000,      // 1 segundo mínimo
        max: 5000,      // 5 segundos máximo
        perChar: 50,    // 50ms por caractere
        variation: 0.3  // 30% de variação
      },
      
      // Delays entre mensagens
      messaging: {
        min: 2000,      // 2 segundos mínimo
        max: 15000,     // 15 segundos máximo
        variation: 0.4  // 40% de variação
      },
      
      // Limites de mensagens
      limits: {
        perMinute: 8,       // Máximo 8 mensagens por minuto
        perHour: 120,       // Máximo 120 mensagens por hora
        perDay: 1000,       // Máximo 1000 mensagens por dia
        burstLimit: 3,      // Máximo 3 mensagens em sequência
        burstCooldown: 30000 // 30 segundos de cooldown após burst
      },
      
      // Padrões de atividade
      activity: {
        workHours: {
          start: 8,   // 8:00
          end: 18     // 18:00
        },
        lunchBreak: {
          start: 12,  // 12:00
          end: 13     // 13:00
        },
        weekends: {
          reduced: true,
          factor: 0.3  // 30% da atividade normal
        }
      },
      
      // Variações de comportamento
      variations: {
        readReceipts: 0.95,     // 95% chance de marcar como lida
        onlineStatus: 0.8,      // 80% chance de aparecer online
        typingIndicator: 0.9,   // 90% chance de mostrar digitando
        responseDelay: 0.7      // 70% chance de delay adicional
      }
    };
    
    // Estado atual
    this.state = {
      messageCount: {
        minute: 0,
        hour: 0,
        day: 0,
        lastReset: {
          minute: Date.now(),
          hour: Date.now(),
          day: Date.now()
        }
      },
      lastActivity: null,
      burstCount: 0,
      lastBurst: null,
      isInCooldown: false,
      currentSession: {
        startTime: Date.now(),
        messagesCount: 0,
        avgResponseTime: 0
      }
    };
    
    // Histórico de comportamento
    this.behaviorHistory = [];
    this.suspiciousPatterns = [];
    
    this.logger.info('🤖 Human Behavior Service inicializado');
  }

  // Calcular delay de digitação baseado no tamanho da mensagem
  calculateTypingDelay(message) {
    const baseDelay = this.config.typing.min;
    const charDelay = message.length * this.config.typing.perChar;
    const variation = this.addRandomVariation(charDelay, this.config.typing.variation);
    
    const totalDelay = Math.min(
      baseDelay + variation,
      this.config.typing.max
    );
    
    this.logger.debug(`⌨️ Typing delay calculado: ${totalDelay}ms para ${message.length} caracteres`);
    return totalDelay;
  }

  // Calcular delay entre mensagens
  calculateMessageDelay() {
    const baseDelay = this.config.messaging.min;
    const randomDelay = Math.random() * (this.config.messaging.max - this.config.messaging.min);
    const variation = this.addRandomVariation(randomDelay, this.config.messaging.variation);
    
    // Adicionar delay extra se estiver em horário de pico
    const hourMultiplier = this.getHourMultiplier();
    const finalDelay = (baseDelay + variation) * hourMultiplier;
    
    this.logger.debug(`💬 Message delay calculado: ${finalDelay}ms`);
    return finalDelay;
  }

  // Adicionar variação aleatória natural
  addRandomVariation(value, variationPercent) {
    const variation = value * variationPercent;
    const randomFactor = (Math.random() - 0.5) * 2; // -1 a 1
    return value + (variation * randomFactor);
  }

  // Obter multiplicador baseado no horário
  getHourMultiplier() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = domingo, 6 = sábado
    
    // Fins de semana
    if (day === 0 || day === 6) {
      return this.config.activity.weekends.reduced ? 
        (1 + this.config.activity.weekends.factor) : 1;
    }
    
    // Horário de almoço
    if (hour >= this.config.activity.lunchBreak.start && 
        hour < this.config.activity.lunchBreak.end) {
      return 1.5; // 50% mais lento
    }
    
    // Fora do horário comercial
    if (hour < this.config.activity.workHours.start || 
        hour >= this.config.activity.workHours.end) {
      return 1.3; // 30% mais lento
    }
    
    return 1; // Horário normal
  }

  // Verificar se pode enviar mensagem (rate limiting humano)
  canSendMessage() {
    this.updateMessageCounts();
    
    // Verificar limites
    if (this.state.messageCount.minute >= this.config.limits.perMinute) {
      this.logger.warn('⚠️ Limite por minuto atingido');
      return { allowed: false, reason: 'minute_limit', waitTime: this.getWaitTime('minute') };
    }
    
    if (this.state.messageCount.hour >= this.config.limits.perHour) {
      this.logger.warn('⚠️ Limite por hora atingido');
      return { allowed: false, reason: 'hour_limit', waitTime: this.getWaitTime('hour') };
    }
    
    if (this.state.messageCount.day >= this.config.limits.perDay) {
      this.logger.warn('⚠️ Limite diário atingido');
      return { allowed: false, reason: 'day_limit', waitTime: this.getWaitTime('day') };
    }
    
    // Verificar burst
    if (this.state.burstCount >= this.config.limits.burstLimit) {
      const timeSinceLastBurst = Date.now() - this.state.lastBurst;
      if (timeSinceLastBurst < this.config.limits.burstCooldown) {
        this.logger.warn('⚠️ Burst cooldown ativo');
        return { 
          allowed: false, 
          reason: 'burst_cooldown', 
          waitTime: this.config.limits.burstCooldown - timeSinceLastBurst 
        };
      } else {
        // Reset burst count
        this.state.burstCount = 0;
      }
    }
    
    return { allowed: true };
  }

  // Registrar envio de mensagem
  registerMessageSent(message) {
    const now = Date.now();
    
    // Atualizar contadores
    this.state.messageCount.minute++;
    this.state.messageCount.hour++;
    this.state.messageCount.day++;
    this.state.currentSession.messagesCount++;
    
    // Verificar burst
    if (this.state.lastActivity && (now - this.state.lastActivity) < 5000) {
      this.state.burstCount++;
      this.state.lastBurst = now;
    } else {
      this.state.burstCount = 1;
    }
    
    this.state.lastActivity = now;
    
    // Registrar no histórico
    this.behaviorHistory.push({
      timestamp: now,
      type: 'message_sent',
      messageLength: message.length,
      burstCount: this.state.burstCount,
      timeSinceLastMessage: this.state.lastActivity ? now - this.state.lastActivity : 0
    });
    
    // Manter apenas últimas 1000 entradas
    if (this.behaviorHistory.length > 1000) {
      this.behaviorHistory = this.behaviorHistory.slice(-1000);
    }
    
    this.logger.debug(`📊 Mensagem registrada: burst=${this.state.burstCount}, total_minuto=${this.state.messageCount.minute}`);
  }

  // Atualizar contadores de mensagens
  updateMessageCounts() {
    const now = Date.now();
    
    // Reset contador por minuto
    if (now - this.state.messageCount.lastReset.minute >= 60000) {
      this.state.messageCount.minute = 0;
      this.state.messageCount.lastReset.minute = now;
    }
    
    // Reset contador por hora
    if (now - this.state.messageCount.lastReset.hour >= 3600000) {
      this.state.messageCount.hour = 0;
      this.state.messageCount.lastReset.hour = now;
    }
    
    // Reset contador diário
    if (now - this.state.messageCount.lastReset.day >= 86400000) {
      this.state.messageCount.day = 0;
      this.state.messageCount.lastReset.day = now;
    }
  }

  // Calcular tempo de espera
  getWaitTime(type) {
    const now = Date.now();
    
    switch (type) {
      case 'minute':
        return 60000 - (now - this.state.messageCount.lastReset.minute);
      case 'hour':
        return 3600000 - (now - this.state.messageCount.lastReset.hour);
      case 'day':
        return 86400000 - (now - this.state.messageCount.lastReset.day);
      default:
        return 0;
    }
  }

  // Simular comportamento de leitura
  async simulateReadBehavior(client, chatId) {
    try {
      // Chance de marcar como lida
      if (Math.random() < this.config.variations.readReceipts) {
        // Delay antes de marcar como lida (1-10 segundos)
        const readDelay = 1000 + Math.random() * 9000;
        await this.sleep(readDelay);
        
        await client.sendSeen(chatId);
        this.logger.debug(`👁️ Mensagem marcada como lida após ${readDelay}ms`);
      }
    } catch (error) {
      this.logger.warn('⚠️ Erro ao simular leitura:', error.message);
    }
  }

  // Simular indicador de digitação
  async simulateTypingIndicator(client, chatId, duration) {
    try {
      if (Math.random() < this.config.variations.typingIndicator) {
        await client.startTyping(chatId);
        this.logger.debug(`⌨️ Indicador de digitação iniciado por ${duration}ms`);
        
        // Manter digitando por um tempo
        setTimeout(async () => {
          try {
            await client.stopTyping(chatId);
            this.logger.debug('⌨️ Indicador de digitação parado');
          } catch (error) {
            this.logger.warn('⚠️ Erro ao parar digitação:', error.message);
          }
        }, duration);
      }
    } catch (error) {
      this.logger.warn('⚠️ Erro ao simular digitação:', error.message);
    }
  }

  // Simular presença online
  async simulateOnlinePresence(client) {
    try {
      if (Math.random() < this.config.variations.onlineStatus) {
        await client.setPresence('available');
        this.logger.debug('🟢 Status definido como online');
        
        // Ficar online por 5-30 minutos
        const onlineTime = 300000 + Math.random() * 1500000;
        setTimeout(async () => {
          try {
            await client.setPresence('unavailable');
            this.logger.debug('⚫ Status definido como offline');
          } catch (error) {
            this.logger.warn('⚠️ Erro ao definir status offline:', error.message);
          }
        }, onlineTime);
      }
    } catch (error) {
      this.logger.warn('⚠️ Erro ao simular presença:', error.message);
    }
  }

  // Detectar padrões suspeitos
  detectSuspiciousPatterns() {
    const recentMessages = this.behaviorHistory.slice(-50); // Últimas 50 mensagens
    const patterns = [];
    
    // Padrão 1: Mensagens muito regulares
    const intervals = recentMessages.map((msg, index) => {
      if (index === 0) return 0;
      return msg.timestamp - recentMessages[index - 1].timestamp;
    }).filter(interval => interval > 0);
    
    if (intervals.length > 10) {
      const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
      const variance = intervals.reduce((sum, interval) => {
        return sum + Math.pow(interval - avgInterval, 2);
      }, 0) / intervals.length;
      
      const standardDeviation = Math.sqrt(variance);
      const coefficientOfVariation = standardDeviation / avgInterval;
      
      if (coefficientOfVariation < 0.2) { // Muito regular
        patterns.push({
          type: 'regular_intervals',
          severity: 'medium',
          description: 'Intervalos entre mensagens muito regulares',
          coefficient: coefficientOfVariation
        });
      }
    }
    
    // Padrão 2: Muitas mensagens em burst
    const burstCount = recentMessages.filter(msg => msg.burstCount > 1).length;
    if (burstCount > recentMessages.length * 0.7) {
      patterns.push({
        type: 'excessive_bursts',
        severity: 'high',
        description: 'Muitas mensagens em sequência rápida',
        percentage: (burstCount / recentMessages.length) * 100
      });
    }
    
    // Padrão 3: Atividade fora do horário normal
    const offHoursMessages = recentMessages.filter(msg => {
      const hour = new Date(msg.timestamp).getHours();
      return hour < 6 || hour > 23;
    }).length;
    
    if (offHoursMessages > recentMessages.length * 0.5) {
      patterns.push({
        type: 'off_hours_activity',
        severity: 'medium',
        description: 'Muita atividade fora do horário normal',
        percentage: (offHoursMessages / recentMessages.length) * 100
      });
    }
    
    // Atualizar lista de padrões suspeitos
    this.suspiciousPatterns = patterns;
    
    if (patterns.length > 0) {
      this.logger.warn(`🚨 ${patterns.length} padrões suspeitos detectados:`, patterns);
    }
    
    return patterns;
  }

  // Aplicar delay inteligente baseado no contexto
  async applyIntelligentDelay(message, context = {}) {
    const typingDelay = this.calculateTypingDelay(message);
    const messageDelay = this.calculateMessageDelay();
    
    // Delay adicional baseado no contexto
    let contextDelay = 0;
    
    if (context.isFirstMessage) {
      contextDelay += 2000; // 2 segundos extra para primeira mensagem
    }
    
    if (context.isImportantMessage) {
      contextDelay += 1000; // 1 segundo extra para mensagens importantes
    }
    
    if (context.isResponse) {
      // Delay de resposta mais humano (3-15 segundos)
      contextDelay += 3000 + Math.random() * 12000;
    }
    
    const totalDelay = typingDelay + messageDelay + contextDelay;
    
    this.logger.info(`⏱️ Aplicando delay total: ${totalDelay}ms (typing: ${typingDelay}ms, message: ${messageDelay}ms, context: ${contextDelay}ms)`);
    
    await this.sleep(totalDelay);
  }

  // Função de sleep
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Obter estatísticas de comportamento
  getStats() {
    this.updateMessageCounts();
    
    return {
      messageCount: this.state.messageCount,
      burstCount: this.state.burstCount,
      isInCooldown: this.state.isInCooldown,
      currentSession: this.state.currentSession,
      suspiciousPatterns: this.suspiciousPatterns.length,
      behaviorScore: this.calculateBehaviorScore(),
      limits: this.config.limits,
      lastActivity: this.state.lastActivity
    };
  }

  // Calcular score de comportamento humano (0-100)
  calculateBehaviorScore() {
    let score = 100;
    
    // Penalizar padrões suspeitos
    this.suspiciousPatterns.forEach(pattern => {
      switch (pattern.severity) {
        case 'high':
          score -= 20;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });
    
    // Penalizar burst excessivo
    if (this.state.burstCount > this.config.limits.burstLimit) {
      score -= 15;
    }
    
    // Penalizar atividade muito alta
    const hourlyRate = this.state.messageCount.hour / 
      Math.max(1, (Date.now() - this.state.messageCount.lastReset.hour) / 3600000);
    
    if (hourlyRate > this.config.limits.perHour * 0.8) {
      score -= 10;
    }
    
    return Math.max(0, Math.min(100, score));
  }

  // Configurar comportamento personalizado
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('⚙️ Configuração de comportamento atualizada');
  }

  // Reset de estatísticas
  resetStats() {
    this.state.messageCount = {
      minute: 0,
      hour: 0,
      day: 0,
      lastReset: {
        minute: Date.now(),
        hour: Date.now(),
        day: Date.now()
      }
    };
    this.state.burstCount = 0;
    this.state.isInCooldown = false;
    this.behaviorHistory = [];
    this.suspiciousPatterns = [];
    
    this.logger.info('🔄 Estatísticas de comportamento resetadas');
  }
}
