module.exports = {
  apps: [
    {
      name: 'vereadora-rafaela-backend',
      script: 'server-centralized.js',
      cwd: '/backend',
      instances: 1,
      exec_mode: 'fork',

      // Configurações de ambiente
      env: {
        NODE_ENV: 'development',
        PORT: 3001,
        WHATSAPP_USE_HTTP: 'true'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001,
        WHATSAPP_USE_HTTP: 'true',
        ENABLE_ORIGIN_VERIFICATION: 'true',
        RATE_LIMIT_MAX_REQUESTS: 50
      },

      // Configurações de restart
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'data', 'wppconnect-data'],
      max_memory_restart: '500M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',

      // Configurações de logs
      log_file: './logs/pm2-combined.log',
      out_file: './logs/pm2-out.log',
      error_file: './logs/pm2-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // Configurações de cluster (se necessário)
      // instances: 'max',
      // exec_mode: 'cluster',

      // Configurações de monitoramento
      monitoring: false,
      pmx: false,

      // Configurações de autorestart
      autorestart: true,

      // Configurações de cron (restart diário)
      cron_restart: '0 4 * * *', // 4:00 AM todos os dias

      // Configurações de kill timeout
      kill_timeout: 5000,
      listen_timeout: 3000,

      // Configurações de source map
      source_map_support: true,

      // Configurações de interpretador
      interpreter: 'node',
      interpreter_args: '--max-old-space-size=512'
    },

    // Processo separado para WPPConnect Server (opcional)
    {
      name: 'wppconnect-server',
      script: 'start-wppconnect.js',
      cwd: '/backend',
      instances: 1,
      exec_mode: 'fork',

      // Configurações de ambiente
      env: {
        NODE_ENV: 'development',
        WPPCONNECT_PORT: 21465
      },
      env_production: {
        NODE_ENV: 'production',
        WPPCONNECT_PORT: 21465
      },

      // Configurações de restart
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 10000,
      max_restarts: 5,
      min_uptime: '30s',

      // Configurações de logs
      log_file: './logs/wppconnect-combined.log',
      out_file: './logs/wppconnect-out.log',
      error_file: './logs/wppconnect-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // Configurações de monitoramento
      monitoring: false,
      pmx: false,

      // Configurações de autorestart
      autorestart: true,

      // Configurações de kill timeout
      kill_timeout: 10000,
      listen_timeout: 5000,

      // Desabilitado por padrão (o backend principal gerencia o WPPConnect)
      disabled: true
    }
  ],

  // Configurações de deploy (opcional)
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: 'https://github.com/ItaloCabral1995RN/AISTUDIOCHATRAG.git',
      path: '/var/www/vereadora-rafaela',
      'post-deploy': 'cd backend && npm install && pm2 reload ecosystem.config.cjs --env production',
      'pre-setup': 'apt update && apt install git nodejs npm -y'
    }
  }
};