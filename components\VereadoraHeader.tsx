import React from 'react';
import { isSupabaseConfigured } from '../config/supabase';
import { isGeminiConfigured } from '../config/gemini';

interface StatusIndicatorProps {
  whatsappConnected?: boolean;
}

export const VereadoraHeader: React.FC<StatusIndicatorProps> = ({ whatsappConnected = false }) => {
  return (
    <header className="bg-gh-bg border-b border-gh-border">
      <div className="max-w-6xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo e Título - Estilo GitHub */}
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-gh-link rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">RN</span>
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gh-text">
                Vereadora Rafaela de Nilda
              </h1>
              <p className="text-sm text-gh-text-secondary">
                Assistente Virtual Inteligente • Parnamirim/RN
              </p>
            </div>
          </div>

          {/* Indicadores de Status Simples */}
          <div className="hidden md:flex items-center space-x-3">
            {/* Sistema */}
            <div className="flex items-center space-x-2">
              <div className={`status-indicator ${
                isSupabaseConfigured && isGeminiConfigured
                  ? 'status-success'
                  : 'status-warning'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  isSupabaseConfigured && isGeminiConfigured
                    ? 'bg-success'
                    : 'bg-warning'
                }`}></div>
                <span>Sistema</span>
              </div>
            </div>

            {/* WhatsApp */}
            <div className="flex items-center space-x-2">
              <div className={`status-indicator ${
                whatsappConnected ? 'status-success' : 'status-danger'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  whatsappConnected ? 'bg-success' : 'bg-danger'
                }`}></div>
                <span>WhatsApp</span>
              </div>
            </div>

            {/* Segurança */}
            <div className="flex items-center space-x-2">
              <div className="status-indicator status-success">
                <div className="w-2 h-2 rounded-full bg-success"></div>
                <span>Segurança</span>
              </div>
            </div>
          </div>

          {/* Status Mobile */}
          <div className="md:hidden flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              isSupabaseConfigured && isGeminiConfigured
                ? 'bg-success'
                : 'bg-warning'
            }`}></div>
            <div className={`w-2 h-2 rounded-full ${
              whatsappConnected ? 'bg-success' : 'bg-danger'
            }`}></div>
            <div className="w-2 h-2 rounded-full bg-success"></div>
          </div>
        </div>

        {/* Informações de Contato - Simplificadas */}
        <div className="mt-4 pt-4 border-t border-gh-border">
          <div className="flex flex-wrap items-center gap-6 text-sm text-gh-text-secondary">
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span>Câmara Municipal de Parnamirim</span>
            </div>

            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span><EMAIL></span>
            </div>

            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              <span>(84) 99999-9999</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};
