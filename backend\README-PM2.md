# 🚀 **GERENCIAMENTO COM PM2 - VEREADORA RAFAELA**

## 📊 **SERVIDOR SEMPRE RODANDO COM PM2**

O PM2 é um gerenciador de processos para Node.js que mantém o servidor sempre rodando, com restart automático, logs organizados e monitoramento em tempo real.

## 🎯 **COMANDOS PRINCIPAIS**

### **🚀 INICIAR SERVIDOR**
```bash
# Iniciar em modo produção (padrão)
npm run pm2:start

# Iniciar em modo desenvolvimento
npm run pm2:dev

# Iniciar em modo produção explícito
npm run pm2:prod
```

### **📊 MONITORAMENTO**
```bash
# Ver status de todos os processos
npm run pm2:status

# Monitoramento em tempo real (CPU, Memória)
npm run pm2:monit

# Ver logs em tempo real
npm run pm2:logs

# Ver logs das últimas 50 linhas
pm2 logs vereadora-rafaela --lines 50
```

### **🔄 CONTROLE DO PROCESSO**
```bash
# Parar servidor
npm run pm2:stop

# Reiniciar servidor (mata e inicia)
npm run pm2:restart

# Reload servidor (zero downtime)
npm run pm2:reload

# Remover processo do PM2
npm run pm2:delete
```

## 📈 **FUNCIONALIDADES ATIVAS**

### **✅ AUTO-RESTART**
- ✅ Reinicia automaticamente se o processo falhar
- ✅ Máximo de 10 tentativas de restart
- ✅ Tempo mínimo de 10s antes de considerar estável
- ✅ Delay de 4s entre tentativas

### **📊 LOGS ORGANIZADOS**
- ✅ Logs de erro: `./logs/pm2-error.log`
- ✅ Logs de saída: `./logs/pm2-out.log`
- ✅ Logs combinados: `./logs/pm2-combined.log`
- ✅ Timestamp em todos os logs

### **🔧 CONFIGURAÇÕES**
- ✅ Limite de memória: 1GB (restart automático)
- ✅ Timeout de kill: 5s
- ✅ Timeout de listen: 3s
- ✅ Shutdown graceful ativado

## 🌐 **AMBIENTES**

### **🏭 PRODUÇÃO**
```bash
npm run pm2:prod
```
- NODE_ENV=production
- Logs otimizados
- Performance máxima

### **🔧 DESENVOLVIMENTO**
```bash
npm run pm2:dev
```
- NODE_ENV=development
- Logs detalhados
- Debug ativado

## 📊 **MONITORAMENTO EM TEMPO REAL**

### **Dashboard PM2**
```bash
npm run pm2:monit
```
Mostra em tempo real:
- 📈 CPU Usage
- 💾 Memory Usage
- 🔄 Restart Count
- ⏱️ Uptime
- 📊 Logs em tempo real

### **Status Detalhado**
```bash
npm run pm2:status
```
Mostra:
- 🆔 Process ID
- 📛 Nome do processo
- 🔄 Modo (cluster/fork)
- ↺ Número de restarts
- 📊 Status (online/stopped/error)
- 💻 CPU e Memória atual

## 🔧 **CONFIGURAÇÃO AVANÇADA**

O arquivo `ecosystem.config.cjs` contém todas as configurações:

```javascript
{
  name: 'vereadora-rafaela',
  script: 'src/server.js',
  instances: 1,
  autorestart: true,
  max_memory_restart: '1G',
  max_restarts: 10,
  min_uptime: '10s',
  restart_delay: 4000
}
```

## 🚨 **COMANDOS DE EMERGÊNCIA**

### **🆘 PARAR TUDO**
```bash
pm2 kill
```

### **🔄 RESTART FORÇADO**
```bash
pm2 restart vereadora-rafaela --force
```

### **📊 INFORMAÇÕES DETALHADAS**
```bash
pm2 describe vereadora-rafaela
```

### **🧹 LIMPAR LOGS**
```bash
pm2 flush vereadora-rafaela
```

## 🎯 **VANTAGENS DO PM2**

1. ✅ **Zero Downtime** - Servidor sempre disponível
2. ✅ **Auto-Restart** - Recuperação automática de falhas
3. ✅ **Logs Organizados** - Fácil debugging
4. ✅ **Monitoramento** - CPU, memória em tempo real
5. ✅ **Cluster Mode** - Escalabilidade (se necessário)
6. ✅ **Startup Script** - Inicia com o sistema
7. ✅ **Memory Limit** - Previne memory leaks

## 🔄 **FLUXO RECOMENDADO**

### **🚀 DEPLOY EM PRODUÇÃO**
```bash
# 1. Parar processo atual (se existir)
npm run pm2:stop

# 2. Atualizar código
git pull origin main

# 3. Instalar dependências
npm install

# 4. Iniciar em produção
npm run pm2:prod

# 5. Verificar status
npm run pm2:status

# 6. Monitorar logs
npm run pm2:logs
```

### **🔧 DESENVOLVIMENTO**
```bash
# Iniciar em modo dev
npm run pm2:dev

# Monitorar em tempo real
npm run pm2:monit
```

## 🎉 **RESULTADO**

**Agora você tem um servidor profissional que:**
- ✅ **Nunca para** - Auto-restart em falhas
- ✅ **Monitora tudo** - CPU, memória, logs
- ✅ **Logs organizados** - Fácil debugging
- ✅ **Zero downtime** - Atualizações sem parar
- ✅ **Pronto para produção** - Configuração profissional

**Execute `npm run pm2:start` e tenha um servidor sempre rodando!** 🚀📊🔄
