# 🐳 Guia de Instalação: Docker + WPPConnect Server

Guia completo para instalar Docker e configurar WPPConnect Server no Windows.

## 📋 Pré-requisitos

- Windows 10/11 Pro, Enterprise ou Education (64-bit)
- Virtualização habilitada no BIOS
- WSL 2 (Windows Subsystem for Linux)
- Pelo menos 4GB de RAM disponível

## 🔧 Passo 1: Instalar Docker Desktop

### 1.1 Download e Instalação

1. **Baixar Docker Desktop:**
   - Acesse: https://www.docker.com/products/docker-desktop/
   - Clique em "Download for Windows"
   - Execute o instalador `Docker Desktop Installer.exe`

2. **Configurar durante a instalação:**
   - ✅ Marque "Use WSL 2 instead of Hyper-V"
   - ✅ Marque "Add shortcut to desktop"
   - Clique em "Ok" e aguarde a instalação

3. **Reiniciar o computador** quando solicitado

### 1.2 Configurar WSL 2

1. **Abrir PowerShell como Administrador**
2. **Executar comandos:**
   ```powershell
   # Habilitar WSL
   dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
   
   # Habilitar Virtual Machine Platform
   dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
   
   # Reiniciar o computador
   Restart-Computer
   ```

3. **Após reiniciar, baixar WSL 2 kernel:**
   - Acesse: https://aka.ms/wsl2kernel
   - Baixe e instale o pacote de atualização

4. **Definir WSL 2 como padrão:**
   ```powershell
   wsl --set-default-version 2
   ```

### 1.3 Verificar Instalação

```powershell
# Verificar versão do Docker
docker --version

# Verificar se está funcionando
docker run hello-world
```

## 🚀 Passo 2: Configurar WPPConnect Server

### 2.1 Preparar Ambiente

```bash
# Navegar para o diretório do backend
cd backend

# Criar diretórios para dados do WPPConnect
mkdir wppconnect-data
mkdir wppconnect-data/sessions
mkdir wppconnect-data/tokens
mkdir wppconnect-data/downloads
mkdir wppconnect-data/uploads
```

### 2.2 Configurar Variáveis de Ambiente

Criar arquivo `.env` no diretório backend:

```env
# WPPConnect Server Configuration
WPPCONNECT_SERVER_URL=http://localhost:21465
WPPCONNECT_SECRET_KEY=vereadora-rafaela-secret-2024
WEBHOOK_URL=http://host.docker.internal:3001/api/whatsapp/webhook

# WhatsApp Session
WHATSAPP_SESSION_NAME=vereadora-rafaela
```

### 2.3 Iniciar WPPConnect Server

```bash
# Iniciar com docker-compose
docker-compose -f docker-compose.wppconnect.yml up -d

# Verificar se está rodando
docker ps

# Ver logs
docker-compose -f docker-compose.wppconnect.yml logs -f
```

### 2.4 Verificar Funcionamento

```bash
# Testar API do WPPConnect
curl http://localhost:21465/api/vereadora-rafaela/status

# Ou usar o script do projeto
npm run status:wppconnect
```

## 🔧 Passo 3: Integração com Backend

### 3.1 Iniciar Backend com WPPConnect

```bash
# Verificar e iniciar WPPConnect automaticamente
npm run dev:full

# Ou manualmente
npm run check:wppconnect
npm run dev
```

### 3.2 Verificar Integração

```bash
# Verificar status do backend
curl http://localhost:3001/api/health/detailed

# Verificar status do WhatsApp
curl http://localhost:3001/api/whatsapp/status
```

## 📱 Passo 4: Conectar WhatsApp

### 4.1 Gerar QR Code

1. **Acessar frontend:** http://localhost:3000
2. **Ir para aba WhatsApp**
3. **Clicar em "Conectar WhatsApp"**
4. **Escanear QR Code com WhatsApp**

### 4.2 Verificar Conexão

```bash
# Ver logs do WPPConnect
docker logs wppconnect-server-rafaela -f

# Ver logs do backend
tail -f logs/whatsapp-backend.log
```

## 🛠️ Troubleshooting

### Problema: Docker não inicia

**Solução:**
```powershell
# Verificar se WSL está funcionando
wsl --list --verbose

# Reiniciar Docker Desktop
# Ir em System Tray > Docker > Restart
```

### Problema: WPPConnect não conecta

**Solução:**
```bash
# Parar e remover container
docker-compose -f docker-compose.wppconnect.yml down

# Limpar dados antigos
rm -rf wppconnect-data/sessions/*
rm -rf wppconnect-data/tokens/*

# Iniciar novamente
docker-compose -f docker-compose.wppconnect.yml up -d
```

### Problema: Erro de permissão

**Solução:**
```powershell
# Dar permissões ao diretório
icacls wppconnect-data /grant nvv\italo:F /T
```

## 📊 Comandos Úteis

```bash
# Parar WPPConnect
docker-compose -f docker-compose.wppconnect.yml down

# Reiniciar WPPConnect
docker-compose -f docker-compose.wppconnect.yml restart

# Ver logs em tempo real
docker-compose -f docker-compose.wppconnect.yml logs -f

# Limpar dados do WhatsApp
docker-compose -f docker-compose.wppconnect.yml down -v

# Verificar uso de recursos
docker stats wppconnect-server-rafaela
```

## 🔒 Segurança

1. **Alterar chave secreta** no arquivo `.env`
2. **Configurar firewall** para bloquear porta 21465 externamente
3. **Fazer backup** dos dados de sessão regularmente
4. **Monitorar logs** para atividades suspeitas

## 📝 Notas Importantes

- O WPPConnect Server consome ~500MB de RAM
- Sessões WhatsApp expiram após 7 dias de inatividade
- QR Code expira em 60 segundos
- Backup automático configurado para 2:00 AM diariamente
- Logs são rotacionados automaticamente

---

**Status:** ✅ Opcional - Sistema funciona sem WPPConnect
**Benefício:** 📱 Integração completa com WhatsApp
**Tempo:** ⏱️ 30-60 minutos para configuração completa
