#!/usr/bin/env node

/**
 * Script para iniciar o WPPConnect Server
 * Uso: node start-wppconnect.js
 */

import WPPConnectServer from './wppconnect-server.js';
import { Logger } from './src/utils/Logger.js';

const logger = new Logger();

async function main() {
  try {
    logger.info('🚀 Iniciando WPPConnect Server...');
    
    // Verificar variáveis de ambiente
    const port = process.env.WPPCONNECT_PORT || 21465;
    const secretKey = process.env.WPPCONNECT_SECRET_KEY || 'vereadora-rafaela-secret-2024';
    const webhookUrl = process.env.WEBHOOK_URL || 'http://localhost:3001/api/whatsapp/webhook';
    
    logger.info(`📊 Configurações:`);
    logger.info(`   Porta: ${port}`);
    logger.info(`   Secret Key: ${secretKey.substring(0, 10)}...`);
    logger.info(`   Webhook URL: ${webhookUrl}`);
    
    // Criar e iniciar servidor
    const server = new WPPConnectServer();
    await server.start();
    
  } catch (error) {
    logger.error('❌ Erro ao iniciar WPPConnect Server:', error);
    process.exit(1);
  }
}

// Tratamento de sinais
process.on('SIGINT', () => {
  logger.info('🛑 Recebido SIGINT, encerrando...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('🛑 Recebido SIGTERM, encerrando...');
  process.exit(0);
});

// Executar
main();
