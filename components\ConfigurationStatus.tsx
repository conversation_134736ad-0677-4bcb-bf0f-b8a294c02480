import React, { useState, useEffect } from 'react';
import { isSupabaseConfigured, supabaseUtils } from '../config/supabase';
import { isGeminiConfigured, geminiUtils } from '../config/gemini';

interface ServiceStatus {
  name: string;
  configured: boolean;
  connected: boolean;
  loading: boolean;
  error?: string;
  details?: any;
}

export const ConfigurationStatus: React.FC = () => {
  const [supabaseStatus, setSupabaseStatus] = useState<ServiceStatus>({
    name: 'Supabase',
    configured: isSupabaseConfigured,
    connected: false,
    loading: true
  });

  const [geminiStatus, setGeminiStatus] = useState<ServiceStatus>({
    name: 'Gemini AI',
    configured: isGeminiConfigured,
    connected: false,
    loading: true
  });

  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    checkSupabaseStatus();
    checkGeminiStatus();
  }, []);

  const checkSupabaseStatus = async () => {
    if (!isSupabaseConfigured) {
      setSupabaseStatus(prev => ({
        ...prev,
        loading: false,
        error: 'Não configurado'
      }));
      return;
    }

    try {
      const connected = await supabaseUtils.testConnection();
      const stats = connected ? await supabaseUtils.getStats() : null;

      setSupabaseStatus(prev => ({
        ...prev,
        connected,
        loading: false,
        details: stats,
        error: connected ? undefined : 'Falha na conexão'
      }));
    } catch (error) {
      setSupabaseStatus(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      }));
    }
  };

  const checkGeminiStatus = async () => {
    if (!isGeminiConfigured) {
      setGeminiStatus(prev => ({
        ...prev,
        loading: false,
        error: 'Não configurado'
      }));
      return;
    }

    try {
      const connected = await geminiUtils.testConnection();
      const config = geminiUtils.getConfig();

      setGeminiStatus(prev => ({
        ...prev,
        connected,
        loading: false,
        details: config,
        error: connected ? undefined : 'Falha na conexão'
      }));
    } catch (error) {
      setGeminiStatus(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      }));
    }
  };

  const getStatusIcon = (status: ServiceStatus) => {
    if (status.loading) {
      return (
        <div className="loading-modern">
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
        </div>
      );
    }

    if (!status.configured) {
      return <span className="text-gray-400">⚪</span>;
    }

    if (status.connected) {
      return <span className="text-orange-500">🟢</span>;
    }

    return <span className="text-red-500">🔴</span>;
  };

  const getStatusText = (status: ServiceStatus) => {
    if (status.loading) return 'Verificando...';
    if (!status.configured) return 'Não configurado';
    if (status.connected) return 'Conectado';
    return status.error || 'Erro de conexão';
  };

  const getOverallStatus = () => {
    if (supabaseStatus.loading || geminiStatus.loading) {
      return { icon: '⏳', text: 'Verificando configurações...', color: 'text-gray-600' };
    }

    if (supabaseStatus.connected && geminiStatus.connected) {
      return { icon: '🟢', text: 'Sistema completo ativo', color: 'text-orange-600' };
    }

    if (supabaseStatus.configured && geminiStatus.configured) {
      return { icon: '🟡', text: 'Configurado, mas com problemas de conexão', color: 'text-orange-500' };
    }

    if (supabaseStatus.configured || geminiStatus.configured) {
      return { icon: '🟡', text: 'Configuração parcial', color: 'text-orange-500' };
    }

    return { icon: '🔴', text: 'Sistema funcionando offline', color: 'text-gray-600' };
  };

  const overallStatus = getOverallStatus();

  return (
    <div className="glass-card p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{overallStatus.icon}</span>
          <div>
            <h3 className="font-semibold text-gray-800">Status do Sistema</h3>
            <p className={`text-sm ${overallStatus.color}`}>{overallStatus.text}</p>
          </div>
        </div>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="px-3 py-1 text-sm bg-white/30 hover:bg-white/50 rounded-lg transition-colors"
        >
          {showDetails ? 'Ocultar' : 'Detalhes'}
        </button>
      </div>

      {showDetails && (
        <div className="space-y-4 pt-4 border-t border-white/20">
          {/* Status do Supabase */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getStatusIcon(supabaseStatus)}
              <div>
                <p className="font-medium text-gray-800">🗄️ Supabase Database</p>
                <p className="text-sm text-gray-600">{getStatusText(supabaseStatus)}</p>
              </div>
            </div>
            {supabaseStatus.details && (
              <div className="text-xs text-gray-500">
                <p>Conversas: {supabaseStatus.details.conversations}</p>
                <p>Mensagens: {supabaseStatus.details.messages}</p>
                <p>Documentos: {supabaseStatus.details.documents}</p>
              </div>
            )}
          </div>

          {/* Status do Gemini */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getStatusIcon(geminiStatus)}
              <div>
                <p className="font-medium text-gray-800">🤖 Gemini AI</p>
                <p className="text-sm text-gray-600">{getStatusText(geminiStatus)}</p>
              </div>
            </div>
            {geminiStatus.details && (
              <div className="text-xs text-gray-500">
                <p>API Key: {geminiStatus.details.apiKeyPrefix}</p>
                <p>Modelos: {Object.keys(geminiStatus.details.models).length}</p>
              </div>
            )}
          </div>

          {/* Funcionalidades disponíveis */}
          <div className="pt-3 border-t border-white/10">
            <p className="text-sm font-medium text-gray-700 mb-2">Funcionalidades Disponíveis:</p>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className={`flex items-center space-x-1 ${supabaseStatus.connected ? 'text-orange-600' : 'text-gray-400'}`}>
                <span>{supabaseStatus.connected ? '✅' : '❌'}</span>
                <span>Persistência de dados</span>
              </div>
              <div className={`flex items-center space-x-1 ${geminiStatus.connected ? 'text-orange-600' : 'text-gray-400'}`}>
                <span>{geminiStatus.connected ? '✅' : '❌'}</span>
                <span>IA avançada</span>
              </div>
              <div className={`flex items-center space-x-1 ${supabaseStatus.connected ? 'text-orange-600' : 'text-gray-400'}`}>
                <span>{supabaseStatus.connected ? '✅' : '❌'}</span>
                <span>Histórico de conversas</span>
              </div>
              <div className={`flex items-center space-x-1 ${geminiStatus.connected ? 'text-orange-600' : 'text-gray-400'}`}>
                <span>{geminiStatus.connected ? '✅' : '❌'}</span>
                <span>Busca semântica</span>
              </div>
              <div className={`flex items-center space-x-1 ${supabaseStatus.connected && geminiStatus.connected ? 'text-orange-600' : 'text-gray-400'}`}>
                <span>{supabaseStatus.connected && geminiStatus.connected ? '✅' : '❌'}</span>
                <span>Análise de documentos</span>
              </div>
              <div className={`flex items-center space-x-1 ${geminiStatus.connected ? 'text-orange-600' : 'text-gray-400'}`}>
                <span>{geminiStatus.connected ? '✅' : '❌'}</span>
                <span>Embeddings</span>
              </div>
            </div>
          </div>

          {/* Ações de configuração */}
          {(!supabaseStatus.configured || !geminiStatus.configured) && (
            <div className="pt-3 border-t border-white/10">
              <p className="text-sm font-medium text-gray-700 mb-2">Configuração Necessária:</p>
              <div className="space-y-2 text-xs">
                {!supabaseStatus.configured && (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>📝</span>
                    <span>Configure VITE_SUPABASE_URL e VITE_SUPABASE_ANON_KEY no arquivo .env</span>
                  </div>
                )}
                {!geminiStatus.configured && (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>🔑</span>
                    <span>Configure VITE_GEMINI_API_KEY no arquivo .env</span>
                  </div>
                )}
                <div className="flex items-center space-x-2 text-gray-600">
                  <span>📖</span>
                  <span>Consulte docs/SETUP_SUPABASE_GEMINI.md para instruções detalhadas</span>
                </div>
              </div>
            </div>
          )}

          {/* Botões de ação */}
          <div className="flex space-x-2 pt-3">
            <button
              onClick={() => {
                checkSupabaseStatus();
                checkGeminiStatus();
              }}
              className="px-3 py-1 text-xs bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              🔄 Verificar Novamente
            </button>
            {(!supabaseStatus.configured || !geminiStatus.configured) && (
              <a
                href="docs/SETUP_SUPABASE_GEMINI.md"
                target="_blank"
                rel="noopener noreferrer"
                className="px-3 py-1 text-xs bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
              >
                📖 Guia de Configuração
              </a>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
