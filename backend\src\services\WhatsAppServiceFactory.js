import { WhatsAppHttpService } from './WhatsAppHttpService.js';
import { WhatsAppSimulatorService } from './WhatsAppSimulatorService.js';
import { WhatsAppRealService } from './WhatsAppRealService.js';

/**
 * Factory para criar instâncias dos serviços WhatsApp
 * Centraliza a lógica de seleção do serviço apropriado
 */
export class WhatsAppServiceFactory {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;
  }

  /**
   * Criar instância do serviço WhatsApp apropriado
   */
  createService() {
    const { whatsapp } = this.config;

    // Determinar qual serviço usar baseado na configuração
    if (whatsapp.useSimulator) {
      this.logger.info('🎭 Criando WhatsApp Simulator Service (modo desenvolvimento)');
      return this.createSimulatorService();
    } 
    
    if (whatsapp.useReal) {
      this.logger.info('📱 Criando WhatsApp Real Service (WPPConnect direto)');
      return this.createRealService();
    }
    
    // Padrão: HTTP Service
    this.logger.info('🌐 Criando WhatsApp HTTP Service (WPPConnect Server)');
    return this.createHttpService();
  }

  /**
   * Criar serviço HTTP (padrão para produção)
   */
  createHttpService() {
    const service = new WhatsAppHttpService();
    
    // Configurar propriedades específicas
    service.serverUrl = this.config.wppconnect.serverUrl;
    service.sessionName = this.config.whatsapp.sessionName;
    service.secretKey = this.config.wppconnect.secretKey;
    service.requestTimeout = this.config.wppconnect.requestTimeout;
    service.maxConnectionAttempts = this.config.whatsapp.maxConnectionAttempts;
    service.reconnectDelay = this.config.whatsapp.reconnectDelay;
    service.maxReconnectAttempts = this.config.whatsapp.maxReconnectAttempts;
    
    return service;
  }

  /**
   * Criar serviço simulador (desenvolvimento)
   */
  createSimulatorService() {
    const service = new WhatsAppSimulatorService();
    
    // Configurar propriedades específicas
    service.sessionName = this.config.whatsapp.sessionName;
    service.simulationMode = true;
    
    return service;
  }

  /**
   * Criar serviço real (WPPConnect direto)
   */
  createRealService() {
    const service = new WhatsAppRealService();
    
    // Configurar propriedades específicas
    service.sessionName = this.config.whatsapp.sessionName;
    service.autoClose = this.config.whatsapp.autoClose;
    service.disableWelcome = this.config.whatsapp.disableWelcome;
    service.maxConnectionAttempts = this.config.whatsapp.maxConnectionAttempts;
    service.reconnectDelay = this.config.whatsapp.reconnectDelay;
    service.maxReconnectAttempts = this.config.whatsapp.maxReconnectAttempts;
    
    return service;
  }

  /**
   * Obter informações sobre o tipo de serviço que será criado
   */
  getServiceInfo() {
    const { whatsapp } = this.config;

    if (whatsapp.useSimulator) {
      return {
        type: 'simulator',
        name: 'WhatsApp Simulator Service',
        description: 'Serviço simulado para desenvolvimento e testes',
        production: false
      };
    }
    
    if (whatsapp.useReal) {
      return {
        type: 'real',
        name: 'WhatsApp Real Service',
        description: 'Integração direta com WPPConnect (biblioteca)',
        production: true
      };
    }
    
    return {
      type: 'http',
      name: 'WhatsApp HTTP Service',
      description: 'Integração via WPPConnect Server (HTTP API)',
      production: true
    };
  }

  /**
   * Validar configuração para o tipo de serviço selecionado
   */
  validateConfiguration() {
    const { whatsapp, wppconnect } = this.config;
    const errors = [];

    if (whatsapp.useHttp) {
      // Validações para HTTP Service
      if (!wppconnect.serverUrl) {
        errors.push('WPPCONNECT_SERVER_URL é obrigatório para HTTP Service');
      }
      
      if (!wppconnect.secretKey) {
        errors.push('WPPCONNECT_SECRET_KEY é obrigatório para HTTP Service');
      }
      
      if (isNaN(wppconnect.port) || wppconnect.port < 1 || wppconnect.port > 65535) {
        errors.push('WPPCONNECT_PORT deve ser um número válido entre 1 e 65535');
      }
    }

    if (whatsapp.useReal) {
      // Validações para Real Service
      // Verificar se as dependências do WPPConnect estão instaladas
      try {
        require.resolve('@wppconnect-team/wppconnect');
      } catch (error) {
        errors.push('@wppconnect-team/wppconnect não está instalado');
      }
    }

    // Validações gerais
    if (!whatsapp.sessionName) {
      errors.push('WHATSAPP_SESSION_NAME é obrigatório');
    }

    if (whatsapp.maxConnectionAttempts < 1) {
      errors.push('WHATSAPP_MAX_CONNECTION_ATTEMPTS deve ser maior que 0');
    }

    if (whatsapp.reconnectDelay < 1000) {
      errors.push('WHATSAPP_RECONNECT_DELAY deve ser pelo menos 1000ms');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Obter configuração recomendada para produção
   */
  getProductionRecommendations() {
    const recommendations = [];

    if (this.config.whatsapp.useSimulator) {
      recommendations.push({
        type: 'warning',
        message: 'Simulator Service não deve ser usado em produção',
        suggestion: 'Configure WHATSAPP_USE_HTTP=true ou WHATSAPP_USE_REAL=true'
      });
    }

    if (this.config.whatsapp.useReal && this.config.app.isProduction) {
      recommendations.push({
        type: 'info',
        message: 'Real Service pode ser menos estável que HTTP Service',
        suggestion: 'Considere usar HTTP Service com WPPConnect Server para maior estabilidade'
      });
    }

    if (!this.config.antiban.enabled) {
      recommendations.push({
        type: 'warning',
        message: 'Anti-ban está desabilitado',
        suggestion: 'Habilite anti-ban para evitar bloqueios do WhatsApp'
      });
    }

    if (this.config.whatsapp.maxConnectionAttempts > 10) {
      recommendations.push({
        type: 'warning',
        message: 'Muitas tentativas de conexão podem causar bloqueios',
        suggestion: 'Reduza WHATSAPP_MAX_CONNECTION_ATTEMPTS para 5 ou menos'
      });
    }

    return recommendations;
  }

  /**
   * Criar serviço com configuração automática
   */
  createServiceWithAutoConfig() {
    // Validar configuração
    const validation = this.validateConfiguration();
    
    if (!validation.valid) {
      throw new Error(`Configuração inválida: ${validation.errors.join(', ')}`);
    }

    // Mostrar recomendações se em produção
    if (this.config.app.isProduction) {
      const recommendations = this.getProductionRecommendations();
      
      recommendations.forEach(rec => {
        if (rec.type === 'warning') {
          this.logger.warn(`⚠️ ${rec.message} - ${rec.suggestion}`);
        } else {
          this.logger.info(`ℹ️ ${rec.message} - ${rec.suggestion}`);
        }
      });
    }

    // Criar serviço
    const service = this.createService();
    const serviceInfo = this.getServiceInfo();
    
    this.logger.info(`✅ ${serviceInfo.name} criado com sucesso`);
    this.logger.info(`📋 Descrição: ${serviceInfo.description}`);
    
    return service;
  }

  /**
   * Obter estatísticas de uso dos serviços
   */
  static getUsageStats() {
    // Esta seria implementada com métricas reais em produção
    return {
      http: {
        instances: 0,
        uptime: 0,
        requests: 0,
        errors: 0
      },
      real: {
        instances: 0,
        uptime: 0,
        connections: 0,
        errors: 0
      },
      simulator: {
        instances: 0,
        uptime: 0,
        simulations: 0
      }
    };
  }
}
