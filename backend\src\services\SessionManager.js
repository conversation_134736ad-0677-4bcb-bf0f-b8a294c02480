import fs from 'fs/promises';
import path from 'path';
import { Logger } from '../utils/Logger.js';
import cron from 'node-cron';

export class SessionManager {
  constructor() {
    this.logger = new Logger();
    this.sessionsPath = path.join(process.cwd(), 'data', 'sessions');
    this.backupPath = path.join(process.cwd(), 'data', 'backups');
    this.sessions = new Map();
    this.isInitialized = false;
    
    // Configurações
    this.backupEnabled = process.env.BACKUP_ENABLED === 'true';
    this.backupInterval = parseInt(process.env.BACKUP_INTERVAL_HOURS) || 24;
  }

  async initialize() {
    this.logger.info('🔄 Inicializando Session Manager...');
    
    try {
      // Criar diretórios necessários
      await this.createDirectories();
      
      // Carregar sessões existentes
      await this.loadSessions();
      
      // Configurar backup automático
      if (this.backupEnabled) {
        this.setupAutoBackup();
      }
      
      // Configurar limpeza automática
      this.setupAutoCleanup();
      
      this.isInitialized = true;
      this.logger.info('✅ Session Manager inicializado com sucesso!');
      
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar Session Manager:', error);
      throw error;
    }
  }

  async createDirectories() {
    try {
      await fs.mkdir(this.sessionsPath, { recursive: true });
      await fs.mkdir(this.backupPath, { recursive: true });
      await fs.mkdir(path.join(process.cwd(), 'data', 'logs'), { recursive: true });
      await fs.mkdir(path.join(process.cwd(), 'public'), { recursive: true });
      
      this.logger.info('✅ Diretórios criados/verificados');
    } catch (error) {
      this.logger.error('❌ Erro ao criar diretórios:', error);
      throw error;
    }
  }

  async loadSessions() {
    try {
      const sessionFiles = await fs.readdir(this.sessionsPath).catch(() => []);
      
      for (const file of sessionFiles) {
        if (file.endsWith('.json')) {
          try {
            const filePath = path.join(this.sessionsPath, file);
            const sessionData = await fs.readFile(filePath, 'utf8');
            const session = JSON.parse(sessionData);
            
            this.sessions.set(session.id, session);
            this.logger.debug(`📂 Sessão carregada: ${session.id}`);
            
          } catch (error) {
            this.logger.warn(`⚠️ Erro ao carregar sessão ${file}:`, error);
          }
        }
      }
      
      this.logger.info(`✅ ${this.sessions.size} sessões carregadas`);
      
    } catch (error) {
      this.logger.error('❌ Erro ao carregar sessões:', error);
    }
  }

  async saveSession(sessionId, sessionData) {
    try {
      const session = {
        id: sessionId,
        ...sessionData,
        lastUpdated: new Date().toISOString()
      };
      
      // Salvar em memória
      this.sessions.set(sessionId, session);
      
      // Salvar em arquivo
      const filePath = path.join(this.sessionsPath, `${sessionId}.json`);
      await fs.writeFile(filePath, JSON.stringify(session, null, 2));
      
      this.logger.debug(`💾 Sessão salva: ${sessionId}`);
      return session;
      
    } catch (error) {
      this.logger.error(`❌ Erro ao salvar sessão ${sessionId}:`, error);
      throw error;
    }
  }

  async getSession(sessionId) {
    try {
      return this.sessions.get(sessionId) || null;
    } catch (error) {
      this.logger.error(`❌ Erro ao obter sessão ${sessionId}:`, error);
      return null;
    }
  }

  async updateSessionStatus(sessionId, status, friendlyStatus) {
    try {
      const existingSession = this.sessions.get(sessionId) || {};
      
      const sessionData = {
        ...existingSession,
        status,
        friendlyStatus,
        statusHistory: [
          ...(existingSession.statusHistory || []),
          {
            status,
            friendlyStatus,
            timestamp: new Date().toISOString()
          }
        ].slice(-10) // Manter apenas os últimos 10 status
      };
      
      await this.saveSession(sessionId, sessionData);
      this.logger.info(`📱 Status da sessão ${sessionId}: ${friendlyStatus}`);
      
    } catch (error) {
      this.logger.error(`❌ Erro ao atualizar status da sessão ${sessionId}:`, error);
    }
  }

  async deleteSession(sessionId) {
    try {
      // Remover da memória
      this.sessions.delete(sessionId);
      
      // Remover arquivo
      const filePath = path.join(this.sessionsPath, `${sessionId}.json`);
      await fs.unlink(filePath).catch(() => {}); // Ignorar se não existir
      
      this.logger.info(`🗑️ Sessão removida: ${sessionId}`);
      
    } catch (error) {
      this.logger.error(`❌ Erro ao remover sessão ${sessionId}:`, error);
    }
  }

  async getAllSessions() {
    try {
      return Array.from(this.sessions.values());
    } catch (error) {
      this.logger.error('❌ Erro ao obter todas as sessões:', error);
      return [];
    }
  }

  async getActiveSessions() {
    try {
      const activeSessions = Array.from(this.sessions.values()).filter(session => {
        const lastUpdate = new Date(session.lastUpdated);
        const now = new Date();
        const hoursDiff = (now - lastUpdate) / (1000 * 60 * 60);
        
        // Considerar ativa se foi atualizada nas últimas 24 horas
        return hoursDiff < 24 && ['ready', 'authenticated', 'chatsAvailable'].includes(session.status);
      });
      
      return activeSessions;
    } catch (error) {
      this.logger.error('❌ Erro ao obter sessões ativas:', error);
      return [];
    }
  }

  setupAutoBackup() {
    // Backup a cada X horas (configurável)
    const cronExpression = `0 */${this.backupInterval} * * *`;
    
    cron.schedule(cronExpression, async () => {
      try {
        await this.createBackup();
      } catch (error) {
        this.logger.error('❌ Erro no backup automático:', error);
      }
    });
    
    this.logger.info(`⏰ Backup automático configurado: a cada ${this.backupInterval} horas`);
  }

  setupAutoCleanup() {
    // Limpeza diária às 2:00 AM
    cron.schedule('0 2 * * *', async () => {
      try {
        await this.cleanupOldSessions();
        await this.cleanupOldBackups();
      } catch (error) {
        this.logger.error('❌ Erro na limpeza automática:', error);
      }
    });
    
    this.logger.info('⏰ Limpeza automática configurada: diariamente às 2:00');
  }

  async createBackup() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `sessions_backup_${timestamp}.json`;
      const backupFilePath = path.join(this.backupPath, backupFileName);
      
      const allSessions = Array.from(this.sessions.values());
      const backupData = {
        timestamp: new Date().toISOString(),
        sessionsCount: allSessions.length,
        sessions: allSessions
      };
      
      await fs.writeFile(backupFilePath, JSON.stringify(backupData, null, 2));
      
      this.logger.info(`💾 Backup criado: ${backupFileName} (${allSessions.length} sessões)`);
      
    } catch (error) {
      this.logger.error('❌ Erro ao criar backup:', error);
      throw error;
    }
  }

  async restoreFromBackup(backupFileName) {
    try {
      const backupFilePath = path.join(this.backupPath, backupFileName);
      const backupData = JSON.parse(await fs.readFile(backupFilePath, 'utf8'));
      
      // Limpar sessões atuais
      this.sessions.clear();
      
      // Restaurar sessões do backup
      for (const session of backupData.sessions) {
        this.sessions.set(session.id, session);
        
        // Salvar arquivo individual
        const sessionFilePath = path.join(this.sessionsPath, `${session.id}.json`);
        await fs.writeFile(sessionFilePath, JSON.stringify(session, null, 2));
      }
      
      this.logger.info(`🔄 Backup restaurado: ${backupData.sessionsCount} sessões`);
      
    } catch (error) {
      this.logger.error('❌ Erro ao restaurar backup:', error);
      throw error;
    }
  }

  async cleanupOldSessions() {
    try {
      const now = new Date();
      let cleanedCount = 0;
      
      for (const [sessionId, session] of this.sessions.entries()) {
        const lastUpdate = new Date(session.lastUpdated);
        const daysDiff = (now - lastUpdate) / (1000 * 60 * 60 * 24);
        
        // Remover sessões inativas há mais de 30 dias
        if (daysDiff > 30) {
          await this.deleteSession(sessionId);
          cleanedCount++;
        }
      }
      
      if (cleanedCount > 0) {
        this.logger.info(`🧹 Limpeza concluída: ${cleanedCount} sessões antigas removidas`);
      }
      
    } catch (error) {
      this.logger.error('❌ Erro na limpeza de sessões:', error);
    }
  }

  async cleanupOldBackups() {
    try {
      const backupFiles = await fs.readdir(this.backupPath);
      const now = new Date();
      let cleanedCount = 0;
      
      for (const file of backupFiles) {
        if (file.startsWith('sessions_backup_') && file.endsWith('.json')) {
          const filePath = path.join(this.backupPath, file);
          const stats = await fs.stat(filePath);
          const daysDiff = (now - stats.mtime) / (1000 * 60 * 60 * 24);
          
          // Remover backups com mais de 90 dias
          if (daysDiff > 90) {
            await fs.unlink(filePath);
            cleanedCount++;
          }
        }
      }
      
      if (cleanedCount > 0) {
        this.logger.info(`🧹 Limpeza de backups: ${cleanedCount} arquivos antigos removidos`);
      }
      
    } catch (error) {
      this.logger.error('❌ Erro na limpeza de backups:', error);
    }
  }

  async getBackupList() {
    try {
      const backupFiles = await fs.readdir(this.backupPath);
      const backups = [];
      
      for (const file of backupFiles) {
        if (file.startsWith('sessions_backup_') && file.endsWith('.json')) {
          const filePath = path.join(this.backupPath, file);
          const stats = await fs.stat(filePath);
          
          backups.push({
            filename: file,
            size: stats.size,
            created: stats.mtime,
            path: filePath
          });
        }
      }
      
      // Ordenar por data de criação (mais recente primeiro)
      backups.sort((a, b) => b.created - a.created);
      
      return backups;
      
    } catch (error) {
      this.logger.error('❌ Erro ao listar backups:', error);
      return [];
    }
  }

  async cleanup() {
    try {
      this.logger.info('🧹 Limpando Session Manager...');
      
      // Salvar todas as sessões ativas
      for (const [sessionId, session] of this.sessions.entries()) {
        await this.saveSession(sessionId, session);
      }
      
      // Criar backup final
      if (this.backupEnabled) {
        await this.createBackup();
      }
      
      this.logger.info('✅ Session Manager limpo com sucesso');
      
    } catch (error) {
      this.logger.error('❌ Erro na limpeza do Session Manager:', error);
    }
  }

  getStats() {
    const activeSessions = Array.from(this.sessions.values()).filter(session => {
      const lastUpdate = new Date(session.lastUpdated);
      const now = new Date();
      const hoursDiff = (now - lastUpdate) / (1000 * 60 * 60);
      return hoursDiff < 24;
    });

    return {
      totalSessions: this.sessions.size,
      activeSessions: activeSessions.length,
      isInitialized: this.isInitialized,
      backupEnabled: this.backupEnabled,
      backupInterval: this.backupInterval,
      lastCleanup: new Date().toISOString()
    };
  }

  // ===== MÉTODOS DE CONVERSAS =====

  // Salvar informações de um chat
  async saveChat(sessionName, chatInfo) {
    try {
      // Validar dados de entrada
      if (!sessionName || !chatInfo || !chatInfo.id) {
        throw new Error('Dados inválidos para salvar chat');
      }

      const chatsPath = path.join(this.sessionsPath, sessionName, 'chats');
      await fs.mkdir(chatsPath, { recursive: true });

      // Sanitizar o ID do chat para nome de arquivo seguro
      const safeId = this.sanitizeFileName(chatInfo.id);
      const chatFile = path.join(chatsPath, `${safeId}.json`);

      const chatData = {
        ...chatInfo,
        lastSync: new Date().toISOString(),
        originalId: chatInfo.id // Manter ID original
      };

      await fs.writeFile(chatFile, JSON.stringify(chatData, null, 2));
      this.logger.debug(`💾 Chat salvo: ${chatInfo.name} (ID: ${safeId})`);

    } catch (error) {
      this.logger.error(`❌ Erro ao salvar chat ${chatInfo?.name || 'DESCONHECIDO'}:`, {
        error: error.message,
        sessionName,
        chatId: chatInfo?.id
      });
      throw error;
    }
  }

  // Salvar mensagens de um chat
  async saveChatMessages(chatId, messages) {
    try {
      // Validar dados de entrada
      if (!chatId || !Array.isArray(messages)) {
        throw new Error('Dados inválidos para salvar mensagens');
      }

      const messagesPath = path.join(this.sessionsPath, 'messages');
      await fs.mkdir(messagesPath, { recursive: true });

      // Sanitizar o ID do chat para nome de arquivo seguro
      const safeId = this.sanitizeFileName(chatId);
      const messagesFile = path.join(messagesPath, `${safeId}.json`);

      // Carregar mensagens existentes se houver
      let existingMessages = [];
      try {
        const existingData = await fs.readFile(messagesFile, 'utf8');
        existingMessages = JSON.parse(existingData);
        if (!Array.isArray(existingMessages)) {
          existingMessages = [];
        }
      } catch {
        // Arquivo não existe ou está corrompido, começar com array vazio
        existingMessages = [];
      }

      // Filtrar mensagens válidas
      const validMessages = messages.filter(msg => msg && msg.id);

      if (validMessages.length === 0) {
        this.logger.debug(`⚠️ Nenhuma mensagem válida para salvar no chat ${safeId}`);
        return;
      }

      // Mesclar mensagens (evitar duplicatas)
      const messageIds = new Set(existingMessages.map(msg => msg.id));
      const newMessages = validMessages.filter(msg => !messageIds.has(msg.id));

      if (newMessages.length > 0) {
        const allMessages = [...existingMessages, ...newMessages]
          .sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0))
          .slice(-500); // Manter as últimas 500 mensagens para mais histórico

        await fs.writeFile(messagesFile, JSON.stringify(allMessages, null, 2));
        this.logger.debug(`💾 ${newMessages.length} novas mensagens salvas para chat ${safeId}`);
      } else {
        this.logger.debug(`ℹ️ Nenhuma mensagem nova para chat ${safeId}`);
      }

    } catch (error) {
      this.logger.error(`❌ Erro ao salvar mensagens do chat ${chatId}:`, {
        error: error.message,
        messagesCount: messages?.length || 0
      });
      throw error;
    }
  }

  // Obter chats de uma sessão
  async getChats(sessionName) {
    try {
      const chatsPath = path.join(this.sessionsPath, sessionName, 'chats');

      try {
        const chatFiles = await fs.readdir(chatsPath);
        const chats = [];

        for (const file of chatFiles) {
          if (file.endsWith('.json')) {
            const chatData = await fs.readFile(path.join(chatsPath, file), 'utf8');
            chats.push(JSON.parse(chatData));
          }
        }

        return chats.sort((a, b) => b.timestamp - a.timestamp);

      } catch {
        return []; // Diretório não existe
      }

    } catch (error) {
      this.logger.error('❌ Erro ao obter chats:', error);
      return [];
    }
  }

  // Obter mensagens de um chat
  async getChatMessages(chatId, limit = 100) {
    try {
      // Sanitizar o ID do chat para nome de arquivo seguro
      const safeId = this.sanitizeFileName(chatId);
      const messagesFile = path.join(this.sessionsPath, 'messages', `${safeId}.json`);

      try {
        const messagesData = await fs.readFile(messagesFile, 'utf8');
        const messages = JSON.parse(messagesData);

        return messages
          .sort((a, b) => b.timestamp - a.timestamp)
          .slice(0, limit);

      } catch {
        return []; // Arquivo não existe
      }

    } catch (error) {
      this.logger.error('❌ Erro ao obter mensagens:', error);
      return [];
    }
  }

  // Buscar mensagens por texto
  async searchMessages(query, sessionName = null) {
    try {
      const messagesPath = path.join(this.sessionsPath, 'messages');
      const messageFiles = await fs.readdir(messagesPath);
      const results = [];

      for (const file of messageFiles) {
        if (file.endsWith('.json')) {
          const messagesData = await fs.readFile(path.join(messagesPath, file), 'utf8');
          const messages = JSON.parse(messagesData);

          const matches = messages.filter(msg =>
            msg.body && msg.body.toLowerCase().includes(query.toLowerCase())
          );

          results.push(...matches);
        }
      }

      return results
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, 100); // Limitar resultados

    } catch (error) {
      this.logger.error('❌ Erro ao buscar mensagens:', error);
      return [];
    }
  }

  // Obter estatísticas de conversas
  async getConversationStats(sessionName) {
    try {
      const chats = await this.getChats(sessionName);
      const totalChats = chats.length;
      const groupChats = chats.filter(chat => chat.isGroup).length;
      const privateChats = totalChats - groupChats;

      let totalMessages = 0;
      for (const chat of chats) {
        const messages = await this.getChatMessages(chat.id);
        totalMessages += messages.length;
      }

      return {
        totalChats,
        groupChats,
        privateChats,
        totalMessages,
        lastSync: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('❌ Erro ao obter estatísticas:', error);
      return {
        totalChats: 0,
        groupChats: 0,
        privateChats: 0,
        totalMessages: 0,
        lastSync: new Date().toISOString()
      };
    }
  }

  // Método utilitário para sanitizar nomes de arquivo
  sanitizeFileName(fileName) {
    if (!fileName) return 'unknown';

    return fileName
      .replace(/[<>:"/\\|?*]/g, '_') // Substituir caracteres inválidos
      .replace(/\s+/g, '_') // Substituir espaços por underscore
      .replace(/_{2,}/g, '_') // Remover underscores duplos
      .substring(0, 100) // Limitar tamanho
      .replace(/^_+|_+$/g, ''); // Remover underscores do início e fim
  }
}
