
import express from 'express';
import { RAGService } from '../services/RAGService.js';

const router = express.Router();
const ragService = new RAGService();

router.post('/query', async (req, res) => {
  try {
    const { query } = req.body;
    const response = await ragService.processMessage(query);
    res.json(response);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

export default router;