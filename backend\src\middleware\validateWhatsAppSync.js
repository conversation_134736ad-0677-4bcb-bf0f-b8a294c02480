const Joi = require('joi');

const syncSchema = Joi.object({
  newOnly: Joi.boolean().required()
});

module.exports = (req, res, next) => {
  const { error } = syncSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }
  next();
};

// Uso no router:
// File: /backend/src/routes/whatsapp.js
const validateWhatsAppSync = require('../middleware/validateWhatsAppSync');

router.post('/sync', validateWhatsAppSync, async (req, res) => {
  // ... lógica de sincronização
});