
// Adicionar ao método existente
async processMultimodalMessage(message, contact) {
  try {
    let processedContent = '';
    
    // Processar diferentes tipos de mídia
    if (message.type === 'image') {
      const imageText = await this.audioProcessor.processImage(message.body);
      processedContent = `[IMAGEM RECEBIDA]: ${imageText}`;
    } else if (message.type === 'audio') {
      const audioText = await this.audioProcessor.processAudioMessage(message.body);
      processedContent = `[ÁUDIO TRANSCRITO]: ${audioText.text}`;
    } else if (message.type === 'document') {
      const docText = await this.documentProcessor.processFile(message.body);
      processedContent = `[DOCUMENTO]: ${docText.text}`;
    } else {
      processedContent = message.body;
    }
    
    // Gerar resposta contextual
    return await this.generateContextualResponse(processedContent, contact);
  } catch (error) {
    this.logger.error('❌ Erro no processamento multimodal:', error);
    return this.getDefaultResponse();
  }
}
