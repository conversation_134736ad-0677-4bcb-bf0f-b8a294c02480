export interface Document {
  id: string;
  title: string;
  content: string;
  file_path?: string;
  file_type: string;
  file_size?: number;
  upload_date: Date;
  processed_at?: Date;
  status: 'pending' | 'processing' | 'completed' | 'error';
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

export interface DocumentChunk {
  id: string;
  document_id: string;
  chunk_text: string;
  chunk_index: number;
  embedding?: number[];
  metadata: Record<string, any>;
  created_at: Date;
}

export interface RAGSearchResult {
  chunk: DocumentChunk;
  document: Document;
  similarity_score: number;
}

export interface RAGResponse {
  answer: string;
  sources: RAGSearchResult[];
  confidence_score: number;
  processing_time: number;
}

export interface ConversationContext {
  conversation_id: string;
  recent_messages: ConversationMessage[];
  relevant_documents: string[];
  user_preferences: Record<string, any>;
}

export interface ConversationMessage {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  sources: RAGSearchResult[];
  metadata: Record<string, any>;
  created_at: Date;
}