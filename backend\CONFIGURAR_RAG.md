# 🧠 Configuração Completa do RAG - Vereadora Rafaela

Este guia mostra como configurar completamente o sistema RAG com Gemini AI e Supabase.

## 🎯 Status Atual

✅ **Servidor Aprimorado Funcionando**
- Servidor básico: ✅ Operacional
- Arquitetura RAG: ✅ Implementada
- Endpoints: ✅ Funcionando
- Fallbacks: ✅ Configurados

⚠️ **Pendente de Configuração**
- Gemini AI: ❌ API Key necessária
- Supabase: ❌ Configuração necessária

## 🔧 Passo 1: Configurar Gemini AI

### 1.1. Obter API Key do Gemini

1. **Acesse o Google AI Studio:**
   ```
   https://makersuite.google.com/app/apikey
   ```

2. **Criar Nova API Key:**
   - Clique em "Create API Key"
   - Selecione um projeto do Google Cloud
   - Copie a API key gerada

3. **Configurar no Backend:**
   ```bash
   # No arquivo .env
   GEMINI_API_KEY=sua-api-key-aqui
   GEMINI_MODEL=gemini-1.5-flash
   ```

### 1.2. Testar Gemini

```bash
# Reiniciar servidor
node server-enhanced.js

# Testar endpoint
curl -X POST http://localhost:3001/api/rag/query \
  -H "Content-Type: application/json" \
  -d '{"query":"Olá Vereadora, como você pode me ajudar?"}'
```

## 🗄️ Passo 2: Configurar Supabase

### 2.1. Criar Projeto Supabase

1. **Acesse Supabase:**
   ```
   https://supabase.com/dashboard
   ```

2. **Criar Novo Projeto:**
   - Clique em "New Project"
   - Nome: "vereadora-rafaela-rag"
   - Região: "South America (São Paulo)"
   - Senha do banco: (anote com segurança)

3. **Obter Credenciais:**
   - Vá em Settings > API
   - Copie: Project URL e anon/public key

### 2.2. Configurar Tabelas

Execute no SQL Editor do Supabase:

```sql
-- Tabela de documentos
CREATE TABLE documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  file_path TEXT,
  file_type TEXT,
  file_size INTEGER,
  status TEXT DEFAULT 'processing',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE
);

-- Tabela de chunks de documentos
CREATE TABLE document_chunks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
  chunk_text TEXT NOT NULL,
  chunk_index INTEGER NOT NULL,
  embedding VECTOR(384), -- Para embeddings
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de conversas
CREATE TABLE conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT,
  phone_number TEXT,
  recent_messages JSONB DEFAULT '[]',
  context JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_conversations_phone_number ON conversations(phone_number);

-- RLS (Row Level Security) - opcional
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_chunks ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;

-- Políticas básicas (permitir tudo por enquanto)
CREATE POLICY "Allow all operations" ON documents FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON document_chunks FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON conversations FOR ALL USING (true);
```

### 2.3. Configurar no Backend

```bash
# No arquivo .env
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_ANON_KEY=sua-anon-key-aqui
SUPABASE_SERVICE_KEY=sua-service-key-aqui
```

## 🚀 Passo 3: Testar Sistema Completo

### 3.1. Reiniciar Servidor

```bash
# Parar servidor atual (Ctrl+C)
# Reiniciar com configurações
node server-enhanced.js
```

### 3.2. Verificar Status

```bash
# Health check
curl http://localhost:3001/api/health

# Configuração
curl http://localhost:3001/api/config

# Documentos (Supabase)
curl http://localhost:3001/api/documents
```

### 3.3. Testar RAG com Gemini

```bash
# Query com IA real
curl -X POST http://localhost:3001/api/rag/query \
  -H "Content-Type: application/json" \
  -d '{"query":"Olá Vereadora Rafaela, quais são suas principais propostas para Parnamirim?"}'
```

## 📊 Passo 4: Monitoramento

### 4.1. Logs do Servidor

O servidor mostrará:
```
🧠 Integrações:
   Gemini AI: ✅ Ativo
   Supabase: ✅ Ativo
```

### 4.2. Verificar Funcionamento

1. **Gemini AI:**
   - Respostas personalizadas da Vereadora Rafaela
   - Tempo de resposta < 3 segundos
   - Uso de emojis e linguagem calorosa

2. **Supabase:**
   - Documentos sendo listados
   - Conexão com banco estabelecida
   - Tabelas criadas corretamente

## 🔧 Passo 5: Configurações Avançadas

### 5.1. Personalizar Prompts

Edite o método `buildRafaelaPrompt()` em `server-enhanced.js` para:
- Adicionar mais informações sobre a vereadora
- Incluir dados específicos de Parnamirim
- Personalizar o tom das respostas

### 5.2. Configurar Cache

```bash
# No arquivo .env
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
```

### 5.3. Configurar Rate Limiting

```bash
# No arquivo .env
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
```

## 🎉 Resultado Final

Com tudo configurado, você terá:

✅ **Sistema RAG Completo**
- IA conversacional com Gemini
- Banco de dados com Supabase
- Respostas personalizadas da Vereadora Rafaela
- Cache inteligente
- Monitoramento em tempo real

✅ **Pronto para Produção**
- Configurações de segurança
- Rate limiting
- Logs estruturados
- Backup automático

## 🆘 Solução de Problemas

### Gemini não funciona:
- Verificar API key válida
- Verificar cotas da API
- Verificar conexão com internet

### Supabase não conecta:
- Verificar URL e keys corretas
- Verificar tabelas criadas
- Verificar políticas RLS

### Servidor não inicia:
- Verificar dependências instaladas
- Verificar arquivo .env
- Verificar logs de erro

## 📞 Próximos Passos

1. ✅ Configurar Gemini e Supabase
2. 📱 Integrar WhatsApp real
3. 📄 Adicionar processamento de documentos
4. 🚀 Deploy em produção
5. 📊 Configurar monitoramento avançado
