import express from 'express';
import { authenticate<PERSON><PERSON><PERSON><PERSON>, generate<PERSON><PERSON><PERSON><PERSON>, revoke<PERSON><PERSON><PERSON><PERSON>, listApi<PERSON>eys } from '../middleware/auth.js';
import { Logger } from '../utils/Logger.js';

const router = express.Router();
const logger = new Logger();

// GET /api/security/status - Status de segurança
router.get('/status', authenticateApi<PERSON>ey(['security:read']), async (req, res) => {
  try {
    const securityStatus = {
      authentication: {
        enabled: true,
        methods: ['api-key', 'jwt'],
        rateLimiting: true,
        originVerification: true
      },
      encryption: {
        sessionData: true,
        algorithm: 'AES-256-GCM',
        keyRotation: false
      },
      monitoring: {
        auditLogs: true,
        securityEvents: true,
        intrusionDetection: false
      },
      backup: {
        encrypted: true,
        automated: true,
        retention: '30 days'
      },
      compliance: {
        dataProtection: true,
        accessControl: true,
        auditTrail: true
      }
    };

    res.json({
      success: true,
      data: securityStatus,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter status de segurança:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível obter status de segurança',
      code: 'SECURITY_STATUS_ERROR'
    });
  }
});

// GET /api/security/api-keys - Listar API keys (apenas admin)
router.get('/api-keys', authenticateApiKey(['admin']), async (req, res) => {
  try {
    const apiKeys = listApiKeys();
    
    res.json({
      success: true,
      data: {
        keys: apiKeys,
        total: apiKeys.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao listar API keys:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível listar API keys',
      code: 'API_KEYS_LIST_ERROR'
    });
  }
});

// POST /api/security/api-keys - Gerar nova API key (apenas admin)
router.post('/api-keys', authenticateApiKey(['admin']), async (req, res) => {
  try {
    const { name, permissions = [] } = req.body;

    if (!name) {
      return res.status(400).json({
        error: 'Nome obrigatório',
        message: 'O campo name é obrigatório',
        code: 'VALIDATION_ERROR'
      });
    }

    const apiKey = generateApiKey(name, permissions);

    logger.security('API key generated', {
      name,
      permissions,
      generatedBy: req.auth.keyName,
      ip: req.ip
    });

    res.json({
      success: true,
      data: {
        name,
        apiKey,
        permissions,
        warning: 'Guarde esta chave em local seguro. Ela não será mostrada novamente.'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao gerar API key:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível gerar API key',
      code: 'API_KEY_GENERATION_ERROR'
    });
  }
});

// DELETE /api/security/api-keys/:name - Revogar API key (apenas admin)
router.delete('/api-keys/:name', authenticateApiKey(['admin']), async (req, res) => {
  try {
    const { name } = req.params;

    if (name === req.auth.keyName) {
      return res.status(400).json({
        error: 'Operação não permitida',
        message: 'Não é possível revogar sua própria API key',
        code: 'SELF_REVOCATION_ERROR'
      });
    }

    const revoked = revokeApiKey(name);

    if (revoked) {
      logger.security('API key revoked', {
        name,
        revokedBy: req.auth.keyName,
        ip: req.ip
      });

      res.json({
        success: true,
        message: 'API key revogada com sucesso',
        data: { name },
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(404).json({
        error: 'API key não encontrada',
        message: `API key '${name}' não existe`,
        code: 'API_KEY_NOT_FOUND'
      });
    }

  } catch (error) {
    logger.error('Erro ao revogar API key:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível revogar API key',
      code: 'API_KEY_REVOCATION_ERROR'
    });
  }
});

// GET /api/security/audit-logs - Logs de auditoria (apenas admin)
router.get('/audit-logs', authenticateApiKey(['admin']), async (req, res) => {
  try {
    const { limit = 100, offset = 0, level = 'all' } = req.query;

    // Em uma implementação real, isso viria de um banco de dados
    const auditLogs = [
      {
        id: '1',
        timestamp: new Date().toISOString(),
        level: 'info',
        event: 'api_access',
        details: {
          endpoint: '/api/whatsapp/status',
          method: 'GET',
          ip: '*************',
          userAgent: 'Mozilla/5.0...',
          auth: 'frontend'
        }
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 60000).toISOString(),
        level: 'security',
        event: 'api_key_generated',
        details: {
          name: 'mobile-app',
          permissions: ['whatsapp:read'],
          generatedBy: 'admin',
          ip: '*************'
        }
      }
    ];

    const filteredLogs = level === 'all' 
      ? auditLogs 
      : auditLogs.filter(log => log.level === level);

    const paginatedLogs = filteredLogs.slice(offset, offset + limit);

    res.json({
      success: true,
      data: {
        logs: paginatedLogs,
        total: filteredLogs.length,
        limit: parseInt(limit),
        offset: parseInt(offset)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter logs de auditoria:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível obter logs de auditoria',
      code: 'AUDIT_LOGS_ERROR'
    });
  }
});

// GET /api/security/threats - Detecção de ameaças (apenas admin)
router.get('/threats', authenticateApiKey(['admin']), async (req, res) => {
  try {
    // Simulação de detecção de ameaças
    const threats = [
      {
        id: '1',
        type: 'rate_limit_exceeded',
        severity: 'medium',
        source: '192.168.1.200',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        details: {
          requests: 150,
          timeWindow: '1 minute',
          endpoint: '/api/whatsapp/send'
        },
        status: 'blocked'
      },
      {
        id: '2',
        type: 'invalid_api_key',
        severity: 'high',
        source: '10.0.0.50',
        timestamp: new Date(Date.now() - 600000).toISOString(),
        details: {
          attempts: 5,
          endpoint: '/api/whatsapp/status',
          userAgent: 'curl/7.68.0'
        },
        status: 'monitoring'
      }
    ];

    res.json({
      success: true,
      data: {
        threats,
        summary: {
          total: threats.length,
          high: threats.filter(t => t.severity === 'high').length,
          medium: threats.filter(t => t.severity === 'medium').length,
          low: threats.filter(t => t.severity === 'low').length,
          blocked: threats.filter(t => t.status === 'blocked').length
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter ameaças:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível obter informações de ameaças',
      code: 'THREATS_ERROR'
    });
  }
});

// POST /api/security/backup - Criar backup de segurança (apenas admin)
router.post('/backup', authenticateApiKey(['admin']), async (req, res) => {
  try {
    const persistenceService = req.app.locals.persistenceService;
    
    if (!persistenceService) {
      return res.status(503).json({
        error: 'Serviço não disponível',
        message: 'Persistence Service não está disponível',
        code: 'SERVICE_UNAVAILABLE'
      });
    }

    const result = await persistenceService.createBackup();

    if (result.success) {
      logger.security('Security backup created', {
        sessionsCount: result.sessionsCount,
        backupDir: result.backupDir,
        requestedBy: req.auth.keyName,
        ip: req.ip
      });

      res.json({
        success: true,
        message: 'Backup de segurança criado com sucesso',
        data: {
          sessionsCount: result.sessionsCount,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.status(500).json({
        error: 'Falha no backup',
        message: result.error,
        code: 'BACKUP_FAILED'
      });
    }

  } catch (error) {
    logger.error('Erro ao criar backup de segurança:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível criar backup de segurança',
      code: 'BACKUP_ERROR'
    });
  }
});

// GET /api/security/integrity - Verificação de integridade (apenas admin)
router.get('/integrity', authenticateApiKey(['admin']), async (req, res) => {
  try {
    const persistenceService = req.app.locals.persistenceService;
    
    if (!persistenceService) {
      return res.status(503).json({
        error: 'Serviço não disponível',
        message: 'Persistence Service não está disponível',
        code: 'SERVICE_UNAVAILABLE'
      });
    }

    const results = await persistenceService.verifyIntegrity();
    const validCount = results.filter(r => r.valid).length;
    const totalCount = results.length;

    res.json({
      success: true,
      data: {
        summary: {
          total: totalCount,
          valid: validCount,
          invalid: totalCount - validCount,
          percentage: totalCount > 0 ? Math.round((validCount / totalCount) * 100) : 100
        },
        details: results
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro na verificação de integridade:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível verificar integridade',
      code: 'INTEGRITY_CHECK_ERROR'
    });
  }
});

export default router;
