#!/usr/bin/env node

/**
 * Servidor de Teste com Gemini AI Real
 * Para testar a integração do Gemini configurado
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '.env') });

console.log('🧪 Testando Gemini AI Real...');

const app = express();
const port = 3002;

// Middlewares básicos
app.use(cors());
app.use(express.json());

// Inicializar Gemini
let genAI = null;
let model = null;

async function initializeGemini() {
  try {
    console.log('🧠 Inicializando Gemini AI...');
    
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY não configurada');
    }

    genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    model = genAI.getGenerativeModel({ 
      model: 'gemini-1.5-flash',
      generationConfig: {
        temperature: 0.8,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      }
    });

    // Testar conexão
    const testResult = await model.generateContent('Responda apenas "OK" se você está funcionando.');
    const response = testResult.response.text();
    
    if (!response) {
      throw new Error('Resposta vazia do Gemini');
    }
    
    console.log('✅ Gemini AI inicializado com sucesso!');
    console.log(`📝 Teste: ${response}`);
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao inicializar Gemini:', error.message);
    return false;
  }
}

// Função para gerar resposta da Vereadora Rafaela
async function generateRafaelaResponse(userMessage) {
  if (!model) {
    return 'Olá! Sou a Vereadora Rafaela de Nilda. Como posso ajudá-lo hoje? 🏛️';
  }

  try {
    const prompt = `Você é a Vereadora Rafaela de Nilda, eleita para representar o povo de Parnamirim/RN no período 2025-2028.

PERSONALIDADE:
- Carinhosa, atenciosa e próxima do povo
- Usa emojis apropriados: 🙏🏽💖🤩😍👏🏛️
- Linguagem acessível e calorosa
- Sempre prestativa e solicita

INFORMAÇÕES:
- Nome: Vereadora Rafaela de Nilda
- Município: Parnamirim/RN
- Mandato: 2025-2028
- Partido: SOLIDARIEDADE

INSTRUÇÕES:
1. Responda como se fosse a própria vereadora
2. Seja calorosa e próxima do povo
3. Use linguagem simples e acessível
4. Inclua emojis apropriados
5. Mantenha respostas concisas (máximo 2 parágrafos)
6. Sempre ofereça ajuda concreta

MENSAGEM DO CIDADÃO: "${userMessage}"

Responda de forma calorosa e prestativa:`;

    const result = await model.generateContent(prompt);
    const response = result.response.text();
    
    return response || 'Desculpe, tive um problema técnico. Como posso ajudá-lo? 🙏🏽';
    
  } catch (error) {
    console.error('❌ Erro ao gerar resposta:', error);
    return 'Desculpe, tive um problema técnico. Como posso ajudá-lo? 🙏🏽';
  }
}

// Rotas
app.get('/', (req, res) => {
  res.json({
    message: '🧪 Servidor de Teste - Gemini AI Real',
    status: 'online',
    gemini: !!model,
    timestamp: new Date().toISOString()
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    gemini: !!model,
    timestamp: new Date().toISOString()
  });
});

app.post('/api/test-gemini', async (req, res) => {
  try {
    const { query } = req.body;
    
    if (!query) {
      return res.status(400).json({
        error: 'Query é obrigatória'
      });
    }

    console.log(`🧠 Processando: ${query}`);
    const startTime = Date.now();
    
    const response = await generateRafaelaResponse(query);
    const processingTime = Date.now() - startTime;
    
    console.log(`✅ Resposta gerada em ${processingTime}ms`);
    
    res.json({
      query,
      response,
      metadata: {
        mode: model ? 'gemini_real' : 'simulated',
        model: 'gemini-1.5-flash',
        processingTime,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('❌ Erro na query:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
});

// Inicializar servidor
async function start() {
  // Inicializar Gemini primeiro
  const geminiSuccess = await initializeGemini();
  
  // Iniciar servidor HTTP
  app.listen(port, () => {
    console.log('');
    console.log('🎉 ================================');
    console.log('🧪  SERVIDOR DE TESTE INICIADO!');
    console.log('🎉 ================================');
    console.log('');
    console.log(`📍 Porta: ${port}`);
    console.log(`🌐 URL: http://localhost:${port}`);
    console.log(`🧠 Gemini AI: ${geminiSuccess ? '✅ Funcionando' : '❌ Erro'}`);
    console.log('');
    console.log('🎯 Endpoints de teste:');
    console.log(`   GET  /                    - Status do servidor`);
    console.log(`   GET  /api/health          - Health check`);
    console.log(`   POST /api/test-gemini     - Testar Gemini AI`);
    console.log('');
    console.log('🧪 Para testar:');
    console.log('curl -X POST http://localhost:3002/api/test-gemini \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"query":"Olá Vereadora Rafaela!"}\'');
    console.log('');
  });
}

start();
