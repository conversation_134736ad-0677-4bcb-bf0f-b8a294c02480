import { useState, useEffect } from 'react';
import api from '../services/api';

export const useWhatsAppSync = (interval = 60000) => {
  const [syncStatus, setSyncStatus] = useState({ lastSync: null, error: null });

  const syncWhatsApp = async () => {
    try {
      const response = await api.post('/whatsapp/sync', { newOnly: false });
      setSyncStatus({ lastSync: new Date(), error: null });
      return response.data;
    } catch (error) {
      setSyncStatus(prev => ({ ...prev, error: error.message }));
      throw error;
    }
  };

  useEffect(() => {
    const timer = setInterval(syncWhatsApp, interval);
    return () => clearInterval(timer);
  }, [interval]);

  return { syncStatus, syncWhatsApp };
};