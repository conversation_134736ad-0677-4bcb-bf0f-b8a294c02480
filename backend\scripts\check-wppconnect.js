#!/usr/bin/env node

import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class WPPConnectChecker {
  constructor() {
    this.containerName = 'wppconnect-server';
    this.serverUrl = 'http://localhost:21465';
    this.composeFile = path.join(__dirname, '../../docker-compose.wppconnect.yml');
  }

  async checkServerHealth() {
    try {
      const { default: fetch } = await import('node-fetch');
      const response = await fetch(`${this.serverUrl}/api/health`, {
        timeout: 5000
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async checkContainer() {
    try {
      const { stdout } = await execAsync(`docker ps --filter name=${this.containerName} --format "{{.Names}}"`);
      return stdout.trim().includes(this.containerName);
    } catch (error) {
      return false;
    }
  }

  async startWPPConnect() {
    console.log('🚀 Iniciando WPPConnect Server...');
    
    try {
      // Verificar se Docker está rodando
      await execAsync('docker --version');
      
      // Verificar se docker-compose existe
      const fs = await import('fs');
      if (!fs.existsSync(this.composeFile)) {
        console.log('❌ Arquivo docker-compose.wppconnect.yml não encontrado');
        return false;
      }

      // Iniciar container
      const { stdout, stderr } = await execAsync(`docker-compose -f ${this.composeFile} up -d`);
      
      if (stderr && !stderr.includes('Creating') && !stderr.includes('Starting')) {
        console.log('❌ Erro ao iniciar WPPConnect:', stderr);
        return false;
      }

      console.log('✅ WPPConnect Server iniciado');
      
      // Aguardar servidor ficar disponível
      console.log('⏳ Aguardando servidor ficar disponível...');
      for (let i = 0; i < 30; i++) {
        if (await this.checkServerHealth()) {
          console.log('✅ WPPConnect Server está funcionando');
          return true;
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      console.log('⚠️ WPPConnect Server demorou para responder');
      return false;

    } catch (error) {
      console.log('❌ Erro ao iniciar WPPConnect:', error.message);
      return false;
    }
  }

  async checkAndStart() {
    console.log('🔍 Verificando WPPConnect Server...');

    // Verificar se o servidor está respondendo
    if (await this.checkServerHealth()) {
      console.log('✅ WPPConnect Server já está funcionando');
      return true;
    }

    // Verificar se o container está rodando
    if (await this.checkContainer()) {
      console.log('⚠️ Container está rodando mas não responde');
      console.log('🔄 Tentando reiniciar...');
      
      try {
        await execAsync(`docker-compose -f ${this.composeFile} restart`);
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        if (await this.checkServerHealth()) {
          console.log('✅ WPPConnect Server reiniciado com sucesso');
          return true;
        }
      } catch (error) {
        console.log('❌ Erro ao reiniciar:', error.message);
      }
    }

    // Tentar iniciar o servidor
    return await this.startWPPConnect();
  }

  async showStatus() {
    console.log('\n📊 Status do WPPConnect Server:');
    console.log('================================');
    
    const serverHealth = await this.checkServerHealth();
    const containerRunning = await this.checkContainer();
    
    console.log(`🌐 Servidor HTTP: ${serverHealth ? '✅ Online' : '❌ Offline'}`);
    console.log(`🐳 Container: ${containerRunning ? '✅ Rodando' : '❌ Parado'}`);
    console.log(`📡 URL: ${this.serverUrl}`);
    
    if (containerRunning) {
      try {
        const { stdout } = await execAsync(`docker logs ${this.containerName} --tail=5`);
        console.log('\n📋 Últimas 5 linhas do log:');
        console.log(stdout);
      } catch (error) {
        console.log('❌ Erro ao obter logs:', error.message);
      }
    }
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new WPPConnectChecker();
  
  const command = process.argv[2] || 'check';
  
  switch (command) {
    case 'check':
      checker.checkAndStart().then(success => {
        process.exit(success ? 0 : 1);
      });
      break;
      
    case 'status':
      checker.showStatus().then(() => {
        process.exit(0);
      });
      break;
      
    case 'start':
      checker.startWPPConnect().then(success => {
        process.exit(success ? 0 : 1);
      });
      break;
      
    default:
      console.log('Uso: node check-wppconnect.js [check|status|start]');
      process.exit(1);
  }
}

export default WPPConnectChecker;
