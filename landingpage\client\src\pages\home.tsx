import { useAuth } from "@/components/auth/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Shield, LogOut, User, BookOpen } from "lucide-react";

export default function Home() {
  const { user, logout, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-50 to-blue-50">
        <Card className="max-w-md w-full bg-white shadow-xl rounded-2xl border border-gray-100">
          <CardContent className="p-8 text-center">
            <Shield className="mx-auto h-16 w-16 text-primary mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Acesso Restrito</h2>
            <p className="text-gray-600 mb-6">
              Você precisa estar logado para acessar esta página.
            </p>
            <a
              href="/login"
              className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
            >
              Fazer Login
            </a>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Erro ao fazer logout:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="h-8 w-8 text-primary" />
              <h1 className="text-2xl font-bold text-gray-900">Painel Seguro</h1>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                asChild
                variant="outline"
                className="flex items-center space-x-2"
              >
                <a href="/instructions">
                  <BookOpen className="h-4 w-4" />
                  <span>Instruções</span>
                </a>
              </Button>
              <Button
                onClick={handleLogout}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Sair</span>
              </Button>
            </div>
          </div>
        </header>

        {/* Welcome Section */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="bg-white shadow-lg rounded-2xl border border-gray-100">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5 text-primary" />
                <span>Bem-vindo</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-2">Usuário logado:</p>
              <p className="font-medium text-gray-900">{user?.email}</p>
              <p className="text-sm text-gray-500 mt-2">
                ID: {user?.id}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-lg rounded-2xl border border-gray-100">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-green-600" />
                <span>Segurança</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-2">Status:</p>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-green-600 font-medium">Autenticado</span>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Sessão ativa e protegida
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-lg rounded-2xl border border-gray-100">
            <CardHeader className="pb-4">
              <CardTitle>Informações da Conta</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <p className="text-sm text-gray-500">Criado em:</p>
                  <p className="font-medium">
                    {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('pt-BR') : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Última atualização:</p>
                  <p className="font-medium">
                    {user?.updatedAt ? new Date(user.updatedAt).toLocaleDateString('pt-BR') : 'N/A'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Features Section */}
        <div className="mt-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Recursos Disponíveis</h2>
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="bg-white shadow-lg rounded-2xl border border-gray-100">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">Autenticação Segura</h3>
                <p className="text-gray-600 text-sm">
                  Sistema de login com validação de email e senha, sessões protegidas e cookies seguros.
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white shadow-lg rounded-2xl border border-gray-100">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">Integração Supabase</h3>
                <p className="text-gray-600 text-sm">
                  Pronto para integração com Supabase para gerenciamento de usuários e dados.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}