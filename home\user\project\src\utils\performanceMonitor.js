import { Profiler } from './profiler.js';

export class PerformanceMonitor {
  static metrics = {};
  
  static recordMetric(label, duration) {
    if (!this.metrics[label]) {
      this.metrics[label] = [];
    }
    this.metrics[label].push(duration);
  }
  
  static getAverageMetric(label) {
    const metrics = this.metrics[label];
    if (!metrics || metrics.length === 0) return 0;
    return metrics.reduce((a, b) => a + b, 0) / metrics.length;
  }
  
  static logPerformanceReport() {
    console.log('Performance Report:');
    for (const [label, metrics] of Object.entries(this.metrics)) {
      console.log(`${label}: Avg ${this.getAverageMetric(label).toFixed(2)}ms (${metrics.length} samples)`);
    }
  }
}

// Modificar o Profiler para usar o PerformanceMonitor
Profiler.endTime = (label) => {
  performance.mark(`${label}-end`);
  performance.measure(label, `${label}-start`, `${label}-end`);
  const duration = performance.getEntriesByName(label).pop().duration;
  PerformanceMonitor.recordMetric(label, duration);
};

// No arquivo server.js, adicionar um intervalo para logar o relatório
setInterval(() => PerformanceMonitor.logPerformanceReport(), 60000); // A cada minuto