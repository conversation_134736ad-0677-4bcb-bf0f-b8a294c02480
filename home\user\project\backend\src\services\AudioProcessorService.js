export class AudioProcessorService {
  constructor() {
    this.logger = new Logger();
    this.geminiService = new GeminiService();
  }
  
  async processAudioMessage(audioBuffer, format = 'ogg') {
    try {
      // Converter áudio para formato suportado pelo Gemini
      const base64Audio = audioBuffer.toString('base64');
      
      // Transcrever áudio usando Gemini
      const transcription = await this.geminiService.transcribeAudio(base64Audio, {
        language: 'pt-BR',
        context: 'municipal_services'
      });
      
      return {
        text: transcription.text,
        confidence: transcription.confidence,
        metadata: {
          duration: transcription.duration,
          language: transcription.language
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar áudio: ${error.message}`);
    }
  }
}