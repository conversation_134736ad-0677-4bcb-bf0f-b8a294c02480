import express from 'express';
import { Logger } from '../utils/Logger.js';

const router = express.Router();
const logger = new Logger();

/**
 * Webhook Routes - Recebe eventos do WPPConnect Server
 */

// Middleware de autenticação para webhook
const authenticateWebhook = (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];
  const expectedToken = process.env.WPPCONNECT_SECRET_KEY || 'vereadora-rafaela-secret-2024';
  
  if (!token || token !== expectedToken) {
    logger.warn('🚫 Tentativa de acesso não autorizada ao webhook');
    return res.status(401).json({
      success: false,
      error: 'Token de autorização inválido'
    });
  }
  
  next();
};

// Webhook para receber mensagens do WPPConnect Server
router.post('/webhook', authenticateWebhook, async (req, res) => {
  try {
    const { session, message, timestamp } = req.body;
    
    logger.info(`📡 Webhook recebido da sessão ${session}: ${message.body}`);
    
    // Verificar se a mensagem não é nossa (evitar loop)
    if (message.isFromMe) {
      logger.info('📤 Mensagem ignorada (enviada por nós)');
      return res.json({ success: true, message: 'Mensagem ignorada' });
    }
    
    // Verificar se é uma mensagem de status (ignorar)
    if (message.from.includes('status@broadcast')) {
      logger.info('📢 Status ignorado');
      return res.json({ success: true, message: 'Status ignorado' });
    }
    
    // Obter o serviço WhatsApp do app
    const whatsappService = req.app.locals.whatsappService;
    
    if (whatsappService && whatsappService.onMessage) {
      // Formatar mensagem para o formato esperado pelo MessageHandler
      const formattedMessage = {
        id: message.id,
        from: message.from,
        body: message.body,
        timestamp: message.timestamp,
        isFromMe: message.isFromMe,
        notifyName: message.notifyName,
        type: message.type || 'chat'
      };
      
      // Processar mensagem através do MessageHandler
      await whatsappService.onMessage(formattedMessage);
      
      logger.info('✅ Mensagem processada com sucesso');
    } else {
      logger.warn('⚠️ WhatsApp Service não disponível para processar mensagem');
    }
    
    res.json({
      success: true,
      message: 'Webhook processado com sucesso',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('❌ Erro ao processar webhook:', error.message);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

// Webhook para eventos de status da sessão
router.post('/webhook/status', authenticateWebhook, async (req, res) => {
  try {
    const { session, status, qrCode } = req.body;
    
    logger.info(`📊 Status da sessão ${session}: ${status}`);
    
    // Obter o serviço WhatsApp do app
    const whatsappService = req.app.locals.whatsappService;
    
    if (whatsappService) {
      // Atualizar status interno
      if (status === 'isLogged') {
        whatsappService.isConnected = true;
        whatsappService.qrCode = null;
        
        if (whatsappService.onReady) {
          whatsappService.onReady();
        }
        
        if (whatsappService.onStatusChange) {
          whatsappService.onStatusChange('connected');
        }
        
      } else if (status === 'WAITING_QR' && qrCode) {
        whatsappService.isConnected = false;
        whatsappService.qrCode = qrCode;
        
        if (whatsappService.onQRCode) {
          whatsappService.onQRCode(qrCode);
        }
        
        if (whatsappService.onStatusChange) {
          whatsappService.onStatusChange('waiting_qr');
        }
        
      } else if (status === 'DISCONNECTED') {
        whatsappService.isConnected = false;
        whatsappService.qrCode = null;
        
        if (whatsappService.onDisconnected) {
          whatsappService.onDisconnected();
        }
        
        if (whatsappService.onStatusChange) {
          whatsappService.onStatusChange('disconnected');
        }
      }
    }
    
    res.json({
      success: true,
      message: 'Status atualizado com sucesso'
    });
    
  } catch (error) {
    logger.error('❌ Erro ao processar webhook de status:', error.message);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

// Health check do webhook
router.get('/webhook/health', (req, res) => {
  res.json({
    success: true,
    message: 'Webhook está funcionando',
    timestamp: new Date().toISOString()
  });
});

export default router;
