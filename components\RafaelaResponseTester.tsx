import React, { useState } from 'react';

interface TestResult {
  originalMessage: string;
  contact: any;
  shouldAutoRespond: boolean;
  response: string;
  personalizedResponse: string;
  stats: any;
}

export const RafaelaResponseTester: React.FC = () => {
  const [message, setMessage] = useState('');
  const [contactName, setContactName] = useState('');
  const [result, setResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Exemplos de mensagens para teste
  const examples = [
    { label: 'Saudação sem contato', message: 'Bom dia' },
    { label: 'Pedido de cesta básica', message: 'Preciso de uma cesta básica' },
    { label: 'Medicação', message: 'Preciso de medicamento para pressão' },
    { label: 'Emprego', message: 'Tem alguma vaga de emprego?' },
    { label: 'Cirurgia', message: 'Preciso marcar uma cirurgia' },
    { label: 'Canal dentário', message: 'Preciso fazer um canal' },
    { label: 'Mensagem de Deus', message: 'Bom dia, que Deus abençoe' },
    { label: 'Agenda', message: 'Quero marcar uma reunião' },
    { label: 'Preciso falar', message: 'Vereadora, preciso falar contigo' },
    { label: 'Gratidão', message: 'Muito obrigada pela ajuda' }
  ];

  const testResponse = async () => {
    if (!message.trim()) {
      setError('Digite uma mensagem para testar');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const contact = contactName.trim() ? { name: contactName.trim() } : null;

      const response = await fetch('http://localhost:3001/api/whatsapp/test-rafaela', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message.trim(),
          contact
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao testar resposta');
      }

      const data = await response.json();
      setResult(data.data);

    } catch (error) {
      console.error('Erro ao testar:', error);
      setError('Erro ao conectar com o servidor');
    } finally {
      setLoading(false);
    }
  };

  const useExample = (exampleMessage: string) => {
    setMessage(exampleMessage);
    setResult(null);
    setError(null);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-2">
          🤖 Testador de Respostas da Vereadora Rafaela
        </h3>
        <p className="text-gray-600 text-sm">
          Teste como a Vereadora Rafaela responderia a diferentes tipos de mensagens
        </p>
      </div>

      {/* Formulário de teste */}
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nome do contato (opcional):
          </label>
          <input
            type="text"
            value={contactName}
            onChange={(e) => setContactName(e.target.value)}
            placeholder="Ex: João Silva"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Mensagem para testar:
          </label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Digite a mensagem que você quer testar..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        <div className="flex space-x-3">
          <button
            onClick={testResponse}
            disabled={loading || !message.trim()}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {loading ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                <span>Testando...</span>
              </>
            ) : (
              <>
                <span>🧪</span>
                <span>Testar Resposta</span>
              </>
            )}
          </button>

          <button
            onClick={() => {
              setMessage('');
              setContactName('');
              setResult(null);
              setError(null);
            }}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
          >
            🗑️ Limpar
          </button>
        </div>
      </div>

      {/* Exemplos */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-3">📋 Exemplos para testar:</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={() => useExample(example.message)}
              className="text-left p-2 text-xs bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded transition-colors"
            >
              <div className="font-medium text-blue-800">{example.label}</div>
              <div className="text-blue-600 truncate">"{example.message}"</div>
            </button>
          ))}
        </div>
      </div>

      {/* Erro */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-red-800 font-medium">❌ Erro</div>
          <div className="text-red-600 text-sm">{error}</div>
        </div>
      )}

      {/* Resultado */}
      {result && (
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-800">📱 Resultado do Teste:</h4>
          
          {/* Mensagem original */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="font-medium text-gray-700 mb-2">📝 Mensagem enviada:</div>
            <div className="text-gray-800">"{result.originalMessage}"</div>
            {result.contact && (
              <div className="text-sm text-gray-600 mt-1">
                👤 Contato: {result.contact.name}
              </div>
            )}
          </div>

          {/* Resposta da Rafaela */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="font-medium text-green-700 mb-2">
              💬 Resposta da Vereadora Rafaela:
            </div>
            <div className="text-green-800 bg-white p-3 rounded border">
              {result.personalizedResponse}
            </div>
          </div>

          {/* Informações técnicas */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="font-medium text-blue-700 mb-2">🔧 Informações técnicas:</div>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Auto-resposta:</span>{' '}
                <span className={result.shouldAutoRespond ? 'text-green-600' : 'text-orange-600'}>
                  {result.shouldAutoRespond ? '✅ Sim' : '⚠️ Não (usaria RAG)'}
                </span>
              </div>
              <div>
                <span className="font-medium">Resposta base:</span>{' '}
                <span className="text-gray-600">"{result.response}"</span>
              </div>
              <div>
                <span className="font-medium">Padrões disponíveis:</span>{' '}
                <span className="text-blue-600">{result.stats.totalPatterns}</span>
              </div>
              <div>
                <span className="font-medium">Emojis disponíveis:</span>{' '}
                <span className="text-blue-600">{result.stats.availableEmojis}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
