{"name": "vereadora-rafaela-rag-system", "version": "1.0.0", "description": "Sistema RAG da Vereadora Rafaela de Nilda - Chatbot Inteligente com IA", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "node test-vereadora-system.js"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@supabase/supabase-js": "^2.50.2", "axios": "^1.10.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "node-cron": "^4.2.0", "pdfjs-dist": "^4.8.69", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^24.0.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "nodemon": "^3.1.10", "postcss": "^8.5.0", "tailwindcss": "^3.4.14", "tsx": "^4.20.3", "typescript": "^5.6.3", "vite": "^6.0.1"}, "keywords": ["vereadora", "rafaela", "nilda", "parnam<PERSON>m", "rag", "chatbot", "ia", "gemini"], "author": "<PERSON><PERSON>", "license": "MIT"}