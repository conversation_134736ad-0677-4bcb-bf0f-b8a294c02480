{"name": "Sistema RAG - Vereadora Rafaela de Nilda", "description": "Configuração de exemplo para o sistema RAG", "version": "1.0.0", "supabase": {"required": true, "description": "Banco de dados para persistência", "setup_url": "https://supabase.com/dashboard", "variables": {"VITE_SUPABASE_URL": {"description": "URL do projeto Supabase", "example": "https://seu-projeto.supabase.co", "required": true}, "VITE_SUPABASE_ANON_KEY": {"description": "<PERSON>ve p<PERSON> (anon key) do Supabase", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "required": true}, "SUPABASE_SERVICE_ROLE_KEY": {"description": "Chave de serviço (opcional, para operações administrativas)", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "required": false}}, "features": ["Persistência de conversas", "Histórico de mensagens", "Armazenamento de documentos", "Busca semântica com embeddings", "Logs de interação"]}, "gemini": {"required": true, "description": "IA do Google para geração de respostas", "setup_url": "https://makersuite.google.com/app/apikey", "variables": {"VITE_GEMINI_API_KEY": {"description": "Chave da <PERSON> do Google Gemini", "example": "AIzaSyD...", "required": true}}, "features": ["Geração de respostas inteligentes", "Análise de documentos", "Criação de embeddings", "Busca semântica", "Resumos automáticos"]}, "vereadora": {"required": false, "description": "Informações específicas da Vereadora", "variables": {"VITE_VEREADORA_NAME": {"description": "Nome da Vereadora", "example": "<PERSON><PERSON>", "default": "<PERSON><PERSON>"}, "VITE_VEREADORA_TITLE": {"description": "Título/cargo", "example": "Vereadora", "default": "Vereadora"}, "VITE_MUNICIPIO": {"description": "Município de atuação", "example": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "VITE_ESTADO": {"description": "Estado", "example": "RN", "default": "RN"}, "VITE_MANDATO": {"description": "Período do mandato", "example": "2021-2024", "default": "2021-2024"}}}, "contato": {"required": false, "description": "Informações de contato do gabinete", "variables": {"VITE_GABINETE_TELEFONE": {"description": "Telefone do gabinete", "example": "(84) 99999-9999"}, "VITE_GABINETE_EMAIL": {"description": "Email do gabinete", "example": "<EMAIL>"}, "VITE_GABINETE_ENDERECO": {"description": "Endereço do gabinete", "example": "Câmara Municipal de Parnamirim"}, "VITE_GABINETE_CEP": {"description": "CEP do gabinete", "example": "59140-000"}}}, "horario": {"required": false, "description": "Configurações de horário de atendimento", "variables": {"VITE_HORARIO_INICIO": {"description": "Horário de início do atendimento", "example": "08:00", "default": "08:00"}, "VITE_HORARIO_FIM": {"description": "Hor<PERSON>rio de fim do atendimento", "example": "17:00", "default": "17:00"}, "VITE_DIAS_FUNCIONAMENTO": {"description": "Dias da semana de funcionamento (1=segunda, 7=domingo)", "example": "1,2,3,4,5", "default": "1,2,3,4,5"}}}, "redes_sociais": {"required": false, "description": "Links das redes sociais", "variables": {"VITE_FACEBOOK_URL": {"description": "URL do Facebook", "example": "https://facebook.com/VereadoraRafaelaDeNilda"}, "VITE_INSTAGRAM_URL": {"description": "URL do Instagram", "example": "https://instagram.com/rafaeladenilda"}, "VITE_TWITTER_URL": {"description": "URL do Twitter", "example": "https://twitter.com/rafaeladenilda"}, "VITE_LINKEDIN_URL": {"description": "URL do LinkedIn", "example": "https://linkedin.com/in/rafael<PERSON><PERSON>lda"}}}, "configuracoes_avancadas": {"required": false, "description": "Configurações avançadas do sistema", "variables": {"VITE_CACHE_TTL": {"description": "Tempo de cache em minutos", "example": "60", "default": "60"}, "VITE_CACHE_MAX_SIZE": {"description": "Tamanho máximo do <PERSON>", "example": "1000", "default": "1000"}, "VITE_MAX_FILE_SIZE": {"description": "Tamanho máximo de arquivo em MB", "example": "10", "default": "10"}, "VITE_ALLOWED_FILE_TYPES": {"description": "Tipos de arquivo permitidos", "example": ".pdf,.doc,.docx,.txt", "default": ".pdf,.doc,.docx,.txt"}, "VITE_RATE_LIMIT": {"description": "Limite de requisições por minuto", "example": "60", "default": "60"}, "VITE_REQUEST_TIMEOUT": {"description": "Timeout para requisições em ms", "example": "30000", "default": "30000"}}}, "tema": {"required": false, "description": "Configurações de tema e aparência", "variables": {"VITE_DEFAULT_THEME": {"description": "<PERSON><PERSON> (light, dark, auto)", "example": "auto", "default": "auto"}, "VITE_PRIMARY_COLOR": {"description": "<PERSON><PERSON> p<PERSON> (hex)", "example": "#f97316", "default": "#f97316"}, "VITE_SECONDARY_COLOR": {"description": "<PERSON><PERSON> (hex)", "example": "#6b7280", "default": "#6b7280"}}}, "debug": {"required": false, "description": "Configurações de debug e desenvolvimento", "variables": {"VITE_DEBUG_MODE": {"description": "Habilitar modo debug", "example": "false", "default": "false"}, "VITE_LOG_LEVEL": {"description": "Nível de log (error, warn, info, debug)", "example": "info", "default": "info"}, "VITE_ANALYTICS_ENABLED": {"description": "Habilitar analytics", "example": "true", "default": "true"}}}, "setup_steps": [{"step": 1, "title": "Criar projeto <PERSON>base", "description": "Acesse supabase.com e crie um novo projeto", "url": "https://supabase.com/dashboard"}, {"step": 2, "title": "Executar schema SQL", "description": "Execute o arquivo database/schema.sql no SQL Editor do Supabase"}, {"step": 3, "title": "Obter credenciais Supabase", "description": "Copie URL e anon key em Settings > API"}, {"step": 4, "title": "Criar API key Gemini", "description": "Acesse Google AI Studio e gere uma API key", "url": "https://makersuite.google.com/app/apikey"}, {"step": 5, "title": "Configurar .env", "description": "Copie .env.example para .env e configure as variáveis"}, {"step": 6, "title": "Testar configuração", "description": "Execute npm run dev e verifique o status na interface"}], "troubleshooting": {"supabase_connection_failed": {"problem": "Falha na conexão com Supabase", "solutions": ["Verificar se a URL está correta (sem barra no final)", "Confirmar se a anon key está correta", "Verificar se o projeto Supabase está ativo", "Verificar políticas RLS nas tabelas"]}, "gemini_not_responding": {"problem": "Gemini não responde", "solutions": ["Verificar se a API key está correta", "Confirmar se a API está habilitada no Google Cloud", "Verificar cotas e limites de uso", "Testar API key diretamente"]}, "embeddings_not_working": {"problem": "Embeddings não funcionam", "solutions": ["Verificar modelo de embedding (text-embedding-004)", "Confirmar dimens<PERSON><PERSON> do vetor (768)", "Verificar extensão pgvector no Supabase", "Testar geração de embedding isoladamente"]}}}