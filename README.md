# 🏛️ Sistema RAG da Vereadora Rafaela de Nilda

Sistema de Chatbot Inteligente com IA para atendimento ao cidadão de Parnamirim/RN.

## 🎯 Sobre o Projeto

Este é um sistema RAG (Retrieval-Augmented Generation) desenvolvido especificamente para automatizar o atendimento do gabinete da Vereadora Rafaela de Nilda. O sistema utiliza inteligência artificial para responder perguntas dos cidadãos com base em documentos oficiais e informações do mandato.

## ✨ Funcionalidades

- 🤖 **Chat Inteligente**: Respostas contextualizadas usando IA Gemini 2.5 Flash
- 📄 **Upload de Documentos**: Processamento automático de PDFs
- 🔍 **Busca Semântica**: Encontra informações relevantes nos documentos
- 💬 **Histórico de Conversas**: Salva e recupera conversas anteriores
- 📊 **Dashboard de Monitoramento**: Métricas e estatísticas de uso
- 📱 **Integração WhatsApp**: Envio de mensagens (simulado)
- 🎨 **Interface Moderna**: Design responsivo e intuitivo

## 🛠️ Tecnologias

- **Frontend**: React 18 + TypeScript + Tailwind CSS
- **IA**: Google Gemini 2.5 Flash
- **Banco de Dados**: Supabase (PostgreSQL)
- **Processamento PDF**: PDF.js
- **Deploy**: Netlify
- **Versionamento**: Git + GitHub

## 🚀 Como Executar

### Pré-requisitos

- Node.js 18+
- npm ou yarn
- Conta no Supabase
- API Key do Google Gemini

### Instalação

1. Clone o repositório:
```bash
git clone https://github.com/ItaloCabral1995RN/AISTUDIOCHATRAG.git
cd AISTUDIOCHATRAG
```

2. Instale as dependências:
```bash
npm install
```

3. Configure as variáveis de ambiente:
```bash
# Crie um arquivo .env com:
VITE_SUPABASE_URL=sua_url_supabase
VITE_SUPABASE_ANON_KEY=sua_chave_supabase
VITE_GEMINI_API_KEY=sua_chave_gemini
```

4. Execute o projeto:
```bash
npm run dev
```

## 📁 Estrutura do Projeto

```
AISTUDIOCHATRAG/
├── components/          # Componentes React
├── services/           # Serviços (IA, Banco, etc.)
├── config/            # Configurações
├── database/          # Schemas SQL
├── types.ts           # Tipos TypeScript
├── App.tsx           # Componente principal
└── index.tsx         # Ponto de entrada
```

## 🎨 Design

O sistema utiliza uma paleta de cores sóbria e moderna:
- **Primária**: Azul (#0ea5e9)
- **Secundária**: Roxo (#d946ef)
- **Neutros**: Cinzas diversos
- **Tipografia**: Inter (Google Fonts)

## 📊 Funcionalidades Principais

### 1. Chat Inteligente
- Processamento de linguagem natural
- Respostas contextualizadas
- Busca em documentos carregados
- Indicador de confiança das respostas

### 2. Gerenciamento de Documentos
- Upload de PDFs
- Processamento automático
- Chunking inteligente
- Embeddings vetoriais

### 3. Histórico e Persistência
- Salva todas as conversas
- Recuperação de sessões anteriores
- Busca no histórico
- Exportação de dados

### 4. Monitoramento
- Métricas de uso em tempo real
- Estatísticas de performance
- Logs de interações
- Dashboard analítico

## 🔧 Configuração do Supabase

Execute os scripts SQL da pasta `database/` para criar as tabelas necessárias:

1. `schema.sql` - Estrutura das tabelas
2. `functions.sql` - Funções auxiliares
3. `policies.sql` - Políticas de segurança

## 🌐 Deploy

O projeto está configurado para deploy automático no Netlify:

1. Conecte o repositório ao Netlify
2. Configure as variáveis de ambiente
3. O deploy será automático a cada push

## 👥 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 Contato

**Vereadora Rafaela de Nilda**
- 🏛️ Câmara Municipal de Parnamirim/RN
- 📧 <EMAIL>
- 📱 WhatsApp: (84) 99999-9999

---

Desenvolvido com ❤️ para servir melhor aos cidadãos de Parnamirim/RN
