version: '3.8'

services:
  wppconnect-server:
    image: wppconnect/server:latest
    container_name: wppconnect-server-rafaela
    ports:
      - "21465:21465"  # Porta principal da API
    environment:
      # Configurações básicas
      - PORT=21465
      - HOST=0.0.0.0
      - LOG_LEVEL=info
      - LOG_LOGGER=console
      
      # Configurações de sessão
      - SESSION_SECRET_KEY=${WPPCONNECT_SECRET_KEY:-vereadora-rafaela-secret-2024}
      - MAX_LISTENERS=15
      - WEBHOOK_URL=${WEBHOOK_URL:-http://host.docker.internal:3001/api/whatsapp/webhook}
      - WEBHOOK_BY_EVENTS=true
      - WEBHOOK_UPLOAD_S3=false
      
      # Configurações de segurança
      - AUTHENTICATION=true
      - SECRET_KEY=${WPPCONNECT_SECRET_KEY:-vereadora-rafaela-secret-2024}
      
      # Configurações do Chrome/Puppeteer
      - CHROME_ARGS=--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--disable-accelerated-2d-canvas,--no-first-run,--no-zygote,--disable-gpu
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
      
      # Configurações de sessão persistente
      - SAVE_QR_CODE=true
      - QR_CODE=true
      - WEBHOOK_ALLUNREADMESSAGES=true
      - WEBHOOK_LISTENACK=true
      - WEBHOOK_ONPRESENCECHANGED=true
      - WEBHOOK_ONPARTICIPANTSCHANGED=true
      - WEBHOOK_ONREACTIONMESSAGE=true
      - WEBHOOK_ONPOLLRESPONSE=true
      - WEBHOOK_ONREVOKEDMESSAGE=true
      - WEBHOOK_ONLABELSASSOCIATED=true
      - WEBHOOK_ONLOCATIONMESSAGE=true
      - WEBHOOK_ONMESSAGE=true
      - WEBHOOK_ONANYMESSAGE=true
      - WEBHOOK_ONMESSAGEACK=true
      - WEBHOOK_ONMESSAGEREACTION=true
      - WEBHOOK_ONMESSAGEREVOKED=true
      - WEBHOOK_ONMESSAGEDELETED=true
      - WEBHOOK_ONMESSAGECIPHERTEXT=true
      - WEBHOOK_ONMESSAGEEDIT=true
      - WEBHOOK_ONUNREADMESSAGE=true
      - WEBHOOK_ONMESSAGEUPLOAD=true
      - WEBHOOK_ONMESSAGEDOWNLOAD=true
      
    volumes:
      # Volume para persistir sessões e tokens
      - ./wppconnect-data/sessions:/app/sessions
      - ./wppconnect-data/tokens:/app/tokens
      - ./wppconnect-data/downloads:/app/downloads
      - ./wppconnect-data/uploads:/app/uploads
      
    restart: unless-stopped
    
    networks:
      - wppconnect-network
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:21465/api/vereadora-rafaela/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  wppconnect-network:
    driver: bridge

volumes:
  wppconnect-sessions:
  wppconnect-tokens:
  wppconnect-downloads:
  wppconnect-uploads:
