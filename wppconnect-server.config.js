// Configuração do WPPConnect Server para Vereadora Rafaela
module.exports = {
  // Configurações básicas do servidor
  host: process.env.WPPCONNECT_HOST || '0.0.0.0',
  port: process.env.WPPCONNECT_PORT || 21465,
  
  // Configurações de autenticação
  authentication: {
    enabled: true,
    secretKey: process.env.WPPCONNECT_SECRET_KEY || 'vereadora-rafaela-secret-2024',
    jwt: {
      secret: process.env.JWT_SECRET || 'vereadora-rafaela-jwt-2024',
      expiresIn: '7d'
    }
  },
  
  // Configurações de webhook
  webhook: {
    url: process.env.WEBHOOK_URL || 'http://localhost:3001/api/whatsapp/webhook',
    autoDownload: true,
    uploadS3: false,
    readMessage: true,
    allUnreadMessages: true,
    listenAcks: true,
    onPresenceChanged: true,
    onParticipantsChanged: true,
    onReactionMessage: true,
    onPollResponse: true,
    onRevokedMessage: true,
    onLabelsAssociated: true,
    onLocationMessage: true,
    onMessage: true,
    onAnyMessage: true,
    onMessageAck: true,
    onMessageReaction: true,
    onMessageRevoked: true,
    onMessageDeleted: true,
    onMessageCiphertext: true,
    onMessageEdit: true,
    onUnreadMessage: true,
    onMessageUpload: true,
    onMessageDownload: true
  },
  
  // Configurações de sessão
  session: {
    secretKey: process.env.WPPCONNECT_SECRET_KEY || 'vereadora-rafaela-secret-2024',
    maxListeners: 15,
    saveQrCode: true,
    qrCode: true
  },
  
  // Configurações do navegador
  browser: {
    headless: true,
    devtools: false,
    debug: false,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding'
    ]
  },
  
  // Configurações de log
  log: {
    level: process.env.LOG_LEVEL || 'info',
    logger: ['console']
  },
  
  // Configurações de arquivos
  files: {
    sessionPath: './wppconnect-data/sessions',
    tokenPath: './wppconnect-data/tokens',
    downloadPath: './wppconnect-data/downloads',
    uploadPath: './wppconnect-data/uploads'
  },
  
  // Configurações específicas para Vereadora Rafaela
  rafaela: {
    sessionName: 'vereadora-rafaela',
    autoStart: true,
    catchQR: true,
    statusFind: true,
    multiDevice: true,
    
    // Configurações anti-ban
    antiBan: {
      enabled: true,
      minDelay: 1000,
      maxDelay: 3000,
      maxMessagesPerMinute: 10,
      humanBehavior: true
    },
    
    // Configurações de backup
    backup: {
      enabled: true,
      interval: '0 2 * * *', // Todo dia às 2h
      retention: 7 // 7 dias
    }
  }
};
