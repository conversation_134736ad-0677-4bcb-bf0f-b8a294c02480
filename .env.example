# 🏛️ Sistema RAG - Vereadora Rafaela de Nilda
# Copie este arquivo para .env e configure suas variáveis

# ===== SUPABASE CONFIGURATION =====
# Obtenha essas informações em: https://supabase.com/dashboard/project/[seu-projeto]/settings/api

# URL do seu projeto Supabase
VITE_SUPABASE_URL=https://seu-projeto.supabase.co

# Chave pública (anon key) do Supabase
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Chave de serviço do Supabase (opcional, para operações administrativas)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# ===== GEMINI AI CONFIGURATION =====
# Obtenha sua API key em: https://makersuite.google.com/app/apikey

# Chave da API do Google Gemini
VITE_GEMINI_API_KEY=AIzaSyD...

# ===== CONFIGURAÇÕES DA APLICAÇÃO =====

# Ambiente de execução
NODE_ENV=development

# Informações da aplicação
VITE_APP_NAME="Vereadora Rafaela de Nilda - Assistente IA"
VITE_APP_DESCRIPTION="Sistema RAG para atendimento ao cidadão"
VITE_APP_URL=http://localhost:3000

# ===== WHATSAPP BACKEND =====
# URL do backend WhatsApp (deixe em branco para usar localhost:3001)
VITE_WHATSAPP_BACKEND_URL=http://localhost:3001

# ===== CONFIGURAÇÕES DA VEREADORA =====

# Informações básicas
VITE_VEREADORA_NAME=Rafaela de Nilda
VITE_VEREADORA_TITLE=Vereadora
VITE_MUNICIPIO=Parnamirim
VITE_ESTADO=RN
VITE_MANDATO=2021-2024

# Contato do gabinete
VITE_GABINETE_TELEFONE=(84) 99999-9999
VITE_GABINETE_EMAIL=<EMAIL>
VITE_GABINETE_ENDERECO=Câmara Municipal de Parnamirim
VITE_GABINETE_CEP=59140-000

# Redes sociais
VITE_FACEBOOK_URL=https://facebook.com/VereadoraRafaelaDeNilda
VITE_INSTAGRAM_URL=https://instagram.com/rafaeladenilda
VITE_TWITTER_URL=https://twitter.com/rafaeladenilda

# ===== CONFIGURAÇÕES DE HORÁRIO =====

# Horário de atendimento do gabinete
VITE_HORARIO_INICIO=08:00
VITE_HORARIO_FIM=17:00
VITE_DIAS_FUNCIONAMENTO=1,2,3,4,5

# ===== CONFIGURAÇÕES DE CACHE =====

# Tempo de cache em minutos
VITE_CACHE_TTL=60

# Tamanho máximo do cache
VITE_CACHE_MAX_SIZE=1000

# ===== CONFIGURAÇÕES DE UPLOAD =====

# Tamanho máximo de arquivo em MB
VITE_MAX_FILE_SIZE=10

# Tipos de arquivo permitidos
VITE_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.txt

# ===== CONFIGURAÇÕES DE DEBUG =====

# Modo de debug
VITE_DEBUG_MODE=false

# Nível de log
VITE_LOG_LEVEL=info

# Habilitar analytics
VITE_ANALYTICS_ENABLED=true

# ===== CONFIGURAÇÕES DE SEGURANÇA =====

# Rate limiting (requisições por minuto)
VITE_RATE_LIMIT=60

# Timeout para requisições em ms
VITE_REQUEST_TIMEOUT=30000

# ===== CONFIGURAÇÕES DE TEMA =====

# Tema padrão (light, dark, auto)
VITE_DEFAULT_THEME=auto

# Cores personalizadas
VITE_PRIMARY_COLOR=#f97316
VITE_SECONDARY_COLOR=#6b7280
