import express from 'express';
import { Logger } from '../utils/Logger.js';
import os from 'os';

const router = express.Router();
const logger = new Logger();

// GET /api/health - Health check básico
router.get('/', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    };
    
    res.json(health);
    
  } catch (error) {
    logger.error('Erro no health check:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/health/detailed - Health check detalhado
router.get('/detailed', async (req, res) => {
  try {
    const startTime = Date.now();
    
    // Verificar serviços
    const services = await checkServices(req.app.locals);
    
    // Informações do sistema
    const system = {
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      memory: {
        used: process.memoryUsage(),
        system: {
          total: os.totalmem(),
          free: os.freemem(),
          usage: ((os.totalmem() - os.freemem()) / os.totalmem() * 100).toFixed(2) + '%'
        }
      },
      cpu: {
        cores: os.cpus().length,
        loadAverage: os.loadavg()
      },
      uptime: {
        process: process.uptime(),
        system: os.uptime()
      }
    };
    
    // Verificar conectividade
    const connectivity = await checkConnectivity();
    
    const responseTime = Date.now() - startTime;
    
    const health = {
      status: services.overall === 'healthy' ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      services,
      system,
      connectivity,
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    };
    
    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(health);
    
  } catch (error) {
    logger.error('Erro no health check detalhado:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/health/services - Status dos serviços
router.get('/services', async (req, res) => {
  try {
    const services = await checkServices(req.app.locals);
    
    res.json({
      timestamp: new Date().toISOString(),
      services
    });
    
  } catch (error) {
    logger.error('Erro ao verificar serviços:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/health/logs - Status dos logs
router.get('/logs', async (req, res) => {
  try {
    const logStats = await logger.getLogStats();
    
    res.json({
      timestamp: new Date().toISOString(),
      logs: logStats,
      config: logger.getConfig()
    });
    
  } catch (error) {
    logger.error('Erro ao verificar logs:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Função para verificar status dos serviços
async function checkServices(appLocals) {
  const services = {
    whatsapp: { status: 'unknown', details: {} },
    rag: { status: 'unknown', details: {} },
    sessionManager: { status: 'unknown', details: {} },
    messageHandler: { status: 'unknown', details: {} }
  };
  
  // Verificar WhatsApp Service
  try {
    if (appLocals.whatsappService) {
      const whatsappStatus = appLocals.whatsappService.getConnectionStatus();
      services.whatsapp = {
        status: whatsappStatus.isConnected ? 'healthy' : 'degraded',
        details: {
          connected: whatsappStatus.isConnected,
          sessionName: whatsappStatus.sessionName,
          connectionAttempts: whatsappStatus.connectionAttempts,
          hasQRCode: !!whatsappStatus.qrCode
        }
      };
    } else {
      services.whatsapp.status = 'unavailable';
    }
  } catch (error) {
    services.whatsapp = {
      status: 'unhealthy',
      error: error.message
    };
  }
  
  // Verificar RAG Service
  try {
    if (appLocals.ragService) {
      const ragStatus = appLocals.ragService.getStatus();
      services.rag = {
        status: ragStatus.isInitialized ? 'healthy' : 'degraded',
        details: {
          initialized: ragStatus.isInitialized,
          frontendUrl: ragStatus.frontendUrl,
          ragApiUrl: ragStatus.ragApiUrl
        }
      };
    } else {
      services.rag.status = 'unavailable';
    }
  } catch (error) {
    services.rag = {
      status: 'unhealthy',
      error: error.message
    };
  }
  
  // Verificar Session Manager
  try {
    if (appLocals.sessionManager) {
      const sessionStats = appLocals.sessionManager.getStats();
      services.sessionManager = {
        status: sessionStats.isInitialized ? 'healthy' : 'degraded',
        details: sessionStats
      };
    } else {
      services.sessionManager.status = 'unavailable';
    }
  } catch (error) {
    services.sessionManager = {
      status: 'unhealthy',
      error: error.message
    };
  }
  
  // Verificar Message Handler
  try {
    if (appLocals.messageHandler) {
      const messageStats = appLocals.messageHandler.getStats();
      services.messageHandler = {
        status: 'healthy',
        details: messageStats
      };
    } else {
      services.messageHandler.status = 'unavailable';
    }
  } catch (error) {
    services.messageHandler = {
      status: 'unhealthy',
      error: error.message
    };
  }
  
  // Determinar status geral
  const statuses = Object.values(services).map(s => s.status);
  const hasUnhealthy = statuses.includes('unhealthy');
  const hasUnavailable = statuses.includes('unavailable');
  const hasDegraded = statuses.includes('degraded');
  
  let overall = 'healthy';
  if (hasUnhealthy) {
    overall = 'unhealthy';
  } else if (hasUnavailable || hasDegraded) {
    overall = 'degraded';
  }
  
  services.overall = overall;
  
  return services;
}

// Função para verificar conectividade
async function checkConnectivity() {
  const connectivity = {
    frontend: { status: 'unknown', responseTime: null },
    internet: { status: 'unknown', responseTime: null }
  };
  
  // Verificar conexão com frontend
  try {
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const startTime = Date.now();
    
    const response = await fetch(frontendUrl, {
      method: 'HEAD',
      timeout: 5000
    });
    
    const responseTime = Date.now() - startTime;
    
    connectivity.frontend = {
      status: response.ok ? 'healthy' : 'degraded',
      responseTime: `${responseTime}ms`,
      statusCode: response.status,
      url: frontendUrl
    };
    
  } catch (error) {
    connectivity.frontend = {
      status: 'unhealthy',
      error: error.message,
      url: process.env.FRONTEND_URL || 'http://localhost:3000'
    };
  }
  
  // Verificar conexão com internet
  try {
    const startTime = Date.now();
    
    const response = await fetch('https://www.google.com', {
      method: 'HEAD',
      timeout: 5000
    });
    
    const responseTime = Date.now() - startTime;
    
    connectivity.internet = {
      status: response.ok ? 'healthy' : 'degraded',
      responseTime: `${responseTime}ms`
    };
    
  } catch (error) {
    connectivity.internet = {
      status: 'unhealthy',
      error: error.message
    };
  }
  
  return connectivity;
}

export default router;
