import express from 'express';
import { authenticate<PERSON><PERSON><PERSON><PERSON> } from '../middleware/auth.js';
import { Logger } from '../utils/Logger.js';

const router = express.Router();
const logger = new Logger();

// GET /api/antiban/status - Status do sistema anti-ban
router.get('/status', authenticateApiKey(['whatsapp:read']), async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    
    if (!whatsappService) {
      return res.status(503).json({
        error: 'Serviço não disponível',
        message: 'WhatsApp Service não está disponível',
        code: 'SERVICE_UNAVAILABLE'
      });
    }

    const antiBanStats = whatsappService.antiBanService.getAntiBanStats();
    const humanBehaviorStats = whatsappService.antiBanService.humanBehavior.getStats();

    res.json({
      success: true,
      data: {
        antiBan: antiBanStats,
        humanBehavior: humanBehaviorStats,
        recommendations: generateRecommendations(antiBanStats, humanBehaviorStats)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter status anti-ban:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível obter status anti-ban',
      code: 'ANTIBAN_STATUS_ERROR'
    });
  }
});

// GET /api/antiban/risk-assessment - Avaliação de risco atual
router.get('/risk-assessment', authenticateApiKey(['whatsapp:read']), async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    
    if (!whatsappService) {
      return res.status(503).json({
        error: 'Serviço não disponível',
        code: 'SERVICE_UNAVAILABLE'
      });
    }

    const antiBanService = whatsappService.antiBanService;
    const currentRisk = antiBanService.calculateCurrentRisk();
    const suspiciousPatterns = antiBanService.humanBehavior.detectSuspiciousPatterns();
    
    // Classificar nível de risco
    let riskLevel = 'low';
    if (currentRisk > antiBanService.config.riskDetection.thresholds.high) {
      riskLevel = 'high';
    } else if (currentRisk > antiBanService.config.riskDetection.thresholds.medium) {
      riskLevel = 'medium';
    }

    res.json({
      success: true,
      data: {
        riskScore: currentRisk,
        riskLevel,
        thresholds: antiBanService.config.riskDetection.thresholds,
        suspiciousPatterns,
        recommendations: getRiskRecommendations(currentRisk, riskLevel, suspiciousPatterns),
        safeToSend: currentRisk < antiBanService.config.riskDetection.thresholds.high,
        nextSafeTime: currentRisk > antiBanService.config.riskDetection.thresholds.high ? 
          antiBanService.calculateSafeWaitTime() : 0
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro na avaliação de risco:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível avaliar risco',
      code: 'RISK_ASSESSMENT_ERROR'
    });
  }
});

// POST /api/antiban/test-message - Testar se mensagem pode ser enviada
router.post('/test-message', authenticateApiKey(['whatsapp:write']), async (req, res) => {
  try {
    const { to, message } = req.body;

    if (!to || !message) {
      return res.status(400).json({
        error: 'Dados obrigatórios',
        message: 'Os campos "to" e "message" são obrigatórios',
        code: 'VALIDATION_ERROR'
      });
    }

    const whatsappService = req.app.locals.whatsappService;
    
    if (!whatsappService) {
      return res.status(503).json({
        error: 'Serviço não disponível',
        code: 'SERVICE_UNAVAILABLE'
      });
    }

    const safetyCheck = await whatsappService.antiBanService.isSafeToSendMessage(message, to);

    res.json({
      success: true,
      data: {
        safe: safetyCheck.safe,
        reason: safetyCheck.reason || 'safe_to_send',
        waitTime: safetyCheck.waitTime || 0,
        riskLevel: safetyCheck.riskLevel,
        recommendedDelay: safetyCheck.recommendedDelay || 0,
        message: safetyCheck.safe ? 
          'Mensagem pode ser enviada com segurança' : 
          `Mensagem bloqueada: ${safetyCheck.reason}`
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro no teste de mensagem:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível testar mensagem',
      code: 'MESSAGE_TEST_ERROR'
    });
  }
});

// POST /api/antiban/force-break - Forçar pausa de segurança
router.post('/force-break', authenticateApiKey(['whatsapp:write']), async (req, res) => {
  try {
    const { duration } = req.body;
    const whatsappService = req.app.locals.whatsappService;
    
    if (!whatsappService) {
      return res.status(503).json({
        error: 'Serviço não disponível',
        code: 'SERVICE_UNAVAILABLE'
      });
    }

    const antiBanService = whatsappService.antiBanService;
    
    // Configurar duração personalizada se fornecida
    if (duration && duration > 0) {
      antiBanService.config.session.breakTime = duration * 60 * 1000; // Converter minutos para ms
    }
    
    antiBanService.startMandatoryBreak();

    logger.info('Pausa forçada iniciada', {
      duration: antiBanService.config.session.breakTime / 60000,
      requestedBy: req.auth.keyName,
      ip: req.ip
    });

    res.json({
      success: true,
      message: 'Pausa de segurança iniciada',
      data: {
        breakDuration: antiBanService.config.session.breakTime / 60000, // em minutos
        breakTimeRemaining: antiBanService.getBreakTimeRemaining(),
        reason: 'manual_break'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao forçar pausa:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível iniciar pausa',
      code: 'FORCE_BREAK_ERROR'
    });
  }
});

// POST /api/antiban/reset - Reset do sistema anti-ban
router.post('/reset', authenticateApiKey(['admin']), async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    
    if (!whatsappService) {
      return res.status(503).json({
        error: 'Serviço não disponível',
        code: 'SERVICE_UNAVAILABLE'
      });
    }

    whatsappService.antiBanService.reset();

    logger.info('Sistema anti-ban resetado', {
      requestedBy: req.auth.keyName,
      ip: req.ip
    });

    res.json({
      success: true,
      message: 'Sistema anti-ban resetado com sucesso',
      data: {
        riskLevel: 0,
        messageCount: { minute: 0, hour: 0, day: 0 },
        isInBreak: false
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao resetar anti-ban:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível resetar sistema',
      code: 'RESET_ERROR'
    });
  }
});

// PUT /api/antiban/config - Atualizar configurações anti-ban
router.put('/config', authenticateApiKey(['admin']), async (req, res) => {
  try {
    const { config } = req.body;

    if (!config || typeof config !== 'object') {
      return res.status(400).json({
        error: 'Configuração inválida',
        message: 'O campo "config" deve ser um objeto válido',
        code: 'VALIDATION_ERROR'
      });
    }

    const whatsappService = req.app.locals.whatsappService;
    
    if (!whatsappService) {
      return res.status(503).json({
        error: 'Serviço não disponível',
        code: 'SERVICE_UNAVAILABLE'
      });
    }

    whatsappService.antiBanService.updateConfig(config);

    logger.info('Configurações anti-ban atualizadas', {
      config,
      requestedBy: req.auth.keyName,
      ip: req.ip
    });

    res.json({
      success: true,
      message: 'Configurações atualizadas com sucesso',
      data: {
        updatedConfig: config
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao atualizar configurações:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível atualizar configurações',
      code: 'CONFIG_UPDATE_ERROR'
    });
  }
});

// GET /api/antiban/behavior-analysis - Análise de comportamento
router.get('/behavior-analysis', authenticateApiKey(['whatsapp:read']), async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    
    if (!whatsappService) {
      return res.status(503).json({
        error: 'Serviço não disponível',
        code: 'SERVICE_UNAVAILABLE'
      });
    }

    const humanBehavior = whatsappService.antiBanService.humanBehavior;
    const behaviorHistory = humanBehavior.behaviorHistory.slice(-100); // Últimas 100 ações
    const suspiciousPatterns = humanBehavior.detectSuspiciousPatterns();
    const behaviorScore = humanBehavior.calculateBehaviorScore();

    // Análise de padrões
    const analysis = {
      behaviorScore,
      totalActions: behaviorHistory.length,
      suspiciousPatterns: suspiciousPatterns.length,
      patterns: suspiciousPatterns,
      recommendations: getBehaviorRecommendations(behaviorScore, suspiciousPatterns),
      timeline: behaviorHistory.map(action => ({
        timestamp: action.timestamp,
        type: action.type,
        messageLength: action.messageLength,
        burstCount: action.burstCount,
        timeSinceLastMessage: action.timeSinceLastMessage
      }))
    };

    res.json({
      success: true,
      data: analysis,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro na análise de comportamento:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível analisar comportamento',
      code: 'BEHAVIOR_ANALYSIS_ERROR'
    });
  }
});

// Funções auxiliares
function generateRecommendations(antiBanStats, humanBehaviorStats) {
  const recommendations = [];

  if (antiBanStats.riskLevel > 70) {
    recommendations.push({
      type: 'warning',
      message: 'Nível de risco alto - considere fazer uma pausa',
      action: 'force_break'
    });
  }

  if (humanBehaviorStats.burstCount > 3) {
    recommendations.push({
      type: 'info',
      message: 'Muitas mensagens em sequência - adicione delays',
      action: 'increase_delays'
    });
  }

  if (!antiBanStats.isHumanActivityTime) {
    recommendations.push({
      type: 'warning',
      message: 'Atividade fora do horário normal detectada',
      action: 'schedule_activity'
    });
  }

  return recommendations;
}

function getRiskRecommendations(riskScore, riskLevel, patterns) {
  const recommendations = [];

  if (riskLevel === 'high') {
    recommendations.push('Pare imediatamente e faça uma pausa de 30+ minutos');
    recommendations.push('Verifique se há padrões suspeitos na atividade');
  } else if (riskLevel === 'medium') {
    recommendations.push('Reduza a frequência de mensagens');
    recommendations.push('Aumente os delays entre mensagens');
  }

  patterns.forEach(pattern => {
    switch (pattern.type) {
      case 'regular_intervals':
        recommendations.push('Varie mais os intervalos entre mensagens');
        break;
      case 'excessive_bursts':
        recommendations.push('Evite enviar muitas mensagens em sequência');
        break;
      case 'off_hours_activity':
        recommendations.push('Limite atividade fora do horário comercial');
        break;
    }
  });

  return recommendations;
}

function getBehaviorRecommendations(score, patterns) {
  const recommendations = [];

  if (score < 70) {
    recommendations.push('Comportamento detectado como não-humano');
    recommendations.push('Implemente mais variações nos delays');
    recommendations.push('Adicione pausas aleatórias');
  }

  if (patterns.length > 2) {
    recommendations.push('Múltiplos padrões suspeitos detectados');
    recommendations.push('Revise a estratégia de envio de mensagens');
  }

  return recommendations;
}

export default router;
