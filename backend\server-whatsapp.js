#!/usr/bin/env node

/**
 * Servidor Backend com WhatsApp Real
 * Integração completa: RAG + Gemini + Supabase + WhatsApp
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Importar configuração e logger
import { ServerConfig } from './src/config/ServerConfig.js';
import { Logger } from './src/utils/Logger.js';

// Importar serviços RAG
import { GoogleGenerativeAI } from '@google/generative-ai';
import { createClient } from '@supabase/supabase-js';

// Importar WPPConnect
import { create, Whatsapp } from '@wppconnect-team/wppconnect';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '.env') });

/**
 * Servidor Backend com WhatsApp Real
 */
class WhatsAppServer {
  constructor() {
    this.app = express();
    this.config = new ServerConfig();
    this.logger = new Logger();
    this.server = null;
    
    // Estados do WhatsApp
    this.whatsappClient = null;
    this.isWhatsAppConnected = false;
    this.qrCode = null;
    this.connectionStatus = 'disconnected';
    
    // Inicializar serviços
    this.initializeRAGServices();
    
    console.log('📱 Inicializando servidor com WhatsApp...');
    
    this.setupMiddlewares();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  initializeRAGServices() {
    console.log('🧠 Inicializando serviços RAG...');

    // Inicializar Gemini se disponível
    if (this.config.gemini.apiKey) {
      try {
        this.genAI = new GoogleGenerativeAI(this.config.gemini.apiKey);
        this.model = this.genAI.getGenerativeModel({ model: this.config.gemini.model });
        console.log('✅ Gemini AI inicializado');
      } catch (error) {
        console.warn('⚠️ Erro ao inicializar Gemini:', error.message);
        this.genAI = null;
        this.model = null;
      }
    } else {
      console.log('⚠️ Gemini API Key não configurada');
      this.genAI = null;
      this.model = null;
    }

    // Inicializar Supabase se disponível
    if (this.config.supabase.url && this.config.supabase.anonKey) {
      try {
        this.supabase = createClient(
          this.config.supabase.url,
          this.config.supabase.anonKey
        );
        console.log('✅ Supabase inicializado');
      } catch (error) {
        console.warn('⚠️ Erro ao inicializar Supabase:', error.message);
        this.supabase = null;
      }
    } else {
      console.log('⚠️ Supabase não configurado');
      this.supabase = null;
    }
  }

  async initializeWhatsApp() {
    console.log('📱 Inicializando WhatsApp...');
    
    try {
      this.whatsappClient = await create({
        session: this.config.whatsapp.sessionName,
        catchQR: (base64Qr, asciiQR, attempts, urlCode) => {
          console.log('📱 QR Code gerado (tentativa', attempts, ')');
          this.qrCode = base64Qr;
          this.connectionStatus = 'qr_ready';
          
          // Salvar QR code para o frontend
          this.saveQRCode(base64Qr);
        },
        statusFind: (statusSession, session) => {
          console.log('📱 Status da sessão:', statusSession, session);
          this.connectionStatus = statusSession;
          
          if (statusSession === 'isLogged') {
            this.isWhatsAppConnected = true;
            this.qrCode = null;
            console.log('✅ WhatsApp conectado com sucesso!');
          }
        },
        headless: true,
        devtools: false,
        useChrome: true,
        debug: false,
        logQR: false,
        browserArgs: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      // Configurar listeners de mensagens
      this.setupWhatsAppListeners();
      
      console.log('✅ WhatsApp inicializado');
      return true;
      
    } catch (error) {
      console.error('❌ Erro ao inicializar WhatsApp:', error);
      this.connectionStatus = 'error';
      return false;
    }
  }

  setupWhatsAppListeners() {
    if (!this.whatsappClient) return;

    console.log('🔧 Configurando listeners do WhatsApp...');

    // Listener para mensagens recebidas
    this.whatsappClient.onMessage(async (message) => {
      try {
        console.log('📨 Mensagem recebida:', {
          from: message.from,
          body: message.body,
          type: message.type
        });

        // Ignorar mensagens de status e grupos por enquanto
        if (message.isGroupMsg || message.from.includes('status')) {
          return;
        }

        // Processar mensagem com RAG
        await this.processIncomingMessage(message);
        
      } catch (error) {
        console.error('❌ Erro ao processar mensagem:', error);
      }
    });

    // Listener para mudanças de estado
    this.whatsappClient.onStateChange((state) => {
      console.log('📱 Estado do WhatsApp mudou:', state);
      this.connectionStatus = state;
      
      if (state === 'CONNECTED') {
        this.isWhatsAppConnected = true;
        this.qrCode = null;
      } else if (state === 'DISCONNECTED') {
        this.isWhatsAppConnected = false;
      }
    });

    console.log('✅ Listeners configurados');
  }

  async processIncomingMessage(message) {
    try {
      const userMessage = message.body;
      const phoneNumber = message.from;
      
      console.log(`🤖 Processando mensagem de ${phoneNumber}: ${userMessage}`);

      // Gerar resposta usando RAG
      const response = await this.generateResponse(userMessage, phoneNumber);
      
      // Enviar resposta
      await this.sendWhatsAppMessage(phoneNumber, response);
      
      // Salvar conversa no Supabase se disponível
      if (this.supabase) {
        await this.saveConversation(phoneNumber, userMessage, response);
      }
      
    } catch (error) {
      console.error('❌ Erro ao processar mensagem:', error);
      
      // Enviar mensagem de erro amigável
      const errorMessage = "Desculpe, tive um problema técnico. Tente novamente em alguns instantes. 🙏🏽";
      await this.sendWhatsAppMessage(message.from, errorMessage);
    }
  }

  async generateResponse(userMessage, phoneNumber) {
    try {
      // Se Gemini está disponível, usar IA real
      if (this.model) {
        const prompt = this.buildRafaelaPrompt(userMessage, phoneNumber);
        const result = await this.model.generateContent(prompt);
        return result.response.text();
      } else {
        // Resposta simulada
        return this.generateSimulatedResponse(userMessage);
      }
    } catch (error) {
      console.error('❌ Erro ao gerar resposta:', error);
      return this.generateSimulatedResponse(userMessage);
    }
  }

  buildRafaelaPrompt(userMessage, phoneNumber) {
    return `Você é a Vereadora Rafaela de Nilda, eleita para representar o povo de Parnamirim/RN no período 2025-2028.

PERSONALIDADE:
- Carinhosa, atenciosa e próxima do povo
- Usa emojis apropriados: 🙏🏽💖🤩😍👏
- Linguagem acessível e calorosa
- Sempre solicita e prestativa

INFORMAÇÕES BÁSICAS:
- Nome: Vereadora Rafaela de Nilda
- Município: Parnamirim/RN
- Mandato: 2025-2028
- Partido: SOLIDARIEDADE

CONTEXTO:
- Esta é uma conversa via WhatsApp
- Mantenha respostas concisas (máximo 2 parágrafos)
- Use linguagem informal mas respeitosa
- Ofereça ajuda concreta quando possível

INSTRUÇÕES:
1. Responda como se fosse a própria vereadora
2. Seja calorosa e próxima do povo
3. Use linguagem simples e acessível
4. Inclua emojis apropriados
5. Se não souber algo específico, seja honesta mas mantenha-se disponível para ajudar
6. Mantenha respostas curtas para WhatsApp

MENSAGEM DO CIDADÃO: ${userMessage}

Responda de forma calorosa e prestativa:`;
  }

  generateSimulatedResponse(userMessage) {
    const responses = [
      `Olá! Sou a Vereadora Rafaela de Nilda. Como posso ajudá-lo hoje? 🏛️`,
      `Obrigada por entrar em contato! Estou aqui para servir Parnamirim. Em que posso ajudar? 💖`,
      `Oi! É um prazer falar com você. Como posso contribuir para melhorar nossa cidade? 🤩`,
      `Olá, querido(a)! Conte comigo para o que precisar. Estamos juntos por Parnamirim! 🙏🏽`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  }

  async sendWhatsAppMessage(phoneNumber, message) {
    try {
      if (!this.whatsappClient || !this.isWhatsAppConnected) {
        console.log('⚠️ WhatsApp não conectado, não é possível enviar mensagem');
        return false;
      }

      await this.whatsappClient.sendText(phoneNumber, message);
      console.log(`✅ Mensagem enviada para ${phoneNumber}`);
      return true;
      
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem:', error);
      return false;
    }
  }

  async saveConversation(phoneNumber, userMessage, botResponse) {
    try {
      if (!this.supabase) return;

      const { error } = await this.supabase
        .from('conversations')
        .upsert({
          phone_number: phoneNumber,
          recent_messages: [
            { role: 'user', content: userMessage, timestamp: new Date().toISOString() },
            { role: 'assistant', content: botResponse, timestamp: new Date().toISOString() }
          ],
          updated_at: new Date().toISOString()
        }, { onConflict: 'phone_number' });

      if (error) {
        console.error('❌ Erro ao salvar conversa:', error);
      } else {
        console.log('💾 Conversa salva no Supabase');
      }
    } catch (error) {
      console.error('❌ Erro ao salvar conversa:', error);
    }
  }

  async saveQRCode(base64Qr) {
    // Salvar QR code em arquivo temporário para o frontend
    try {
      const fs = await import('fs');
      const qrData = {
        qr: base64Qr,
        timestamp: new Date().toISOString(),
        status: 'waiting_scan'
      };

      // Garantir que o diretório data existe
      const dataDir = join(__dirname, 'data');
      try {
        await fs.promises.access(dataDir);
      } catch {
        await fs.promises.mkdir(dataDir, { recursive: true });
      }

      await fs.promises.writeFile(
        join(dataDir, 'qr-code.json'),
        JSON.stringify(qrData, null, 2)
      );
      console.log('💾 QR Code salvo para o frontend');
    } catch (error) {
      console.error('❌ Erro ao salvar QR Code:', error);
    }
  }

  setupMiddlewares() {
    console.log('🔧 Configurando middlewares...');

    // Segurança
    this.app.use(helmet({
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false
    }));

    // CORS
    this.app.use(cors({
      origin: this.config.cors.allowedOrigins,
      credentials: this.config.cors.credentials,
      methods: this.config.cors.methods,
      allowedHeaders: this.config.cors.allowedHeaders
    }));

    // Compressão
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: this.config.rateLimit.windowMs,
      max: this.config.rateLimit.maxRequests,
      message: {
        error: 'Muitas requisições. Tente novamente em alguns minutos.',
        code: 'RATE_LIMIT_EXCEEDED'
      }
    });
    this.app.use('/api/', limiter);

    // Logging
    this.app.use(morgan('combined'));

    // Body parsing
    this.app.use(express.json({ limit: this.config.server.bodyLimit }));
    this.app.use(express.urlencoded({ extended: true, limit: this.config.server.bodyLimit }));

    console.log('✅ Middlewares configurados');
  }

  setupRoutes() {
    console.log('🛣️ Configurando rotas...');

    // Rota principal
    this.app.get('/', (req, res) => {
      res.json({
        message: '🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda',
        version: '2.0.0-whatsapp',
        status: 'online',
        timestamp: new Date().toISOString(),
        environment: this.config.app.environment,
        mode: 'whatsapp_integrated',
        integrations: {
          gemini: !!this.genAI,
          supabase: !!this.supabase,
          whatsapp: this.isWhatsAppConnected
        },
        whatsapp: {
          connected: this.isWhatsAppConnected,
          status: this.connectionStatus,
          hasQR: !!this.qrCode
        },
        endpoints: {
          health: '/api/health',
          config: '/api/config',
          whatsapp: '/api/whatsapp/status',
          qr: '/api/whatsapp/qr',
          rag: '/api/rag/query',
          documents: '/api/documents'
        }
      });
    });

    // Health check
    this.app.get('/api/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '2.0.0-whatsapp',
        environment: this.config.app.environment,
        services: {
          server: 'online',
          gemini: this.genAI ? 'online' : 'offline',
          supabase: this.supabase ? 'online' : 'offline',
          whatsapp: this.isWhatsAppConnected ? 'connected' : 'disconnected',
          rag: this.genAI ? 'online' : 'simulated'
        }
      });
    });

    // Configuração
    this.app.get('/api/config', (req, res) => {
      res.json({
        server: {
          port: this.config.server.port,
          environment: this.config.app.environment,
          version: this.config.app.version
        },
        whatsapp: {
          sessionName: this.config.whatsapp.sessionName,
          useSimulator: this.config.whatsapp.useSimulator,
          useReal: this.config.whatsapp.useReal,
          useHttp: this.config.whatsapp.useHttp,
          connected: this.isWhatsAppConnected,
          status: this.connectionStatus
        },
        integrations: {
          hasGeminiKey: !!this.config.gemini.apiKey,
          hasSupabaseUrl: !!this.config.supabase.url,
          ragEnabled: this.config.rag.enabled,
          geminiModel: this.config.gemini.model
        },
        timestamp: new Date().toISOString()
      });
    });

    // Status do WhatsApp
    this.app.get('/api/whatsapp/status', (req, res) => {
      res.json({
        connected: this.isWhatsAppConnected,
        status: this.connectionStatus,
        session: this.config.whatsapp.sessionName,
        hasQR: !!this.qrCode,
        timestamp: new Date().toISOString()
      });
    });

    // QR Code do WhatsApp
    this.app.get('/api/whatsapp/qr', (req, res) => {
      if (this.qrCode) {
        res.json({
          hasQR: true,
          qr: this.qrCode,
          status: this.connectionStatus,
          timestamp: new Date().toISOString()
        });
      } else {
        res.json({
          hasQR: false,
          message: this.isWhatsAppConnected ? 'WhatsApp já conectado' : 'QR Code não disponível',
          status: this.connectionStatus,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Enviar mensagem via API
    this.app.post('/api/whatsapp/send', async (req, res) => {
      try {
        const { phoneNumber, message } = req.body;

        if (!phoneNumber || !message) {
          return res.status(400).json({
            error: 'phoneNumber e message são obrigatórios',
            code: 'MISSING_PARAMETERS'
          });
        }

        if (!this.isWhatsAppConnected) {
          return res.status(503).json({
            error: 'WhatsApp não conectado',
            code: 'WHATSAPP_DISCONNECTED'
          });
        }

        const success = await this.sendWhatsAppMessage(phoneNumber, message);

        if (success) {
          res.json({
            success: true,
            message: 'Mensagem enviada com sucesso',
            timestamp: new Date().toISOString()
          });
        } else {
          res.status(500).json({
            error: 'Falha ao enviar mensagem',
            code: 'SEND_FAILED'
          });
        }

      } catch (error) {
        console.error('❌ Erro ao enviar mensagem via API:', error);
        res.status(500).json({
          error: 'Erro interno do servidor',
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // RAG Query (mesmo do servidor enhanced)
    this.app.post('/api/rag/query', async (req, res) => {
      try {
        const { query } = req.body;

        if (!query) {
          return res.status(400).json({
            error: 'Query é obrigatória',
            code: 'MISSING_QUERY'
          });
        }

        const startTime = Date.now();
        const response = await this.generateResponse(query, 'api_request');

        res.json({
          query,
          response,
          sources: [],
          metadata: {
            mode: this.model ? 'gemini' : 'simulated',
            vereadora: 'Rafaela de Nilda',
            municipio: 'Parnamirim/RN',
            timestamp: new Date().toISOString(),
            processingTime: Date.now() - startTime
          }
        });

      } catch (error) {
        console.error('❌ Erro na query RAG:', error);
        res.status(500).json({
          error: 'Erro interno do servidor',
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Documentos (mesmo do servidor enhanced)
    this.app.get('/api/documents', async (req, res) => {
      try {
        if (this.supabase) {
          const { data, error } = await this.supabase
            .from('documents')
            .select('*')
            .order('created_at', { ascending: false });

          if (error) throw error;

          res.json({
            documents: data || [],
            count: data?.length || 0,
            source: 'supabase',
            timestamp: new Date().toISOString()
          });
        } else {
          res.json({
            documents: [],
            count: 0,
            source: 'simulated',
            message: 'Supabase não configurado',
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('❌ Erro ao buscar documentos:', error);
        res.status(500).json({
          error: 'Erro ao buscar documentos',
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Rota 404
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint não encontrado',
        message: 'A rota solicitada não existe neste servidor',
        availableEndpoints: [
          '/',
          '/api/health',
          '/api/config',
          '/api/whatsapp/status',
          '/api/whatsapp/qr',
          '/api/whatsapp/send',
          '/api/rag/query',
          '/api/documents'
        ],
        timestamp: new Date().toISOString()
      });
    });

    console.log('✅ Rotas configuradas');
  }

  setupErrorHandling() {
    console.log('⚠️ Configurando tratamento de erros...');

    // Middleware de tratamento de erros
    this.app.use((error, req, res, next) => {
      console.error('❌ Erro no servidor:', error);

      res.status(500).json({
        error: 'Erro interno do servidor',
        message: error.message,
        timestamp: new Date().toISOString(),
        mode: 'whatsapp_integrated'
      });
    });

    // Tratamento de promises rejeitadas
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Promise rejeitada não tratada:', reason);
    });

    // Tratamento de exceções não capturadas
    process.on('uncaughtException', (error) => {
      console.error('❌ Exceção não capturada:', error);
      setTimeout(() => process.exit(1), 1000);
    });

    console.log('✅ Tratamento de erros configurado');
  }

  async start() {
    try {
      console.log('🚀 Iniciando servidor com WhatsApp...');

      const port = this.config.server.port;

      // Iniciar servidor HTTP primeiro
      this.server = this.app.listen(port, () => {
        console.log('');
        console.log('🎉 ===================================');
        console.log('📱  SERVIDOR WHATSAPP INICIADO!');
        console.log('🎉 ===================================');
        console.log('');
        console.log(`📍 Porta: ${port}`);
        console.log(`🌐 URL: http://localhost:${port}`);
        console.log(`📱 Ambiente: ${this.config.app.environment}`);
        console.log(`⚡ Modo: WhatsApp Integrado`);
        console.log('');
        console.log('🧠 Integrações:');
        console.log(`   Gemini AI: ${this.genAI ? '✅ Ativo' : '❌ Não configurado'}`);
        console.log(`   Supabase: ${this.supabase ? '✅ Ativo' : '❌ Não configurado'}`);
        console.log(`   WhatsApp: 🔄 Inicializando...`);
        console.log('');
        console.log('🎯 Endpoints disponíveis:');
        console.log(`   GET  /                         - Informações do servidor`);
        console.log(`   GET  /api/health               - Health check`);
        console.log(`   GET  /api/whatsapp/status      - Status WhatsApp`);
        console.log(`   GET  /api/whatsapp/qr          - QR Code`);
        console.log(`   POST /api/whatsapp/send        - Enviar mensagem`);
        console.log(`   POST /api/rag/query            - Query RAG`);
        console.log(`   GET  /api/documents            - Documentos`);
        console.log('');
        console.log('✅ Servidor HTTP funcionando!');
        console.log('📱 Inicializando WhatsApp...');
        console.log('');
      });

      // Inicializar WhatsApp após servidor HTTP
      setTimeout(async () => {
        const whatsappSuccess = await this.initializeWhatsApp();

        if (whatsappSuccess) {
          console.log('');
          console.log('🎉 ===================================');
          console.log('📱  WHATSAPP INICIALIZADO!');
          console.log('🎉 ===================================');
          console.log('');
          console.log('📱 Para conectar:');
          console.log('1. Acesse: http://localhost:3001/api/whatsapp/qr');
          console.log('2. Escaneie o QR Code com seu WhatsApp');
          console.log('3. Aguarde a conexão ser estabelecida');
          console.log('');
          console.log('✅ Sistema completo funcionando!');
          console.log('');
        } else {
          console.log('');
          console.log('⚠️ WhatsApp não pôde ser inicializado');
          console.log('💡 O servidor continuará funcionando em modo API');
          console.log('');
        }
      }, 2000);

      // Graceful shutdown
      process.on('SIGTERM', this.shutdown.bind(this));
      process.on('SIGINT', this.shutdown.bind(this));

    } catch (error) {
      console.error('❌ Erro ao iniciar servidor:', error);
      process.exit(1);
    }
  }

  async shutdown(signal) {
    console.log(`\n🛑 Recebido sinal ${signal}, parando servidor...`);

    try {
      // Fechar WhatsApp primeiro
      if (this.whatsappClient) {
        console.log('📱 Fechando conexão WhatsApp...');
        await this.whatsappClient.close();
      }

      // Fechar servidor HTTP
      if (this.server) {
        this.server.close(() => {
          console.log('✅ Servidor parado com sucesso');
          process.exit(0);
        });
      } else {
        process.exit(0);
      }
    } catch (error) {
      console.error('❌ Erro durante shutdown:', error);
      process.exit(1);
    }
  }
}

// Iniciar servidor
const server = new WhatsAppServer();
server.start();
