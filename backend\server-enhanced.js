#!/usr/bin/env node

/**
 * Servidor Backend Aprimorado
 * Com integração completa RAG + Gemini + Supabase
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Importar configuração e logger
import { ServerConfig } from './src/config/ServerConfig.js';
import { Logger } from './src/utils/Logger.js';

// Importar serviços RAG
import { GoogleGenerativeAI } from '@google/generative-ai';
import { createClient } from '@supabase/supabase-js';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '.env') });

/**
 * Servidor Backend Aprimorado
 */
class EnhancedServer {
  constructor() {
    this.app = express();
    this.config = new ServerConfig();
    this.logger = new Logger();
    this.server = null;
    
    // Inicializar serviços RAG
    this.initializeRAGServices();
    
    console.log('🏗️ Inicializando servidor aprimorado...');
    
    this.setupMiddlewares();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  initializeRAGServices() {
    console.log('🧠 Inicializando serviços RAG...');

    // Inicializar Gemini se disponível
    if (this.config.gemini.apiKey) {
      try {
        this.genAI = new GoogleGenerativeAI(this.config.gemini.apiKey);
        this.model = this.genAI.getGenerativeModel({ model: this.config.gemini.model });
        console.log('✅ Gemini AI inicializado');
      } catch (error) {
        console.warn('⚠️ Erro ao inicializar Gemini:', error.message);
        this.genAI = null;
        this.model = null;
      }
    } else {
      console.log('⚠️ Gemini API Key não configurada');
      this.genAI = null;
      this.model = null;
    }

    // Inicializar Supabase se disponível
    if (this.config.supabase.url && this.config.supabase.anonKey) {
      try {
        this.supabase = createClient(
          this.config.supabase.url,
          this.config.supabase.anonKey
        );
        console.log('✅ Supabase inicializado');
      } catch (error) {
        console.warn('⚠️ Erro ao inicializar Supabase:', error.message);
        this.supabase = null;
      }
    } else {
      console.log('⚠️ Supabase não configurado');
      this.supabase = null;
    }
  }

  setupMiddlewares() {
    console.log('🔧 Configurando middlewares...');

    // Segurança
    this.app.use(helmet({
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false
    }));

    // CORS
    this.app.use(cors({
      origin: this.config.cors.allowedOrigins,
      credentials: this.config.cors.credentials,
      methods: this.config.cors.methods,
      allowedHeaders: this.config.cors.allowedHeaders
    }));

    // Compressão
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: this.config.rateLimit.windowMs,
      max: this.config.rateLimit.maxRequests,
      message: {
        error: 'Muitas requisições. Tente novamente em alguns minutos.',
        code: 'RATE_LIMIT_EXCEEDED'
      }
    });
    this.app.use('/api/', limiter);

    // Logging
    this.app.use(morgan('combined'));

    // Body parsing
    this.app.use(express.json({ limit: this.config.server.bodyLimit }));
    this.app.use(express.urlencoded({ extended: true, limit: this.config.server.bodyLimit }));

    console.log('✅ Middlewares configurados');
  }

  setupRoutes() {
    console.log('🛣️ Configurando rotas...');

    // Rota principal
    this.app.get('/', (req, res) => {
      res.json({
        message: '🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda',
        version: '2.0.0-enhanced',
        status: 'online',
        timestamp: new Date().toISOString(),
        environment: this.config.app.environment,
        mode: 'enhanced',
        integrations: {
          gemini: !!this.genAI,
          supabase: !!this.supabase
        },
        endpoints: {
          health: '/api/health',
          config: '/api/config',
          whatsapp: '/api/whatsapp/status',
          rag: '/api/rag/query',
          documents: '/api/documents'
        }
      });
    });

    // Health check
    this.app.get('/api/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '2.0.0-enhanced',
        environment: this.config.app.environment,
        services: {
          server: 'online',
          gemini: this.genAI ? 'online' : 'offline',
          supabase: this.supabase ? 'online' : 'offline',
          whatsapp: 'simulated',
          rag: this.genAI ? 'online' : 'simulated'
        }
      });
    });

    // Configuração
    this.app.get('/api/config', (req, res) => {
      res.json({
        server: {
          port: this.config.server.port,
          environment: this.config.app.environment,
          version: this.config.app.version
        },
        whatsapp: {
          sessionName: this.config.whatsapp.sessionName,
          useSimulator: this.config.whatsapp.useSimulator,
          useReal: this.config.whatsapp.useReal,
          useHttp: this.config.whatsapp.useHttp
        },
        integrations: {
          hasGeminiKey: !!this.config.gemini.apiKey,
          hasSupabaseUrl: !!this.config.supabase.url,
          ragEnabled: this.config.rag.enabled,
          geminiModel: this.config.gemini.model
        },
        timestamp: new Date().toISOString()
      });
    });

    // RAG Query com Gemini real
    this.app.post('/api/rag/query', async (req, res) => {
      try {
        const { query } = req.body;
        
        if (!query) {
          return res.status(400).json({
            error: 'Query é obrigatória',
            code: 'MISSING_QUERY'
          });
        }

        const startTime = Date.now();

        // Se Gemini está disponível, usar IA real
        if (this.model) {
          try {
            const prompt = this.buildRafaelaPrompt(query);
            const result = await this.model.generateContent(prompt);
            const response = result.response.text();

            res.json({
              query,
              response,
              sources: [],
              metadata: {
                mode: 'gemini',
                vereadora: 'Rafaela de Nilda',
                municipio: 'Parnamirim/RN',
                model: this.config.gemini.model,
                timestamp: new Date().toISOString(),
                processingTime: Date.now() - startTime
              }
            });
          } catch (error) {
            console.error('Erro no Gemini:', error);
            // Fallback para resposta simulada
            res.json(this.generateSimulatedResponse(query, Date.now() - startTime));
          }
        } else {
          // Resposta simulada
          res.json(this.generateSimulatedResponse(query, Date.now() - startTime));
        }

      } catch (error) {
        console.error('Erro na query RAG:', error);
        res.status(500).json({
          error: 'Erro interno do servidor',
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Documentos (com Supabase se disponível)
    this.app.get('/api/documents', async (req, res) => {
      try {
        if (this.supabase) {
          const { data, error } = await this.supabase
            .from('documents')
            .select('*')
            .order('created_at', { ascending: false });

          if (error) throw error;

          res.json({
            documents: data || [],
            count: data?.length || 0,
            source: 'supabase',
            timestamp: new Date().toISOString()
          });
        } else {
          res.json({
            documents: [],
            count: 0,
            source: 'simulated',
            message: 'Supabase não configurado',
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Erro ao buscar documentos:', error);
        res.status(500).json({
          error: 'Erro ao buscar documentos',
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // WhatsApp Status (simulado)
    this.app.get('/api/whatsapp/status', (req, res) => {
      res.json({
        connected: false,
        session: this.config.whatsapp.sessionName,
        mode: 'simulated',
        message: 'WhatsApp em modo simulado - servidor aprimorado',
        timestamp: new Date().toISOString()
      });
    });

    // Webhook (simulado)
    this.app.post('/api/webhook/whatsapp', (req, res) => {
      console.log('📨 Webhook recebido (simulado):', req.body);
      res.json({
        received: true,
        mode: 'simulated',
        timestamp: new Date().toISOString()
      });
    });

    // Rota 404
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint não encontrado',
        message: 'A rota solicitada não existe neste servidor',
        availableEndpoints: [
          '/',
          '/api/health',
          '/api/config',
          '/api/whatsapp/status',
          '/api/rag/query',
          '/api/documents',
          '/api/webhook/whatsapp'
        ],
        timestamp: new Date().toISOString()
      });
    });

    console.log('✅ Rotas configuradas');
  }

  buildRafaelaPrompt(userQuery) {
    return `Você é a Vereadora Rafaela de Nilda, eleita para representar o povo de Parnamirim/RN no período 2025-2028.

PERSONALIDADE:
- Carinhosa, atenciosa e próxima do povo
- Usa emojis apropriados: 🙏🏽💖🤩😍👏
- Linguagem acessível e calorosa
- Sempre solicita e prestativa

INFORMAÇÕES BÁSICAS:
- Nome: Vereadora Rafaela de Nilda
- Município: Parnamirim/RN
- Mandato: 2025-2028
- Partido: SOLIDARIEDADE

INSTRUÇÕES:
1. Responda como se fosse a própria vereadora
2. Seja calorosa e próxima do povo
3. Use linguagem simples e acessível
4. Inclua emojis apropriados
5. Ofereça ajuda concreta quando possível
6. Se não souber algo específico, seja honesta mas mantenha-se disponível para ajudar

PERGUNTA DO CIDADÃO: ${userQuery}

Responda de forma calorosa e prestativa:`;
  }

  generateSimulatedResponse(userMessage, processingTime) {
    const responses = [
      `Olá! Sou a Vereadora Rafaela de Nilda. Como posso ajudá-lo hoje? 🏛️`,
      `Obrigada por entrar em contato! Estou aqui para servir Parnamirim. Em que posso ajudar? 💖`,
      `Oi! É um prazer falar com você. Como posso contribuir para melhorar nossa cidade? 🤩`,
      `Olá, querido(a)! Conte comigo para o que precisar. Estamos juntos por Parnamirim! 🙏🏽`
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

    return {
      query: userMessage,
      response: randomResponse,
      sources: [],
      metadata: {
        mode: 'simulated',
        vereadora: 'Rafaela de Nilda',
        municipio: 'Parnamirim/RN',
        timestamp: new Date().toISOString(),
        processingTime: processingTime || Math.random() * 500 + 200
      }
    };
  }

  setupErrorHandling() {
    console.log('⚠️ Configurando tratamento de erros...');

    // Middleware de tratamento de erros
    this.app.use((error, req, res, next) => {
      console.error('❌ Erro no servidor:', error);

      res.status(500).json({
        error: 'Erro interno do servidor',
        message: error.message,
        timestamp: new Date().toISOString(),
        mode: 'enhanced'
      });
    });

    // Tratamento de promises rejeitadas
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Promise rejeitada não tratada:', reason);
    });

    // Tratamento de exceções não capturadas
    process.on('uncaughtException', (error) => {
      console.error('❌ Exceção não capturada:', error);
      setTimeout(() => process.exit(1), 1000);
    });

    console.log('✅ Tratamento de erros configurado');
  }

  async start() {
    try {
      console.log('🚀 Iniciando servidor aprimorado...');

      const port = this.config.server.port;

      this.server = this.app.listen(port, () => {
        console.log('');
        console.log('🎉 ===================================');
        console.log('🏛️  SERVIDOR APRIMORADO INICIADO!');
        console.log('🎉 ===================================');
        console.log('');
        console.log(`📍 Porta: ${port}`);
        console.log(`🌐 URL: http://localhost:${port}`);
        console.log(`📱 Ambiente: ${this.config.app.environment}`);
        console.log(`⚡ Modo: Aprimorado (RAG + Gemini + Supabase)`);
        console.log('');
        console.log('🧠 Integrações:');
        console.log(`   Gemini AI: ${this.genAI ? '✅ Ativo' : '❌ Não configurado'}`);
        console.log(`   Supabase: ${this.supabase ? '✅ Ativo' : '❌ Não configurado'}`);
        console.log('');
        console.log('🎯 Endpoints disponíveis:');
        console.log(`   GET  /                         - Informações do servidor`);
        console.log(`   GET  /api/health               - Health check`);
        console.log(`   GET  /api/config               - Configuração`);
        console.log(`   GET  /api/whatsapp/status      - Status WhatsApp`);
        console.log(`   POST /api/rag/query            - Query RAG (Gemini)`);
        console.log(`   GET  /api/documents            - Documentos (Supabase)`);
        console.log(`   POST /api/webhook/whatsapp     - Webhook`);
        console.log('');
        console.log('✅ Servidor funcionando com RAG completo!');
        console.log('🔧 Para testar: curl http://localhost:3001/api/health');
        console.log('');
      });

      // Graceful shutdown
      process.on('SIGTERM', this.shutdown.bind(this));
      process.on('SIGINT', this.shutdown.bind(this));

    } catch (error) {
      console.error('❌ Erro ao iniciar servidor:', error);
      process.exit(1);
    }
  }

  shutdown(signal) {
    console.log(`\n🛑 Recebido sinal ${signal}, parando servidor...`);

    if (this.server) {
      this.server.close(() => {
        console.log('✅ Servidor parado com sucesso');
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  }
}

// Iniciar servidor
const server = new EnhancedServer();
server.start();
