<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API WhatsApp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #25D366;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #128C7E;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste API WhatsApp</h1>
        
        <div>
            <h3>Testes Básicos:</h3>
            <button onclick="testHealth()">🏥 Health Check</button>
            <button onclick="testStatus()">📱 Status WhatsApp</button>
            <button onclick="testChats()">💬 Listar Conversas</button>
            <button onclick="testSync()">🔄 Sincronizar</button>
            <button onclick="testStats()">📊 Estatísticas</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        function addResult(title, data, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<strong>${title}</strong>\n${JSON.stringify(data, null, 2)}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                addResult('✅ Health Check', data);
            } catch (error) {
                addResult('❌ Health Check', { error: error.message }, true);
            }
        }

        async function testStatus() {
            try {
                const response = await fetch(`${API_BASE}/whatsapp/status`);
                const data = await response.json();
                addResult('✅ Status WhatsApp', data);
            } catch (error) {
                addResult('❌ Status WhatsApp', { error: error.message }, true);
            }
        }

        async function testChats() {
            try {
                const response = await fetch(`${API_BASE}/whatsapp/chats`);
                const data = await response.json();
                addResult('✅ Listar Conversas', data);
            } catch (error) {
                addResult('❌ Listar Conversas', { error: error.message }, true);
            }
        }

        async function testSync() {
            try {
                const response = await fetch(`${API_BASE}/whatsapp/sync`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                addResult('✅ Sincronizar', data);
            } catch (error) {
                addResult('❌ Sincronizar', { error: error.message }, true);
            }
        }

        async function testStats() {
            try {
                const response = await fetch(`${API_BASE}/whatsapp/stats`);
                const data = await response.json();
                addResult('✅ Estatísticas', data);
            } catch (error) {
                addResult('❌ Estatísticas', { error: error.message }, true);
            }
        }

        // Teste automático ao carregar
        window.onload = function() {
            addResult('🚀 Iniciando testes...', { timestamp: new Date().toISOString() });
            setTimeout(testHealth, 500);
        };
    </script>
</body>
</html>
