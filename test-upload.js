import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simular o processo de upload e processamento
async function testDocumentUpload() {
  try {
    console.log('🚀 Iniciando teste de upload de documento...\n');

    // 1. Carregar o arquivo
    const filePath = path.join(__dirname, 'public', 'test-documents', 'Descrição_Camaras.txt');
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const fileStats = fs.statSync(filePath);

    console.log('📄 Documento: Descrição_Camaras.txt');
    console.log('📊 Tamanho:', fileStats.size, 'bytes');
    console.log('📝 Caracteres:', fileContent.length);
    console.log('');

    // 2. Simular o processamento que aconteceria no sistema
    console.log('🔄 Simulando processamento...');
    
    // Dividir em chunks (como faz o documentProcessor)
    const chunkSize = 1000;
    const overlap = 200;
    const chunks = [];
    
    for (let i = 0; i < fileContent.length; i += chunkSize - overlap) {
      const chunk = fileContent.substring(i, i + chunkSize);
      chunks.push({
        index: chunks.length,
        content: chunk,
        size: chunk.length
      });
    }

    console.log('📦 Chunks criados:', chunks.length);
    console.log('');

    // 3. Mostrar alguns chunks como exemplo
    console.log('📋 Exemplos de chunks que seriam processados:');
    chunks.slice(0, 3).forEach((chunk, index) => {
      console.log(`\n--- Chunk ${index + 1} (${chunk.size} chars) ---`);
      console.log(chunk.content.substring(0, 150) + '...');
    });

    console.log('\n✅ Processamento simulado concluído!');
    console.log('');
    console.log('🎯 Próximos passos:');
    console.log('1. Acesse: http://localhost:3001');
    console.log('2. Vá para a seção "Gerenciar Documentos"');
    console.log('3. Faça upload do arquivo: public/test-documents/Descrição_Camaras.txt');
    console.log('4. Aguarde o processamento (status: uploading → processing → ready)');
    console.log('5. Teste perguntando: "De que se trata o documento Descrição Camaras?"');
    console.log('');
    console.log('📝 Perguntas de teste sugeridas:');
    console.log('- "Quantos vereadores tem a Câmara de Parnamirim?"');
    console.log('- "Quais são as comissões permanentes da Câmara?"');
    console.log('- "Como funciona o processo legislativo municipal?"');
    console.log('- "Quais são os instrumentos de fiscalização dos vereadores?"');
    console.log('- "Como a população pode participar das sessões?"');

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }
}

testDocumentUpload();
