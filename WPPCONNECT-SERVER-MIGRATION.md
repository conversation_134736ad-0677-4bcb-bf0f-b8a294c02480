# 🔄 Migração WPPConnect: Biblioteca → Server

Este documento descreve a migração completa da integração WPPConnect de biblioteca Node.js para WPPConnect Server (API RESTful).

## 📋 Resumo da Migração

### ✅ O que foi alterado:

1. **Substituição do WhatsAppService.js** → **WhatsAppHttpService.js**
   - Migração de biblioteca direta para chamadas HTTP
   - Mantida compatibilidade de interface
   - Adicionado suporte a webhooks

2. **Configuração do WPPConnect Server**
   - Docker Compose configurado
   - Arquivo de configuração criado
   - Script de gerenciamento incluído

3. **Atualização das rotas**
   - Webhook atualizado para processar eventos do WPPConnect Server
   - Rotas mantidas compatíveis

4. **Remoção de dependências**
   - `@wppconnect-team/wppconnect` removido
   - Dependências desnecessárias limpas

## 🚀 Como usar o WPPConnect Server

### 1. Iniciar o WPPConnect Server

```bash
# Usando Docker Compose
docker-compose -f docker-compose.wppconnect.yml up -d

# Ou usando o script de gerenciamento
node scripts/wppconnect-server.js start
```

### 2. Verificar status

```bash
# Verificar se o container está rodando
node scripts/wppconnect-server.js status

# Verificar saúde da API
node scripts/wppconnect-server.js health
```

### 3. Visualizar logs

```bash
# Ver logs em tempo real
node scripts/wppconnect-server.js logs
```

### 4. Parar o servidor

```bash
# Parar o WPPConnect Server
node scripts/wppconnect-server.js stop
```

## 🔧 Configuração

### Variáveis de Ambiente (.env)

```env
# WPPConnect Server
WPPCONNECT_SERVER_URL=http://localhost:21465
WPPCONNECT_SECRET_KEY=vereadora-rafaela-secret-2024
JWT_SECRET=vereadora-rafaela-jwt-2024
WHATSAPP_SESSION_NAME=vereadora-rafaela
WEBHOOK_URL=http://host.docker.internal:3001/api/whatsapp/webhook
VITE_WHATSAPP_BACKEND_URL=http://localhost:3001
```

### Portas utilizadas

- **21465**: WPPConnect Server API
- **3001**: Backend da Vereadora Rafaela
- **3000**: Frontend da aplicação

## 📡 Endpoints da API

### WPPConnect Server (porta 21465)

- `GET /api/vereadora-rafaela/status` - Status da sessão
- `POST /api/vereadora-rafaela/start-session` - Iniciar sessão
- `GET /api/vereadora-rafaela/qr-code` - Obter QR Code
- `POST /api/vereadora-rafaela/send-message` - Enviar mensagem
- `GET /api/vereadora-rafaela/all-chats` - Listar conversas

### Backend Vereadora Rafaela (porta 3001)

- `GET /api/whatsapp/status` - Status do WhatsApp
- `POST /api/whatsapp/send` - Enviar mensagem
- `POST /api/webhook/whatsapp` - Webhook para eventos

## 🔄 Fluxo de Funcionamento

1. **Inicialização**:
   - WPPConnect Server inicia em container Docker
   - Backend conecta-se ao WPPConnect Server via HTTP
   - Sessão WhatsApp é inicializada

2. **Conexão WhatsApp**:
   - QR Code é gerado pelo WPPConnect Server
   - Backend obtém QR Code via API
   - Frontend exibe QR Code para usuário

3. **Mensagens**:
   - Mensagens recebidas → Webhook → Backend → Processamento RAG
   - Mensagens enviadas → Backend → WPPConnect Server → WhatsApp

## 🛠️ Vantagens da Migração

### ✅ Benefícios:

1. **Isolamento**: WhatsApp roda em container separado
2. **Escalabilidade**: Múltiplas aplicações podem usar o mesmo servidor
3. **Estabilidade**: Menos dependências diretas no código
4. **Manutenção**: Atualizações independentes
5. **Monitoramento**: APIs RESTful facilitam monitoramento
6. **Flexibilidade**: Pode ser usado com outras linguagens

### 🔧 Recursos mantidos:

- ✅ Conexão via QR Code
- ✅ Envio e recebimento de mensagens
- ✅ Sincronização de conversas
- ✅ Sistema anti-ban
- ✅ Webhooks para eventos
- ✅ Persistência de sessão
- ✅ Monitoramento de saúde

## 🚨 Troubleshooting

### Problema: WPPConnect Server não inicia

```bash
# Verificar se Docker está rodando
docker --version

# Verificar logs do container
docker-compose -f docker-compose.wppconnect.yml logs
```

### Problema: Webhook não funciona

1. Verificar se a URL do webhook está correta no .env
2. Verificar se o backend está acessível do container
3. Usar `host.docker.internal` ao invés de `localhost` no webhook

### Problema: QR Code não aparece

```bash
# Verificar status da sessão
curl http://localhost:21465/api/vereadora-rafaela/status

# Verificar QR Code
curl http://localhost:21465/api/vereadora-rafaela/qr-code
```

## 📚 Documentação Adicional

- [WPPConnect Server GitHub](https://github.com/wppconnect-team/wppconnect-server)
- [Documentação da API](https://wppconnect.io/wppconnect-server/)
- [Docker Compose Reference](https://docs.docker.com/compose/)

## 🔄 Próximos Passos

1. **Testar integração completa**
2. **Configurar monitoramento avançado**
3. **Implementar backup automático**
4. **Otimizar performance**
5. **Documentar APIs customizadas**

---

**Status da Migração**: ✅ Concluída
**Data**: Janeiro 2025
**Versão**: 2.0.0
