import { createClient } from '@supabase/supabase-js';
import { Logger } from '../utils/Logger.js';

/**
 * Serviço Completo do Supabase
 * Persistência avançada para a Vereadora Rafaela de Nilda
 */
export class SupabaseService {
  constructor(config) {
    this.config = config;
    this.logger = new Logger();
    this.supabase = null;
    this.isInitialized = false;
    
    // Cache para otimização
    this.cache = new Map();
    this.maxCacheSize = 500;
    this.cacheTTL = 300000; // 5 minutos
  }

  async initialize() {
    try {
      this.logger.info('🗄️ Inicializando Supabase Service...');
      
      if (!this.config.supabase.url || !this.config.supabase.anonKey) {
        throw new Error('Configurações do Supabase não encontradas');
      }

      this.supabase = createClient(
        this.config.supabase.url,
        this.config.supabase.anonKey,
        {
          auth: {
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: false
          },
          realtime: {
            params: {
              eventsPerSecond: 10
            }
          }
        }
      );

      // Testar conexão
      await this.testConnection();
      
      this.isInitialized = true;
      this.logger.info('✅ Supabase Service inicializado com sucesso');
      
      return true;
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar Supabase:', error);
      this.isInitialized = false;
      return false;
    }
  }

  async testConnection() {
    try {
      const { data, error } = await this.supabase
        .from('conversations')
        .select('count')
        .limit(1);
      
      if (error && !error.message.includes('relation "conversations" does not exist')) {
        throw error;
      }
      
      this.logger.info('✅ Conexão com Supabase testada com sucesso');
    } catch (error) {
      throw new Error(`Falha no teste de conexão: ${error.message}`);
    }
  }

  /**
   * CONVERSAS - Gerenciamento completo
   */
  
  async saveConversation(phoneNumber, userMessage, botResponse, metadata = {}) {
    try {
      const conversationData = {
        phone_number: phoneNumber,
        user_message: userMessage,
        bot_response: botResponse,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          messageType: metadata.messageType || 'general',
          sentiment: metadata.sentiment || 'neutral',
          processingTime: metadata.processingTime || 0
        },
        created_at: new Date().toISOString()
      };

      const { data, error } = await this.supabase
        .from('conversations')
        .insert(conversationData)
        .select()
        .single();

      if (error) throw error;

      // Atualizar estatísticas
      await this.updateUserStats(phoneNumber);
      
      this.logger.info(`💾 Conversa salva: ${phoneNumber}`);
      return data;
      
    } catch (error) {
      this.logger.error('❌ Erro ao salvar conversa:', error);
      throw error;
    }
  }

  async getConversationHistory(phoneNumber, limit = 10) {
    try {
      const cacheKey = `history_${phoneNumber}_${limit}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.supabase
        .from('conversations')
        .select('*')
        .eq('phone_number', phoneNumber)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      this.saveToCache(cacheKey, data);
      return data || [];
      
    } catch (error) {
      this.logger.error('❌ Erro ao buscar histórico:', error);
      return [];
    }
  }

  async getConversationContext(phoneNumber, contextSize = 5) {
    try {
      const history = await this.getConversationHistory(phoneNumber, contextSize);
      
      return {
        phoneNumber,
        messageCount: history.length,
        lastInteraction: history[0]?.created_at,
        recentMessages: history.map(conv => ({
          role: 'user',
          content: conv.user_message,
          timestamp: conv.created_at
        })).concat(history.map(conv => ({
          role: 'assistant', 
          content: conv.bot_response,
          timestamp: conv.created_at
        }))).sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)).slice(-contextSize)
      };
    } catch (error) {
      this.logger.error('❌ Erro ao obter contexto:', error);
      return { phoneNumber, messageCount: 0, recentMessages: [] };
    }
  }

  /**
   * USUÁRIOS - Estatísticas e perfis
   */
  
  async updateUserStats(phoneNumber) {
    try {
      const { data: existing, error: selectError } = await this.supabase
        .from('user_stats')
        .select('*')
        .eq('phone_number', phoneNumber)
        .single();

      if (selectError && selectError.code !== 'PGRST116') {
        throw selectError;
      }

      const now = new Date().toISOString();
      
      if (existing) {
        // Atualizar estatísticas existentes
        const { error } = await this.supabase
          .from('user_stats')
          .update({
            message_count: existing.message_count + 1,
            last_interaction: now,
            updated_at: now
          })
          .eq('phone_number', phoneNumber);

        if (error) throw error;
      } else {
        // Criar novas estatísticas
        const { error } = await this.supabase
          .from('user_stats')
          .insert({
            phone_number: phoneNumber,
            message_count: 1,
            first_interaction: now,
            last_interaction: now,
            created_at: now,
            updated_at: now
          });

        if (error) throw error;
      }
      
    } catch (error) {
      this.logger.warn('⚠️ Erro ao atualizar estatísticas do usuário:', error);
    }
  }

  async getUserProfile(phoneNumber) {
    try {
      const { data, error } = await this.supabase
        .from('user_stats')
        .select('*')
        .eq('phone_number', phoneNumber)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      return data || {
        phone_number: phoneNumber,
        message_count: 0,
        first_interaction: null,
        last_interaction: null,
        is_frequent_user: false
      };
      
    } catch (error) {
      this.logger.error('❌ Erro ao buscar perfil do usuário:', error);
      return null;
    }
  }

  /**
   * DOCUMENTOS - Gerenciamento de conhecimento
   */
  
  async saveDocument(title, content, metadata = {}) {
    try {
      const documentData = {
        title,
        content,
        file_type: metadata.fileType || 'text',
        file_size: content.length,
        status: 'ready',
        metadata: {
          ...metadata,
          uploadedAt: new Date().toISOString(),
          source: 'whatsapp_system'
        },
        created_at: new Date().toISOString()
      };

      const { data, error } = await this.supabase
        .from('documents')
        .insert(documentData)
        .select()
        .single();

      if (error) throw error;

      this.logger.info(`📄 Documento salvo: ${title}`);
      return data;
      
    } catch (error) {
      this.logger.error('❌ Erro ao salvar documento:', error);
      throw error;
    }
  }

  async getDocuments(limit = 50, status = 'ready') {
    try {
      const cacheKey = `docs_${limit}_${status}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.supabase
        .from('documents')
        .select('*')
        .eq('status', status)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      this.saveToCache(cacheKey, data);
      return data || [];
      
    } catch (error) {
      this.logger.error('❌ Erro ao buscar documentos:', error);
      return [];
    }
  }

  async searchDocuments(query, limit = 10) {
    try {
      const { data, error } = await this.supabase
        .from('documents')
        .select('*')
        .textSearch('content', query)
        .eq('status', 'ready')
        .limit(limit);

      if (error) throw error;

      return data || [];
      
    } catch (error) {
      this.logger.error('❌ Erro na busca de documentos:', error);
      return [];
    }
  }

  /**
   * ANALYTICS - Métricas e relatórios
   */
  
  async getAnalytics(period = '7d') {
    try {
      const cacheKey = `analytics_${period}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      // Calcular data de início baseada no período
      const endDate = new Date();
      const startDate = new Date();
      
      switch (period) {
        case '1d':
          startDate.setDate(endDate.getDate() - 1);
          break;
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        default:
          startDate.setDate(endDate.getDate() - 7);
      }

      // Buscar dados de conversas
      const { data: conversations, error: convError } = await this.supabase
        .from('conversations')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (convError) throw convError;

      // Buscar dados de usuários
      const { data: users, error: usersError } = await this.supabase
        .from('user_stats')
        .select('*');

      if (usersError) throw usersError;

      // Calcular métricas
      const analytics = {
        period,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        totalMessages: conversations?.length || 0,
        uniqueUsers: new Set(conversations?.map(c => c.phone_number)).size || 0,
        totalUsers: users?.length || 0,
        averageResponseTime: this.calculateAverageResponseTime(conversations),
        messagesByDay: this.groupMessagesByDay(conversations),
        topUsers: this.getTopUsers(users, 5),
        sentimentAnalysis: this.analyzeSentiments(conversations),
        messageTypes: this.analyzeMessageTypes(conversations)
      };

      this.saveToCache(cacheKey, analytics, 600000); // Cache por 10 minutos
      return analytics;
      
    } catch (error) {
      this.logger.error('❌ Erro ao gerar analytics:', error);
      return null;
    }
  }

  calculateAverageResponseTime(conversations) {
    if (!conversations || conversations.length === 0) return 0;
    
    const times = conversations
      .filter(c => c.metadata?.processingTime)
      .map(c => c.metadata.processingTime);
    
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }

  groupMessagesByDay(conversations) {
    if (!conversations) return {};
    
    const groups = {};
    conversations.forEach(conv => {
      const date = new Date(conv.created_at).toISOString().split('T')[0];
      groups[date] = (groups[date] || 0) + 1;
    });
    
    return groups;
  }

  getTopUsers(users, limit = 5) {
    if (!users) return [];
    
    return users
      .sort((a, b) => b.message_count - a.message_count)
      .slice(0, limit)
      .map(user => ({
        phone_number: user.phone_number,
        message_count: user.message_count,
        last_interaction: user.last_interaction
      }));
  }

  analyzeSentiments(conversations) {
    if (!conversations) return {};
    
    const sentiments = {};
    conversations.forEach(conv => {
      const sentiment = conv.metadata?.sentiment || 'neutral';
      sentiments[sentiment] = (sentiments[sentiment] || 0) + 1;
    });
    
    return sentiments;
  }

  analyzeMessageTypes(conversations) {
    if (!conversations) return {};
    
    const types = {};
    conversations.forEach(conv => {
      const type = conv.metadata?.messageType || 'general';
      types[type] = (types[type] || 0) + 1;
    });
    
    return types;
  }

  /**
   * CACHE - Gerenciamento de cache
   */
  
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > this.cacheTTL) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  saveToCache(key, data, ttl = null) {
    if (this.cache.size >= this.maxCacheSize) {
      // Remover entrada mais antiga
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.cacheTTL
    });
  }

  clearCache() {
    this.cache.clear();
    this.logger.info('🗑️ Cache do Supabase limpo');
  }

  /**
   * HEALTH CHECK e ESTATÍSTICAS
   */
  
  async healthCheck() {
    try {
      if (!this.isInitialized) {
        return { status: 'offline', error: 'Não inicializado' };
      }
      
      const startTime = Date.now();
      
      // Teste de conexão simples
      const { error } = await this.supabase
        .from('conversations')
        .select('count')
        .limit(1);
      
      const responseTime = Date.now() - startTime;
      
      if (error && !error.message.includes('relation "conversations" does not exist')) {
        throw error;
      }
      
      return {
        status: 'online',
        responseTime,
        cacheSize: this.cache.size,
        lastTest: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        lastTest: new Date().toISOString()
      };
    }
  }

  getStats() {
    return {
      initialized: this.isInitialized,
      cacheSize: this.cache.size,
      maxCacheSize: this.maxCacheSize,
      cacheTTL: this.cacheTTL,
      url: this.config.supabase.url ? 'configured' : 'not_configured'
    };
  }
}
