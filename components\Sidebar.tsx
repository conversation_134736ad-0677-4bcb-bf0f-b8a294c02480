import React from 'react';

interface SidebarProps {
  activeTab: 'chat' | 'documents' | 'monitoring' | 'whatsapp';
  onTabChange: (tab: 'chat' | 'documents' | 'monitoring' | 'whatsapp') => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    {
      id: 'chat' as const,
      name: 'Cha<PERSON>',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      )
    },
    {
      id: 'documents' as const,
      name: 'Documentos',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      id: 'monitoring' as const,
      name: 'Monitoramento',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    },
    {
      id: 'whatsapp' as const,
      name: 'WhatsApp',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21l4-4 4 4M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
        </svg>
      )
    }
  ];

  return (
    <div className="sidebar-modern fixed right-0 top-0 h-full w-64 z-40 flex flex-col">
      {/* Header da Sidebar */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <h2 className="text-xl font-bold text-gray-800 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Menu
        </h2>
        <p className="text-sm text-gray-600 mt-2">Navegue pelas funcionalidades</p>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-6">
        <div className="space-y-3">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`
                sidebar-nav-item w-full flex items-center space-x-4 px-4 py-4 rounded-xl text-left
                ${activeTab === tab.id
                  ? 'active text-blue-700'
                  : 'text-gray-700 sidebar-gradient-hover'
                }
                group
              `}
            >
              {/* Ícone */}
              <div className="icon relative z-10">
                {tab.icon}
              </div>

              {/* Nome */}
              <span className={`
                relative z-10 font-medium transition-all duration-200
                ${activeTab === tab.id ? 'font-semibold' : 'group-hover:font-semibold'}
              `}>
                {tab.name}
              </span>

              {/* Indicador ativo */}
              {activeTab === tab.id && (
                <div className="sidebar-activity-indicator" />
              )}
            </button>
          ))}
        </div>
      </nav>

      {/* Footer da Sidebar */}
      <div className="p-6 border-t border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <div className="text-xs font-medium text-gray-600">Sistema Online</div>
          </div>
          <div className="text-sm font-semibold text-gray-700">
            Vereadora Rafaela de Nilda
          </div>
          <div className="text-xs text-gray-500 mt-1">
            Parnamirim/RN • 2025-2028
          </div>
          <div className="mt-3 text-xs text-gray-400">
            💖 Servindo nossa cidade
          </div>
        </div>
      </div>
    </div>
  );
};
