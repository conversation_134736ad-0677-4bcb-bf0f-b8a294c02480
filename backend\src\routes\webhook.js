import express from 'express';
import crypto from 'crypto';
import { Logger } from '../utils/Logger.js';

const router = express.Router();
const logger = new Logger();

// Middleware para verificar assinatura do webhook (se configurado)
const verifyWebhookSignature = (req, res, next) => {
  const webhookSecret = process.env.WEBHOOK_SECRET;
  
  if (!webhookSecret) {
    // Se não há secret configurado, pular verificação
    return next();
  }
  
  const signature = req.headers['x-webhook-signature'];
  if (!signature) {
    return res.status(401).json({
      error: 'Assinatura ausente',
      message: 'Header x-webhook-signature é obrigatório',
      code: 'SIGNATURE_MISSING'
    });
  }
  
  const body = JSON.stringify(req.body);
  const expectedSignature = crypto
    .createHmac('sha256', webhookSecret)
    .update(body)
    .digest('hex');
  
  if (signature !== `sha256=${expectedSignature}`) {
    logger.security('Webhook signature mismatch', { 
      received: signature,
      expected: `sha256=${expectedSignature}`
    });
    
    return res.status(401).json({
      error: 'Assinatura inválida',
      message: 'A assinatura do webhook não confere',
      code: 'SIGNATURE_INVALID'
    });
  }
  
  next();
};

// POST /api/webhook/whatsapp - Webhook para eventos do WPPConnect Server
router.post('/whatsapp', verifyWebhookSignature, async (req, res) => {
  try {
    // O WPPConnect Server envia dados em formato diferente
    const webhookData = req.body;

    logger.info('Webhook WPPConnect recebido:', {
      session: webhookData.session,
      event: webhookData.event || 'unknown',
      hasData: !!webhookData.data
    });

    // Obter o serviço WhatsApp da aplicação
    const whatsappService = req.app.locals.whatsappService;

    if (whatsappService && typeof whatsappService.processWebhook === 'function') {
      // Verificar e sincronizar status de conexão se necessário
      if (webhookData.event === 'message' && !whatsappService.isConnected) {
        logger.info('🔄 Sincronizando status de conexão para processamento de mensagem...');
        try {
          const serverStatus = await whatsappService.getSessionStatus();
          if (serverStatus && serverStatus.connected) {
            logger.info('✅ Forçando sincronização: servidor está conectado');
            whatsappService.isConnected = true;
          }
        } catch (error) {
          logger.warn('⚠️ Erro ao verificar status do servidor:', error.message);
        }
      }

      await whatsappService.processWebhook(webhookData);
    } else {
      // Fallback para processamento manual
      await processWPPConnectWebhook(webhookData);
    }

    res.json({
      success: true,
      message: 'Webhook WPPConnect processado com sucesso',
      session: webhookData.session,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao processar webhook WPPConnect:', error);
    res.status(500).json({
      error: 'Erro ao processar webhook',
      message: error.message,
      code: 'WEBHOOK_ERROR'
    });
  }
});

// POST /api/webhook/rag - Webhook para eventos do sistema RAG
router.post('/rag', verifyWebhookSignature, async (req, res) => {
  try {
    const { event, data } = req.body;
    
    switch (event) {
      case 'document_processed':
        await documentService.updateDocumentStatus(data.documentId, 'processed');
        break;
      case 'knowledge_base_updated':
        await ragService.refreshKnowledgeBase();
        break;
      // Adicionar mais casos conforme necessário
    }
    
    res.json({ success: true, message: 'Webhook processado com sucesso' });
  } catch (error) {
    logger.error('Erro ao processar webhook RAG:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// GET /api/webhook/test - Endpoint para testar webhooks
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Webhook endpoint funcionando',
    timestamp: new Date().toISOString(),
    config: {
      webhookSecretConfigured: !!process.env.WEBHOOK_SECRET,
      webhookUrlConfigured: !!process.env.WEBHOOK_URL
    }
  });
});

// POST /api/webhook/test - Testar envio de webhook
router.post('/test', async (req, res) => {
  try {
    const { event, data } = req.body;
    
    if (!event) {
      return res.status(400).json({
        error: 'Evento obrigatório',
        message: 'O campo event é obrigatório',
        code: 'VALIDATION_ERROR'
      });
    }
    
    // Simular processamento do webhook
    logger.info(`Webhook de teste: ${event}`, { event, data });
    
    // Se há URL de webhook configurada, enviar para lá também
    if (process.env.WEBHOOK_URL) {
      await sendWebhook(event, data);
    }
    
    res.json({
      success: true,
      message: 'Webhook de teste processado',
      event,
      data,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro no webhook de teste:', error);
    res.status(500).json({
      error: 'Erro no teste',
      message: error.message,
      code: 'TEST_ERROR'
    });
  }
});

// Função para processar webhooks do WPPConnect Server
async function processWPPConnectWebhook(webhookData) {
  const { event, session, data } = webhookData;

  logger.info(`Processando webhook WPPConnect: ${event} para sessão ${session}`);

  switch (event) {
    case 'qrcode':
      await handleWPPConnectQRCode(data, session);
      break;

    case 'connection':
      await handleWPPConnectConnection(data, session);
      break;

    case 'message':
      await handleWPPConnectMessage(data, session);
      break;

    case 'disconnected':
      await handleWPPConnectDisconnected(data, session);
      break;

    case 'status':
      await handleWPPConnectStatus(data, session);
      break;

    default:
      logger.debug(`Evento WPPConnect não tratado: ${event}`);
  }
}

async function handleWPPConnectQRCode(data, session) {
  logger.info('QR Code recebido via webhook', { session, hasQRCode: !!data.qrcode });

  // Aqui você pode implementar lógica adicional para QR Code
  // como salvar em arquivo, enviar notificação, etc.
}

async function handleWPPConnectConnection(data, session) {
  logger.info('Status de conexão via webhook', {
    session,
    state: data.state,
    connected: data.connected
  });

  // Implementar lógica para mudanças de conexão
}

async function handleWPPConnectMessage(data, session) {
  logger.info('Mensagem recebida via webhook', {
    session,
    from: data.from,
    messageType: data.type,
    isFromMe: data.fromMe
  });

  // Implementar lógica para processar mensagens
  // Aqui você pode integrar com o sistema RAG da Vereadora Rafaela
}

async function handleWPPConnectDisconnected(data, session) {
  logger.warn('Desconexão detectada via webhook', { session });

  // Implementar lógica para desconexão
}

async function handleWPPConnectStatus(data, session) {
  logger.info('Status atualizado via webhook', { session, status: data });

  // Implementar lógica para atualizações de status
}

// Handlers para diferentes tipos de eventos (mantidos para compatibilidade)

async function handleMessageWebhook(data) {
  const { from, message, timestamp } = data;

  logger.info('Processando webhook de mensagem', {
    from,
    messageLength: message?.length || 0,
    timestamp
  });

  // Aqui você pode implementar lógica adicional
  // como notificações, logging especial, etc.
}

async function handleStatusChangeWebhook(data) {
  const { sessionId, status, previousStatus } = data;
  
  logger.info('Processando webhook de mudança de status', {
    sessionId,
    status,
    previousStatus
  });
  
  // Implementar lógica para mudanças de status
  // como notificações para administradores
}

async function handleQRCodeWebhook(data) {
  const { qrCode, sessionId } = data;
  
  logger.info('Processando webhook de QR Code', {
    sessionId,
    hasQRCode: !!qrCode
  });
  
  // Implementar lógica para novo QR Code
  // como envio por email para administradores
}

async function handleConnectionWebhook(data) {
  const { sessionId, connected, timestamp } = data;
  
  logger.info('Processando webhook de conexão', {
    sessionId,
    connected,
    timestamp
  });
  
  // Implementar lógica para mudanças de conexão
}

async function handleDocumentProcessedWebhook(data) {
  const { documentId, status, processingTime } = data;
  
  logger.info('Processando webhook de documento', {
    documentId,
    status,
    processingTime
  });
  
  // Implementar lógica para documentos processados
}

async function handleConversationStartedWebhook(data) {
  const { conversationId, userId, platform } = data;
  
  logger.info('Processando webhook de conversa iniciada', {
    conversationId,
    userId,
    platform
  });
  
  // Implementar lógica para novas conversas
}

async function handleResponseGeneratedWebhook(data) {
  const { conversationId, query, response, confidence } = data;
  
  logger.info('Processando webhook de resposta gerada', {
    conversationId,
    queryLength: query?.length || 0,
    responseLength: response?.length || 0,
    confidence
  });
  
  // Implementar lógica para respostas geradas
}

// Função para enviar webhook para URL externa
async function sendWebhook(event, data) {
  try {
    const webhookUrl = process.env.WEBHOOK_URL;
    if (!webhookUrl) return;
    
    const payload = {
      event,
      data,
      timestamp: new Date().toISOString(),
      source: 'vereadora-rafaela-whatsapp-backend'
    };
    
    const body = JSON.stringify(payload);
    let headers = {
      'Content-Type': 'application/json',
      'User-Agent': 'Vereadora-Rafaela-Webhook/1.0.0'
    };
    
    // Adicionar assinatura se secret estiver configurado
    if (process.env.WEBHOOK_SECRET) {
      const signature = crypto
        .createHmac('sha256', process.env.WEBHOOK_SECRET)
        .update(body)
        .digest('hex');
      
      headers['X-Webhook-Signature'] = `sha256=${signature}`;
    }
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers,
      body
    });
    
    if (!response.ok) {
      throw new Error(`Webhook failed: ${response.status} ${response.statusText}`);
    }
    
    logger.info('Webhook enviado com sucesso', {
      url: webhookUrl,
      event,
      status: response.status
    });
    
  } catch (error) {
    logger.error('Erro ao enviar webhook:', error);
  }
}

export default router;