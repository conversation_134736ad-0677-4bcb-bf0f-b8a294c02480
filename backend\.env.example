# ========================================
# CONFIGURAÇÕES DO SERVIDOR CENTRALIZADO
# Backend Vereadora Rafaela de Nilda v2.0
# ========================================

# Configurações Básicas do Servidor
PORT=3001
HOST=localhost
NODE_ENV=development
BODY_LIMIT=10mb
SERVER_TIMEOUT=30000

# Configurações de CORS
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CORS_CREDENTIALS=true
FRONTEND_URL=http://localhost:3000

# Configurações de Segurança
ENABLE_CSP=true
ENABLE_ORIGIN_VERIFICATION=false
ADMIN_API_KEY=admin-key-2024
WHATSAPP_API_KEY=whatsapp-key-2024

# Configurações de Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESS=false

# ========================================
# CONFIGURAÇÕES DO WHATSAPP
# ========================================

# Configurações Básicas do WhatsApp
WHATSAPP_SESSION_NAME=vereadora-rafaela
WHATSAPP_AUTO_CLOSE=false
WHATSAPP_DISABLE_WELCOME=false

# Seleção do Serviço WhatsApp (escolha apenas um)
# WHATSAPP_USE_SIMULATOR=true    # Para desenvolvimento (simulador)
# WHATSAPP_USE_REAL=true         # Para produção (WPPConnect direto)
WHATSAPP_USE_HTTP=true           # Para produção (WPPConnect Server) - RECOMENDADO

# Configurações de Reconexão
WHATSAPP_MAX_CONNECTION_ATTEMPTS=5
WHATSAPP_RECONNECT_DELAY=30000
WHATSAPP_MAX_RECONNECT_ATTEMPTS=10

# ========================================
# CONFIGURAÇÕES DO WPPCONNECT SERVER
# ========================================

# Configurações Básicas do WPPConnect Server
WPPCONNECT_SERVER_URL=http://localhost:21465
WPPCONNECT_PORT=21465
WPPCONNECT_SECRET_KEY=vereadora-rafaela-secret-2024
WEBHOOK_URL=http://localhost:3001/api/webhook/whatsapp

# Configurações de Timeout
WPPCONNECT_REQUEST_TIMEOUT=30000
WPPCONNECT_HEALTH_CHECK_INTERVAL=60000

# ========================================
# CONFIGURAÇÕES DO RAG
# ========================================

# Configurações Básicas do RAG
RAG_ENABLED=true
RAG_CACHE_ENABLED=true
RAG_MAX_DOCUMENTS=100
RAG_CHUNK_SIZE=1000
RAG_CHUNK_OVERLAP=200

# ========================================
# CONFIGURAÇÕES DO GEMINI
# ========================================

# API do Google Gemini
GEMINI_API_KEY=your-gemini-api-key-here
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=2048
GEMINI_TEMPERATURE=0.7

# ========================================
# CONFIGURAÇÕES DO SUPABASE
# ========================================

# Configurações do Supabase
SUPABASE_URL=your-supabase-url-here
SUPABASE_ANON_KEY=your-supabase-anon-key-here
SUPABASE_SERVICE_KEY=your-supabase-service-key-here

# ========================================
# CONFIGURAÇÕES DE LOGGING
# ========================================

# Configurações de Log
LOG_LEVEL=info
ENABLE_FILE_LOGGING=true
LOG_DIRECTORY=logs
LOG_MAX_FILE_SIZE=10m
LOG_MAX_FILES=5

# ========================================
# CONFIGURAÇÕES DE CACHE
# ========================================

# Configurações de Cache
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ========================================
# CONFIGURAÇÕES DE MONITORAMENTO
# ========================================

# Configurações de Monitoramento
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=false

# ========================================
# CONFIGURAÇÕES DE ANTI-BAN
# ========================================

# Configurações de Anti-Ban
ANTIBAN_ENABLED=true
ANTIBAN_MIN_DELAY=1000
ANTIBAN_MAX_DELAY=5000
ANTIBAN_RISK_THRESHOLD=70

# ========================================
# CONFIGURAÇÕES DE BACKUP
# ========================================

# Configurações de Backup
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400000
BACKUP_MAX_FILES=7
BACKUP_DIRECTORY=data/backups

# ========================================
# CONFIGURAÇÕES DE SESSÃO
# ========================================

# Configurações de Sessão
SESSION_DIRECTORY=data/sessions
SESSION_AUTO_CLEANUP=true
SESSION_CLEANUP_INTERVAL=3600000
SESSION_MAX_AGE=**********

# ========================================
# CONFIGURAÇÕES DA VEREADORA (LEGADO)
# ========================================

# Informações da Vereadora
VEREADORA_NAME=Rafaela de Nilda
VEREADORA_TITLE=Vereadora
MUNICIPIO=Parnamirim
ESTADO=RN
GABINETE_TELEFONE=(84) 99999-9999
GABINETE_EMAIL=<EMAIL>

# Configurações de Horário de Atendimento
HORARIO_INICIO=08:00
HORARIO_FIM=18:00
DIAS_FUNCIONAMENTO=1,2,3,4,5

# Configurações de Mensagens Automáticas
AUTO_REPLY_ENABLED=true
WELCOME_MESSAGE_ENABLED=true
BUSINESS_HOURS_ONLY=false
