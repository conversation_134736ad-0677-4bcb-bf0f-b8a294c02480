-- ========================================
-- CONFIGURAÇÃO COMPLETA DO SUPABASE
-- Sistema Vereadora Rafaela de Nilda
-- ========================================

-- Habilitar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ========================================
-- TABELA DE CONVERSAS
-- ========================================

CREATE TABLE IF NOT EXISTS conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  phone_number TEXT NOT NULL,
  user_message TEXT NOT NULL,
  bot_response TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_conversations_phone_number ON conversations(phone_number);
CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversations_metadata ON conversations USING GIN(metadata);

-- ========================================
-- TABELA DE ESTATÍSTICAS DE USUÁRIOS
-- ========================================

CREATE TABLE IF NOT EXISTS user_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  phone_number TEXT UNIQUE NOT NULL,
  message_count INTEGER DEFAULT 0,
  first_interaction TIMESTAMP WITH TIME ZONE,
  last_interaction TIMESTAMP WITH TIME ZONE,
  is_frequent_user BOOLEAN DEFAULT FALSE,
  user_preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_user_stats_phone_number ON user_stats(phone_number);
CREATE INDEX IF NOT EXISTS idx_user_stats_message_count ON user_stats(message_count DESC);
CREATE INDEX IF NOT EXISTS idx_user_stats_last_interaction ON user_stats(last_interaction DESC);

-- ========================================
-- TABELA DE DOCUMENTOS
-- ========================================

CREATE TABLE IF NOT EXISTS documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  file_path TEXT,
  file_type TEXT DEFAULT 'text',
  file_size INTEGER DEFAULT 0,
  status TEXT DEFAULT 'processing',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE
);

-- Índices para performance e busca
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_documents_file_type ON documents(file_type);
CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_documents_content_search ON documents USING GIN(to_tsvector('portuguese', content));
CREATE INDEX IF NOT EXISTS idx_documents_title_search ON documents USING GIN(to_tsvector('portuguese', title));

-- ========================================
-- TABELA DE CHUNKS DE DOCUMENTOS (Para RAG)
-- ========================================

CREATE TABLE IF NOT EXISTS document_chunks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
  chunk_text TEXT NOT NULL,
  chunk_index INTEGER NOT NULL,
  embedding VECTOR(384), -- Para embeddings (requer extensão pgvector)
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_document_chunks_chunk_index ON document_chunks(chunk_index);

-- ========================================
-- TABELA DE SESSÕES WHATSAPP
-- ========================================

CREATE TABLE IF NOT EXISTS whatsapp_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_name TEXT UNIQUE NOT NULL,
  status TEXT DEFAULT 'disconnected',
  qr_code TEXT,
  last_activity TIMESTAMP WITH TIME ZONE,
  connection_info JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_whatsapp_sessions_session_name ON whatsapp_sessions(session_name);
CREATE INDEX IF NOT EXISTS idx_whatsapp_sessions_status ON whatsapp_sessions(status);

-- ========================================
-- TABELA DE LOGS DO SISTEMA
-- ========================================

CREATE TABLE IF NOT EXISTS system_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  level TEXT NOT NULL, -- info, warn, error
  message TEXT NOT NULL,
  component TEXT, -- whatsapp, rag, gemini, etc
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_component ON system_logs(component);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at DESC);

-- ========================================
-- TABELA DE MÉTRICAS E ANALYTICS
-- ========================================

CREATE TABLE IF NOT EXISTS analytics_metrics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  metric_name TEXT NOT NULL,
  metric_value NUMERIC,
  metric_data JSONB DEFAULT '{}',
  period_start TIMESTAMP WITH TIME ZONE,
  period_end TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_analytics_metrics_name ON analytics_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_analytics_metrics_period ON analytics_metrics(period_start, period_end);

-- ========================================
-- FUNÇÕES E TRIGGERS
-- ========================================

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para atualizar updated_at
CREATE TRIGGER update_conversations_updated_at 
    BEFORE UPDATE ON conversations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_stats_updated_at 
    BEFORE UPDATE ON user_stats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at 
    BEFORE UPDATE ON documents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_whatsapp_sessions_updated_at 
    BEFORE UPDATE ON whatsapp_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- FUNÇÃO PARA MARCAR USUÁRIOS FREQUENTES
-- ========================================

CREATE OR REPLACE FUNCTION update_frequent_user_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Marcar como usuário frequente se tiver mais de 10 mensagens
    IF NEW.message_count >= 10 THEN
        NEW.is_frequent_user = TRUE;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_frequent_status 
    BEFORE UPDATE ON user_stats 
    FOR EACH ROW EXECUTE FUNCTION update_frequent_user_status();

-- ========================================
-- VIEWS PARA RELATÓRIOS
-- ========================================

-- View para estatísticas diárias
CREATE OR REPLACE VIEW daily_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_messages,
    COUNT(DISTINCT phone_number) as unique_users,
    AVG(CASE 
        WHEN metadata->>'processingTime' IS NOT NULL 
        THEN (metadata->>'processingTime')::numeric 
        ELSE NULL 
    END) as avg_response_time
FROM conversations
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- View para usuários mais ativos
CREATE OR REPLACE VIEW top_users AS
SELECT 
    phone_number,
    message_count,
    first_interaction,
    last_interaction,
    CASE 
        WHEN last_interaction > NOW() - INTERVAL '7 days' THEN 'active'
        WHEN last_interaction > NOW() - INTERVAL '30 days' THEN 'inactive'
        ELSE 'dormant'
    END as status
FROM user_stats
ORDER BY message_count DESC;

-- View para análise de sentimentos
CREATE OR REPLACE VIEW sentiment_analysis AS
SELECT 
    DATE(created_at) as date,
    metadata->>'sentiment' as sentiment,
    COUNT(*) as count
FROM conversations
WHERE metadata->>'sentiment' IS NOT NULL
GROUP BY DATE(created_at), metadata->>'sentiment'
ORDER BY date DESC, sentiment;

-- ========================================
-- POLÍTICAS RLS (Row Level Security)
-- ========================================

-- Habilitar RLS nas tabelas principais
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_logs ENABLE ROW LEVEL SECURITY;

-- Políticas permissivas para desenvolvimento (ajustar para produção)
CREATE POLICY "Allow all operations on conversations" ON conversations FOR ALL USING (true);
CREATE POLICY "Allow all operations on user_stats" ON user_stats FOR ALL USING (true);
CREATE POLICY "Allow all operations on documents" ON documents FOR ALL USING (true);
CREATE POLICY "Allow all operations on whatsapp_sessions" ON whatsapp_sessions FOR ALL USING (true);
CREATE POLICY "Allow all operations on system_logs" ON system_logs FOR ALL USING (true);

-- ========================================
-- DADOS INICIAIS
-- ========================================

-- Inserir sessão padrão do WhatsApp
INSERT INTO whatsapp_sessions (session_name, status, connection_info)
VALUES ('vereadora-rafaela', 'disconnected', '{"created_by": "system", "version": "2.0.0"}')
ON CONFLICT (session_name) DO NOTHING;

-- Inserir documento inicial sobre a Vereadora
INSERT INTO documents (title, content, file_type, status, metadata)
VALUES (
    'Informações da Vereadora Rafaela de Nilda',
    'Vereadora Rafaela de Nilda, eleita para representar o povo de Parnamirim/RN no período 2025-2028. Partido SOLIDARIEDADE. Áreas de atuação: saúde pública, educação, infraestrutura urbana, segurança pública, apoio às famílias, desenvolvimento econômico local e meio ambiente.',
    'text',
    'ready',
    '{"source": "system", "category": "profile", "priority": "high"}'
)
ON CONFLICT DO NOTHING;

-- ========================================
-- COMENTÁRIOS E DOCUMENTAÇÃO
-- ========================================

COMMENT ON TABLE conversations IS 'Armazena todas as conversas entre cidadãos e a Vereadora Rafaela';
COMMENT ON TABLE user_stats IS 'Estatísticas e perfis dos usuários do sistema';
COMMENT ON TABLE documents IS 'Documentos e conhecimento base para o sistema RAG';
COMMENT ON TABLE document_chunks IS 'Chunks de documentos para busca semântica';
COMMENT ON TABLE whatsapp_sessions IS 'Sessões ativas do WhatsApp';
COMMENT ON TABLE system_logs IS 'Logs do sistema para monitoramento';
COMMENT ON TABLE analytics_metrics IS 'Métricas e analytics do sistema';

-- ========================================
-- VERIFICAÇÃO FINAL
-- ========================================

-- Verificar se todas as tabelas foram criadas
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN (
        'conversations', 
        'user_stats', 
        'documents', 
        'document_chunks',
        'whatsapp_sessions',
        'system_logs',
        'analytics_metrics'
    )
ORDER BY tablename;
