import React, { useState } from 'react';
import type { Conversation } from '../types';

interface HistoryPanelProps {
  conversations: Conversation[];
  onLoadConversation: (id: string) => void;
  onNewConversation: () => void;
  onDeleteConversation: (id: string) => void;
  currentConversationId: string | null;
}

export const HistoryPanel: React.FC<HistoryPanelProps> = ({
  conversations,
  onLoadConversation,
  onNewConversation,
  onDeleteConversation,
  currentConversationId
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  const filteredConversations = conversations.filter(conv =>
    conv.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString('pt-BR', {
        weekday: 'short',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      });
    }
  };

  const handleDelete = (id: string) => {
    if (showDeleteConfirm === id) {
      onDeleteConversation(id);
      setShowDeleteConfirm(null);
    } else {
      setShowDeleteConfirm(id);
    }
  };

  const getPreviewText = (conversation: Conversation) => {
    if (conversation.messages.length === 0) return 'Conversa vazia';
    
    const lastUserMessage = conversation.messages
      .filter(msg => msg.sender === 'user')
      .pop();
    
    return lastUserMessage?.content.substring(0, 100) + '...' || 'Sem mensagens do usuário';
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Histórico de Conversas</h2>
          <button
            onClick={onNewConversation}
            className="btn-primary flex items-center space-x-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            <span>Nova Conversa</span>
          </button>
        </div>

        {/* Busca */}
        <div className="relative">
          <input
            type="text"
            placeholder="Buscar conversas..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <svg className="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* Lista de conversas */}
      <div className="flex-1 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="p-6 text-center">
            {searchTerm ? (
              <div>
                <svg className="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <p className="text-gray-500">Nenhuma conversa encontrada para "{searchTerm}"</p>
              </div>
            ) : (
              <div>
                <svg className="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p className="text-gray-500 mb-2">Nenhuma conversa ainda</p>
                <p className="text-sm text-gray-400">Inicie uma nova conversa para começar</p>
              </div>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-200 ${
                  currentConversationId === conversation.id ? 'bg-blue-50 border-r-4 border-blue-500' : ''
                }`}
                onClick={() => onLoadConversation(conversation.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 truncate">
                      {conversation.title}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                      {getPreviewText(conversation)}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                      <span>{formatDate(conversation.updated_at)}</span>
                      <span>{conversation.message_count} mensagens</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {currentConversationId === conversation.id && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(conversation.id);
                      }}
                      className={`p-1 rounded hover:bg-gray-200 transition-colors duration-200 ${
                        showDeleteConfirm === conversation.id ? 'bg-red-100 text-red-600' : 'text-gray-400'
                      }`}
                      title={showDeleteConfirm === conversation.id ? 'Clique novamente para confirmar' : 'Deletar conversa'}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer com estatísticas */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-sm text-gray-600 text-center">
          {conversations.length === 0 ? (
            'Nenhuma conversa salva'
          ) : (
            `${conversations.length} conversa${conversations.length !== 1 ? 's' : ''} salva${conversations.length !== 1 ? 's' : ''}`
          )}
        </div>
      </div>
    </div>
  );
};
