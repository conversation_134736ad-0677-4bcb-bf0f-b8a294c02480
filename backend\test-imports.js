#!/usr/bin/env node

/**
 * Teste de Importações
 * Verifica se todas as importações estão funcionando
 */

console.log('🧪 Testando importações...\n');

async function testImports() {
  const tests = [
    {
      name: 'Express',
      test: () => import('express')
    },
    {
      name: 'ServerConfig',
      test: () => import('./src/config/ServerConfig.js')
    },
    {
      name: 'Logger',
      test: () => import('./src/utils/Logger.js')
    },
    {
      name: 'ServiceManager',
      test: () => import('./src/services/ServiceManager.js')
    },
    {
      name: 'RouteManager',
      test: () => import('./src/routes/RouteManager.js')
    },
    {
      name: 'WPPConnectServer',
      test: () => import('./wppconnect-server.js')
    }
  ];

  for (const { name, test } of tests) {
    try {
      await test();
      console.log(`✅ ${name}`);
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
    }
  }
}

testImports().catch(console.error);
