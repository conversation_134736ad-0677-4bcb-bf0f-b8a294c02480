#!/usr/bin/env node

/**
 * Teste Completo do Sistema
 * Valida todos os servidores e funcionalidades
 */

import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

console.log('🧪 TESTE COMPLETO DO SISTEMA');
console.log('============================\n');

async function testEndpoint(url, method = 'GET', body = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(url, options);
    const data = await response.json();
    
    return {
      success: response.ok,
      status: response.status,
      data
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

async function testServer(serverName, baseUrl) {
  console.log(`\n🔍 Testando ${serverName}...`);
  console.log(`📍 URL: ${baseUrl}`);
  
  const tests = [
    {
      name: 'Informações do Servidor',
      url: `${baseUrl}/`,
      expected: ['message', 'version', 'status']
    },
    {
      name: 'Health Check',
      url: `${baseUrl}/api/health`,
      expected: ['status', 'timestamp', 'uptime']
    },
    {
      name: 'Configuração',
      url: `${baseUrl}/api/config`,
      expected: ['server', 'whatsapp', 'integrations']
    },
    {
      name: 'RAG Query',
      url: `${baseUrl}/api/rag/query`,
      method: 'POST',
      body: { query: 'Olá Vereadora Rafaela!' },
      expected: ['query', 'response', 'metadata']
    },
    {
      name: 'Documentos',
      url: `${baseUrl}/api/documents`,
      expected: ['documents', 'count', 'source']
    }
  ];
  
  // Adicionar testes específicos do WhatsApp se for o servidor WhatsApp
  if (serverName.includes('WhatsApp')) {
    tests.push(
      {
        name: 'Status WhatsApp',
        url: `${baseUrl}/api/whatsapp/status`,
        expected: ['connected', 'status', 'session']
      },
      {
        name: 'QR Code WhatsApp',
        url: `${baseUrl}/api/whatsapp/qr`,
        expected: ['hasQR', 'status', 'timestamp']
      }
    );
  }
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      const result = await testEndpoint(
        test.url, 
        test.method || 'GET', 
        test.body || null
      );
      
      if (result.success) {
        // Verificar se os campos esperados estão presentes
        const hasExpectedFields = test.expected.every(field => 
          result.data.hasOwnProperty(field)
        );
        
        if (hasExpectedFields) {
          console.log(`   ✅ ${test.name}`);
          passedTests++;
        } else {
          console.log(`   ⚠️ ${test.name} - Campos ausentes`);
        }
      } else {
        console.log(`   ❌ ${test.name} - ${result.error || `Status ${result.status}`}`);
      }
    } catch (error) {
      console.log(`   ❌ ${test.name} - Erro: ${error.message}`);
    }
    
    // Pequena pausa entre testes
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  const successRate = (passedTests / totalTests * 100).toFixed(1);
  console.log(`\n📊 Resultado: ${passedTests}/${totalTests} testes passaram (${successRate}%)`);
  
  return {
    serverName,
    passed: passedTests,
    total: totalTests,
    successRate: parseFloat(successRate)
  };
}

async function checkServerRunning(url) {
  try {
    const response = await fetch(url, { 
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

async function runCompleteTest() {
  const baseUrl = 'http://localhost:3001';
  
  console.log('🔍 Verificando qual servidor está rodando...\n');
  
  const isRunning = await checkServerRunning(baseUrl);
  
  if (!isRunning) {
    console.log('❌ Nenhum servidor está rodando na porta 3001');
    console.log('\n💡 Para executar os testes:');
    console.log('1. Inicie um dos servidores:');
    console.log('   - node server-basic.js');
    console.log('   - node server-enhanced.js');
    console.log('   - node server-whatsapp.js');
    console.log('2. Execute novamente: node test-sistema-completo.js');
    return;
  }
  
  // Detectar qual servidor está rodando
  try {
    const response = await fetch(`${baseUrl}/`);
    const data = await response.json();
    
    let serverType = 'Desconhecido';
    if (data.mode === 'basic') {
      serverType = 'Servidor Básico';
    } else if (data.mode === 'enhanced') {
      serverType = 'Servidor Aprimorado';
    } else if (data.mode === 'whatsapp_integrated') {
      serverType = 'Servidor WhatsApp';
    }
    
    console.log(`✅ Servidor detectado: ${serverType}`);
    console.log(`📋 Versão: ${data.version}`);
    console.log(`🌐 Ambiente: ${data.environment}`);
    
    // Executar testes
    const result = await testServer(serverType, baseUrl);
    
    // Resumo final
    console.log('\n🎯 RESUMO FINAL');
    console.log('===============');
    console.log(`Servidor: ${result.serverName}`);
    console.log(`Testes: ${result.passed}/${result.total}`);
    console.log(`Taxa de Sucesso: ${result.successRate}%`);
    
    if (result.successRate >= 80) {
      console.log('\n🎉 SISTEMA FUNCIONANDO CORRETAMENTE!');
      console.log('✅ Pronto para uso em produção');
    } else if (result.successRate >= 60) {
      console.log('\n⚠️ Sistema funcionando com algumas limitações');
      console.log('🔧 Verifique as configurações pendentes');
    } else {
      console.log('\n❌ Sistema com problemas');
      console.log('🔧 Verifique os logs e configurações');
    }
    
    // Informações adicionais baseadas no tipo de servidor
    if (serverType === 'Servidor Básico') {
      console.log('\n💡 Para funcionalidades completas:');
      console.log('- Configure Gemini AI e use server-enhanced.js');
      console.log('- Para WhatsApp real, use server-whatsapp.js');
    } else if (serverType === 'Servidor Aprimorado') {
      console.log('\n💡 Para melhorar ainda mais:');
      console.log('- Configure Gemini AI para respostas reais');
      console.log('- Configure Supabase para persistência');
      console.log('- Para WhatsApp real, use server-whatsapp.js');
    } else if (serverType === 'Servidor WhatsApp') {
      console.log('\n💡 Para usar o WhatsApp:');
      console.log('1. Acesse: http://localhost:3001/api/whatsapp/qr');
      console.log('2. Escaneie o QR Code com seu WhatsApp');
      console.log('3. Envie mensagens para testar');
    }
    
    // Verificar configurações
    console.log('\n🔧 CONFIGURAÇÕES:');
    console.log(`Gemini AI: ${process.env.GEMINI_API_KEY ? '✅ Configurado' : '❌ Não configurado'}`);
    console.log(`Supabase: ${process.env.SUPABASE_URL ? '✅ Configurado' : '❌ Não configurado'}`);
    
    if (!process.env.GEMINI_API_KEY || !process.env.SUPABASE_URL) {
      console.log('\n📖 Para configurar:');
      console.log('- Veja: CONFIGURAR_RAG.md');
      console.log('- Execute: node test-rag-config.js');
    }
    
  } catch (error) {
    console.error('❌ Erro ao detectar servidor:', error.message);
  }
}

// Executar teste
runCompleteTest().catch(console.error);
