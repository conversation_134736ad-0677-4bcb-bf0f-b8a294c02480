import express from 'express';
import { Logger } from '../utils/Logger.js';

const router = express.Router();
const logger = new Logger();

// Middleware para verificar se o Session Manager está disponível
const checkSessionManager = (req, res, next) => {
  if (!req.app.locals.sessionManager) {
    return res.status(503).json({
      error: 'Session Manager não disponível',
      message: 'O gerenciador de sessões não foi inicializado',
      code: 'SERVICE_UNAVAILABLE'
    });
  }
  next();
};

// GET /api/session/list - Listar todas as sessões
router.get('/list', checkSessionManager, async (req, res) => {
  try {
    const sessionManager = req.app.locals.sessionManager;
    const sessions = await sessionManager.getAllSessions();
    
    res.json({
      success: true,
      data: {
        sessions,
        total: sessions.length
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao listar sessões:', error);
    res.status(500).json({
      error: 'Erro ao listar sessões',
      message: error.message,
      code: 'LIST_ERROR'
    });
  }
});

// GET /api/session/active - Listar sessões ativas
router.get('/active', checkSessionManager, async (req, res) => {
  try {
    const sessionManager = req.app.locals.sessionManager;
    const activeSessions = await sessionManager.getActiveSessions();
    
    res.json({
      success: true,
      data: {
        sessions: activeSessions,
        total: activeSessions.length
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao listar sessões ativas:', error);
    res.status(500).json({
      error: 'Erro ao listar sessões ativas',
      message: error.message,
      code: 'ACTIVE_ERROR'
    });
  }
});

// GET /api/session/:id - Obter sessão específica
router.get('/:id', checkSessionManager, async (req, res) => {
  try {
    const { id } = req.params;
    const sessionManager = req.app.locals.sessionManager;
    const session = await sessionManager.getSession(id);
    
    if (!session) {
      return res.status(404).json({
        error: 'Sessão não encontrada',
        message: `Sessão com ID ${id} não existe`,
        code: 'SESSION_NOT_FOUND'
      });
    }
    
    res.json({
      success: true,
      data: session,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao obter sessão:', error);
    res.status(500).json({
      error: 'Erro ao obter sessão',
      message: error.message,
      code: 'GET_ERROR'
    });
  }
});

// DELETE /api/session/:id - Deletar sessão
router.delete('/:id', checkSessionManager, async (req, res) => {
  try {
    const { id } = req.params;
    const sessionManager = req.app.locals.sessionManager;
    
    // Verificar se a sessão existe
    const session = await sessionManager.getSession(id);
    if (!session) {
      return res.status(404).json({
        error: 'Sessão não encontrada',
        message: `Sessão com ID ${id} não existe`,
        code: 'SESSION_NOT_FOUND'
      });
    }
    
    await sessionManager.deleteSession(id);
    
    res.json({
      success: true,
      message: 'Sessão deletada com sucesso',
      data: { deletedSessionId: id },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao deletar sessão:', error);
    res.status(500).json({
      error: 'Erro ao deletar sessão',
      message: error.message,
      code: 'DELETE_ERROR'
    });
  }
});

// GET /api/session/stats - Estatísticas das sessões
router.get('/stats', checkSessionManager, async (req, res) => {
  try {
    const sessionManager = req.app.locals.sessionManager;
    const stats = sessionManager.getStats();
    
    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao obter estatísticas das sessões:', error);
    res.status(500).json({
      error: 'Erro ao obter estatísticas',
      message: error.message,
      code: 'STATS_ERROR'
    });
  }
});

// POST /api/session/backup - Criar backup das sessões
router.post('/backup', checkSessionManager, async (req, res) => {
  try {
    const sessionManager = req.app.locals.sessionManager;
    await sessionManager.createBackup();
    
    res.json({
      success: true,
      message: 'Backup criado com sucesso',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao criar backup:', error);
    res.status(500).json({
      error: 'Erro ao criar backup',
      message: error.message,
      code: 'BACKUP_ERROR'
    });
  }
});

// GET /api/session/backup/list - Listar backups disponíveis
router.get('/backup/list', checkSessionManager, async (req, res) => {
  try {
    const sessionManager = req.app.locals.sessionManager;
    const backups = await sessionManager.getBackupList();
    
    res.json({
      success: true,
      data: {
        backups,
        total: backups.length
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao listar backups:', error);
    res.status(500).json({
      error: 'Erro ao listar backups',
      message: error.message,
      code: 'BACKUP_LIST_ERROR'
    });
  }
});

// POST /api/session/backup/restore - Restaurar backup
router.post('/backup/restore', checkSessionManager, async (req, res) => {
  try {
    const { backupFileName } = req.body;
    
    if (!backupFileName) {
      return res.status(400).json({
        error: 'Nome do backup obrigatório',
        message: 'O campo backupFileName é obrigatório',
        code: 'VALIDATION_ERROR'
      });
    }
    
    const sessionManager = req.app.locals.sessionManager;
    await sessionManager.restoreFromBackup(backupFileName);
    
    res.json({
      success: true,
      message: 'Backup restaurado com sucesso',
      data: { restoredBackup: backupFileName },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao restaurar backup:', error);
    res.status(500).json({
      error: 'Erro ao restaurar backup',
      message: error.message,
      code: 'RESTORE_ERROR'
    });
  }
});

// POST /api/session/cleanup - Limpeza manual de sessões antigas
router.post('/cleanup', checkSessionManager, async (req, res) => {
  try {
    const sessionManager = req.app.locals.sessionManager;
    await sessionManager.cleanupOldSessions();
    
    res.json({
      success: true,
      message: 'Limpeza de sessões concluída',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro na limpeza de sessões:', error);
    res.status(500).json({
      error: 'Erro na limpeza',
      message: error.message,
      code: 'CLEANUP_ERROR'
    });
  }
});

export default router;
