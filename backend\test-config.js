#!/usr/bin/env node

/**
 * Teste de Configuração
 * Verifica se a configuração está funcionando
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '.env') });

console.log('🧪 Testando configuração...\n');

async function testConfig() {
  try {
    console.log('📋 Importando ServerConfig...');
    const { ServerConfig } = await import('./src/config/ServerConfig.js');
    
    console.log('⚙️ Criando instância de configuração...');
    const config = new ServerConfig();
    
    console.log('✅ Configuração criada com sucesso!');
    console.log('\n📊 Resumo da configuração:');
    console.log(JSON.stringify(config.getSummary(), null, 2));
    
    console.log('\n🔍 Validando configuração...');
    const isValid = config.validate();
    
    if (isValid) {
      console.log('✅ Configuração válida!');
    } else {
      console.log('❌ Configuração inválida!');
    }
    
  } catch (error) {
    console.error('❌ Erro ao testar configuração:', error);
    process.exit(1);
  }
}

testConfig();
