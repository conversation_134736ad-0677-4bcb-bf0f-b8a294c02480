import wppconnect from '@wppconnect-team/wppconnect';
import { EventEmitter } from 'events';
import { Logger } from '../utils/Logger.js';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * WhatsApp Real Service - Integração real com WhatsApp usando WPPConnect
 */
export class WhatsAppRealService extends EventEmitter {
  constructor() {
    super();
    
    // Configurações básicas
    this.sessionName = process.env.WHATSAPP_SESSION_NAME || 'vereadora-rafaela';
    this.isConnected = false;
    this.qrCode = null;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 3;
    this.client = null;
    
    // Logger
    this.logger = new Logger();
    
    // Callbacks para eventos
    this.onMessage = null;
    this.onReady = null;
    this.onDisconnected = null;
    this.onQRCode = null;
    this.onStatusChange = null;
    
    // Estado da conexão
    this.simulationMode = false;
    this.reconnectInterval = null;
    this.reconnectDelay = 30000; // 30 segundos
    
    // Configurações do WPPConnect
    this.wppOptions = {
      session: this.sessionName,
      catchQR: (base64Qr, asciiQR) => {
        this.handleQRCode(base64Qr, asciiQR);
      },
      statusFind: (statusSession, session) => {
        this.handleStatusChange(statusSession, session);
      },
      headless: true,
      devtools: false,
      useChrome: true,
      debug: false,
      logQR: true,
      browserWS: '',
      browserArgs: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ],
      puppeteerOptions: {
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      },
      folderNameToken: 'tokens',
      mkdirFolderToken: 'data/tokens',
      updatesLog: true,
      autoClose: 60000,
      createPathFileToken: true
    };
  }

  async initialize() {
    this.logger.info('🔄 Inicializando WhatsApp Real Service...');
    
    try {
      // Criar diretórios necessários
      this.createDirectories();
      
      // Inicializar cliente WPPConnect
      await this.initializeClient();
      
      this.logger.info('✅ WhatsApp Real Service inicializado');
      return true;
      
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar WhatsApp Real Service:', error.message);
      
      // Agendar tentativa de reconexão
      this.scheduleReconnect();
      return false;
    }
  }

  createDirectories() {
    const dirs = ['data/tokens', 'data/sessions', 'public'];
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        this.logger.info(`📁 Diretório criado: ${dir}`);
      }
    });
  }

  async initializeClient() {
    this.logger.info('🔄 Inicializando cliente WPPConnect...');
    
    try {
      this.client = await wppconnect.create(this.wppOptions);
      
      // Configurar event listeners
      this.setupEventListeners();
      
      this.isConnected = true;
      this.connectionAttempts++;
      
      this.logger.info('✅ Cliente WPPConnect inicializado com sucesso');
      
      // Emitir evento de ready
      if (this.onReady) {
        this.onReady();
      }
      this.emit('ready');
      
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar cliente WPPConnect:', error.message);
      throw error;
    }
  }

  setupEventListeners() {
    if (!this.client) return;
    
    // Listener para mensagens recebidas
    this.client.onMessage((message) => {
      try {
        this.logger.info(`📨 Mensagem recebida de ${message.from}: ${message.body}`);
        
        if (this.onMessage) {
          this.onMessage(message);
        }
        
        this.emit('message', message);
      } catch (error) {
        this.logger.error('❌ Erro ao processar mensagem recebida:', error.message);
      }
    });

    // Listener para mudanças de estado
    this.client.onStateChange((state) => {
      this.logger.info(`📊 Estado alterado para: ${state}`);
      
      if (this.onStatusChange) {
        this.onStatusChange(state);
      }
      
      this.emit('stateChange', state);
    });

    // Listener para desconexão
    this.client.onStreamChange((state) => {
      this.logger.info(`🔄 Stream alterado para: ${state}`);
      
      if (state === 'DISCONNECTED') {
        this.isConnected = false;
        
        if (this.onDisconnected) {
          this.onDisconnected();
        }
        
        this.emit('disconnected');
        this.scheduleReconnect();
      }
    });
  }

  handleQRCode(base64Qr, asciiQR) {
    try {
      this.logger.info('📱 QR Code gerado');
      
      // Salvar QR Code como arquivo
      const qrCodePath = path.join(__dirname, '../../public/qr-code.png');
      const base64Data = base64Qr.replace(/^data:image\/png;base64,/, '');
      
      fs.writeFileSync(qrCodePath, base64Data, 'base64');
      
      this.qrCode = {
        base64: base64Qr,
        ascii: asciiQR,
        path: qrCodePath,
        timestamp: new Date().toISOString(),
        attempt: this.connectionAttempts + 1
      };
      
      this.logger.info('📱 QR Code salvo em: ' + qrCodePath);
      
      // Emitir evento de QR Code
      if (this.onQRCode) {
        this.onQRCode(this.qrCode);
      }
      
      this.emit('qrcode', this.qrCode);
      
    } catch (error) {
      this.logger.error('❌ Erro ao processar QR Code:', error.message);
    }
  }

  handleStatusChange(statusSession, session) {
    this.logger.info(`📊 Status da sessão: ${statusSession} - ${session}`);
    
    switch (statusSession) {
      case 'isLogged':
        this.isConnected = true;
        this.qrCode = null;
        this.logger.info('✅ WhatsApp conectado com sucesso!');
        break;
        
      case 'notLogged':
        this.isConnected = false;
        this.logger.warn('⚠️ WhatsApp não está logado');
        break;
        
      case 'browserClose':
        this.isConnected = false;
        this.logger.warn('⚠️ Navegador fechado');
        this.scheduleReconnect();
        break;
        
      case 'qrReadSuccess':
        this.logger.info('✅ QR Code lido com sucesso');
        break;
        
      case 'qrReadFail':
        this.logger.error('❌ Falha ao ler QR Code');
        break;
    }
    
    if (this.onStatusChange) {
      this.onStatusChange(statusSession);
    }
    
    this.emit('statusChange', statusSession);
  }

  async sendMessage(to, message) {
    if (!this.client || !this.isConnected) {
      throw new Error('WhatsApp não está conectado');
    }
    
    try {
      this.logger.info(`📤 Enviando mensagem para ${to}: ${message}`);
      
      const result = await this.client.sendText(to, message);
      
      this.logger.info(`✅ Mensagem enviada com sucesso: ${result.id}`);
      
      return {
        success: true,
        messageId: result.id,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      this.logger.error(`❌ Erro ao enviar mensagem para ${to}:`, error.message);
      throw error;
    }
  }

  async getChats() {
    if (!this.client || !this.isConnected) {
      throw new Error('WhatsApp não está conectado');
    }
    
    try {
      const chats = await this.client.getAllChats();
      
      return chats.map(chat => ({
        id: chat.id._serialized,
        name: chat.name || chat.formattedTitle,
        lastMessage: chat.lastMessage?.body || '',
        lastMessageTime: chat.lastMessage?.timestamp ? new Date(chat.lastMessage.timestamp * 1000) : null,
        messageCount: chat.unreadCount || 0,
        isGroup: chat.isGroup
      }));
      
    } catch (error) {
      this.logger.error('❌ Erro ao obter chats:', error.message);
      throw error;
    }
  }

  async getChatMessages(chatId, limit = 50) {
    if (!this.client || !this.isConnected) {
      throw new Error('WhatsApp não está conectado');
    }
    
    try {
      const messages = await this.client.getMessages(chatId, false, false, limit);
      
      return messages.map(msg => ({
        id: msg.id,
        from: msg.from,
        body: msg.body,
        timestamp: msg.timestamp * 1000,
        isFromMe: msg.fromMe,
        notifyName: msg.notifyName || msg.sender?.pushname,
        type: msg.type
      }));
      
    } catch (error) {
      this.logger.error(`❌ Erro ao obter mensagens do chat ${chatId}:`, error.message);
      throw error;
    }
  }

  getConnectionStatus() {
    let status = 'Desconectado';
    let statusCode = 'disconnected';

    if (this.isConnected) {
      status = 'Conectado';
      statusCode = 'connected';
    } else if (this.qrCode) {
      status = 'Aguardando QR Code';
      statusCode = 'waiting_qr';
    } else if (this.connectionAttempts > 0) {
      status = 'Conectando...';
      statusCode = 'connecting';
    }

    return {
      isConnected: this.isConnected,
      sessionName: this.sessionName,
      qrCode: this.qrCode,
      hasQRCode: !!this.qrCode,
      qrCodeUrl: this.qrCode ? '/qr-code.png' : null,
      connectionAttempts: this.connectionAttempts,
      maxAttempts: this.maxConnectionAttempts,
      status,
      statusCode,
      timestamp: new Date().toISOString(),
      simulationMode: false
    };
  }

  scheduleReconnect() {
    if (this.reconnectInterval) {
      clearTimeout(this.reconnectInterval);
    }
    
    if (this.connectionAttempts >= this.maxConnectionAttempts) {
      this.logger.error(`❌ Máximo de tentativas de reconexão atingido (${this.maxConnectionAttempts})`);
      return;
    }
    
    this.logger.info(`🔄 Agendando reconexão em ${this.reconnectDelay}ms (tentativa ${this.connectionAttempts + 1})`);
    
    this.reconnectInterval = setTimeout(async () => {
      try {
        await this.initialize();
      } catch (error) {
        this.logger.error('❌ Erro na reconexão:', error.message);
      }
    }, this.reconnectDelay);
  }

  // Métodos para compatibilidade com MessageHandler
  setOnMessage(callback) {
    this.onMessage = callback;
  }

  setOnReady(callback) {
    this.onReady = callback;
  }

  setOnDisconnected(callback) {
    this.onDisconnected = callback;
  }

  setOnQRCode(callback) {
    this.onQRCode = callback;
  }

  setOnStatusChange(callback) {
    this.onStatusChange = callback;
  }

  async stop() {
    this.logger.info('🛑 Parando WhatsApp Real Service...');
    
    try {
      if (this.reconnectInterval) {
        clearTimeout(this.reconnectInterval);
        this.reconnectInterval = null;
      }
      
      if (this.client) {
        await this.client.close();
        this.client = null;
      }
      
      this.isConnected = false;
      this.qrCode = null;
      
      if (this.onDisconnected) {
        this.onDisconnected();
      }
      
      this.emit('disconnected');
      this.logger.info('✅ WhatsApp Real Service parado');
      
    } catch (error) {
      this.logger.error('❌ Erro ao parar WhatsApp Real Service:', error.message);
    }
  }

  async restart() {
    await this.stop();
    await this.initialize();
  }
}

export default WhatsAppRealService;
