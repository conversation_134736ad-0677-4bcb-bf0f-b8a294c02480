import { Logger } from '../utils/Logger.js';
import { HumanBehaviorService } from './HumanBehaviorService.js';

export class AntiBanService {
  constructor() {
    this.logger = new Logger();
    this.humanBehavior = new HumanBehaviorService();
    
    // Configurações anti-ban
    this.config = {
      // Rotação de User Agents
      userAgents: [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      ],
      
      // Configurações de sessão
      session: {
        rotateInterval: 24 * 60 * 60 * 1000, // 24 horas
        maxSessionTime: 8 * 60 * 60 * 1000,  // 8 horas
        breakTime: 2 * 60 * 60 * 1000,       // 2 horas de pausa
        randomBreaks: true
      },
      
      // Padrões de atividade humana
      activityPatterns: {
        workDays: [1, 2, 3, 4, 5], // Segunda a sexta
        workHours: { start: 8, end: 18 },
        lunchBreak: { start: 12, end: 13 },
        eveningActivity: { start: 19, end: 22 },
        weekendActivity: { start: 10, end: 16, reduced: true }
      },
      
      // Limites de segurança
      safetyLimits: {
        maxDailyMessages: 800,
        maxHourlyMessages: 100,
        maxConsecutiveMessages: 5,
        minDelayBetweenMessages: 3000,
        maxDelayBetweenMessages: 30000
      },
      
      // Configurações de proxy/IP
      networking: {
        rotateIP: false,
        useProxy: false,
        proxyList: [],
        dnsServers: ['*******', '*******']
      },
      
      // Detecção de riscos
      riskDetection: {
        enabled: true,
        thresholds: {
          low: 30,
          medium: 60,
          high: 80
        },
        actions: {
          low: 'log',
          medium: 'slow_down',
          high: 'pause'
        }
      }
    };
    
    // Estado atual
    this.state = {
      riskLevel: 0,
      lastRiskCheck: Date.now(),
      sessionStartTime: Date.now(),
      isInBreak: false,
      breakStartTime: null,
      currentUserAgent: this.getRandomUserAgent(),
      suspiciousActivity: [],
      banWarnings: [],
      lastActivityTime: Date.now()
    };
    
    // Inicializar monitoramento
    this.startRiskMonitoring();
    
    this.logger.info('🛡️ Anti-Ban Service inicializado');
  }

  // Obter User Agent aleatório
  getRandomUserAgent() {
    const agents = this.config.userAgents;
    return agents[Math.floor(Math.random() * agents.length)];
  }

  // Verificar se é seguro enviar mensagem
  async isSafeToSendMessage(message, recipient) {
    // Verificar comportamento humano
    const behaviorCheck = this.humanBehavior.canSendMessage();
    if (!behaviorCheck.allowed) {
      this.logger.warn(`🚫 Mensagem bloqueada por comportamento: ${behaviorCheck.reason}`);
      return {
        safe: false,
        reason: behaviorCheck.reason,
        waitTime: behaviorCheck.waitTime,
        riskLevel: this.state.riskLevel
      };
    }
    
    // Verificar nível de risco atual
    const currentRisk = this.calculateCurrentRisk();
    if (currentRisk > this.config.riskDetection.thresholds.high) {
      this.logger.warn(`🚨 Risco muito alto: ${currentRisk}%`);
      return {
        safe: false,
        reason: 'high_risk',
        waitTime: this.calculateSafeWaitTime(),
        riskLevel: currentRisk
      };
    }
    
    // Verificar se está em horário de atividade humana
    if (!this.isHumanActivityTime()) {
      this.logger.warn('🌙 Fora do horário de atividade humana');
      return {
        safe: false,
        reason: 'off_hours',
        waitTime: this.getTimeUntilNextActiveHour(),
        riskLevel: currentRisk
      };
    }
    
    // Verificar se está em pausa obrigatória
    if (this.state.isInBreak) {
      const breakTimeRemaining = this.getBreakTimeRemaining();
      if (breakTimeRemaining > 0) {
        this.logger.warn(`☕ Em pausa obrigatória: ${breakTimeRemaining}ms restantes`);
        return {
          safe: false,
          reason: 'mandatory_break',
          waitTime: breakTimeRemaining,
          riskLevel: currentRisk
        };
      } else {
        this.endBreak();
      }
    }
    
    // Verificar limites de sessão
    const sessionTime = Date.now() - this.state.sessionStartTime;
    if (sessionTime > this.config.session.maxSessionTime) {
      this.logger.warn('⏰ Tempo máximo de sessão atingido');
      this.startMandatoryBreak();
      return {
        safe: false,
        reason: 'session_timeout',
        waitTime: this.config.session.breakTime,
        riskLevel: currentRisk
      };
    }
    
    return {
      safe: true,
      riskLevel: currentRisk,
      recommendedDelay: this.calculateRecommendedDelay(message, currentRisk)
    };
  }

  // Calcular nível de risco atual
  calculateCurrentRisk() {
    let risk = 0;
    
    // Risco baseado na frequência de mensagens
    const behaviorStats = this.humanBehavior.getStats();
    const hourlyRate = behaviorStats.messageCount.hour;
    const dailyRate = behaviorStats.messageCount.day;
    
    // Risco por frequência horária
    if (hourlyRate > this.config.safetyLimits.maxHourlyMessages * 0.8) {
      risk += 20;
    }
    if (hourlyRate > this.config.safetyLimits.maxHourlyMessages) {
      risk += 30;
    }
    
    // Risco por frequência diária
    if (dailyRate > this.config.safetyLimits.maxDailyMessages * 0.8) {
      risk += 15;
    }
    if (dailyRate > this.config.safetyLimits.maxDailyMessages) {
      risk += 25;
    }
    
    // Risco por padrões suspeitos
    const suspiciousPatterns = this.humanBehavior.detectSuspiciousPatterns();
    risk += suspiciousPatterns.length * 10;
    
    // Risco por burst de mensagens
    if (behaviorStats.burstCount > this.config.safetyLimits.maxConsecutiveMessages) {
      risk += 20;
    }
    
    // Risco por atividade fora do horário
    if (!this.isHumanActivityTime()) {
      risk += 15;
    }
    
    // Risco por tempo de sessão
    const sessionTime = Date.now() - this.state.sessionStartTime;
    if (sessionTime > this.config.session.maxSessionTime * 0.8) {
      risk += 10;
    }
    
    // Risco por atividades suspeitas recentes
    const recentSuspicious = this.state.suspiciousActivity.filter(
      activity => Date.now() - activity.timestamp < 3600000 // Última hora
    );
    risk += recentSuspicious.length * 5;
    
    this.state.riskLevel = Math.min(100, Math.max(0, risk));
    return this.state.riskLevel;
  }

  // Verificar se está em horário de atividade humana
  isHumanActivityTime() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = domingo, 6 = sábado
    const patterns = this.config.activityPatterns;
    
    // Verificar se é dia útil
    if (patterns.workDays.includes(day)) {
      // Horário de trabalho
      if (hour >= patterns.workHours.start && hour < patterns.workHours.end) {
        // Verificar se não é horário de almoço
        if (hour >= patterns.lunchBreak.start && hour < patterns.lunchBreak.end) {
          return false; // Horário de almoço
        }
        return true;
      }
      
      // Atividade noturna
      if (hour >= patterns.eveningActivity.start && hour <= patterns.eveningActivity.end) {
        return true;
      }
    } else {
      // Fim de semana - atividade reduzida
      if (patterns.weekendActivity.reduced) {
        if (hour >= patterns.weekendActivity.start && hour <= patterns.weekendActivity.end) {
          return Math.random() < 0.3; // 30% de chance de atividade
        }
      }
    }
    
    return false;
  }

  // Calcular tempo até próximo horário ativo
  getTimeUntilNextActiveHour() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(this.config.activityPatterns.workHours.start, 0, 0, 0);
    
    return tomorrow.getTime() - now.getTime();
  }

  // Calcular delay recomendado
  calculateRecommendedDelay(message, riskLevel) {
    const baseDelay = this.humanBehavior.calculateMessageDelay();

    // Aumentar delay baseado no risco
    let riskMultiplier = 1;
    if (riskLevel > this.config.riskDetection.thresholds.medium) {
      riskMultiplier = 2;
    } else if (riskLevel > this.config.riskDetection.thresholds.low) {
      riskMultiplier = 1.5;
    }

    return Math.min(
      baseDelay * riskMultiplier,
      this.config.safetyLimits.maxDelayBetweenMessages
    );
  }

  // Método de compatibilidade para WhatsAppHttpService
  calculateDelay(recipient, message) {
    const currentRisk = this.calculateCurrentRisk();
    const delay = this.calculateRecommendedDelay(message, currentRisk);

    return {
      delay: delay,
      riskLevel: currentRisk,
      safe: currentRisk < this.config.riskDetection.thresholds.high
    };
  }

  // Calcular tempo de espera seguro
  calculateSafeWaitTime() {
    const riskLevel = this.state.riskLevel;
    
    if (riskLevel > 80) {
      return 30 * 60 * 1000; // 30 minutos
    } else if (riskLevel > 60) {
      return 15 * 60 * 1000; // 15 minutos
    } else {
      return 5 * 60 * 1000;  // 5 minutos
    }
  }

  // Iniciar pausa obrigatória
  startMandatoryBreak() {
    this.state.isInBreak = true;
    this.state.breakStartTime = Date.now();
    
    this.logger.info(`☕ Iniciando pausa obrigatória de ${this.config.session.breakTime / 60000} minutos`);
  }

  // Terminar pausa
  endBreak() {
    this.state.isInBreak = false;
    this.state.breakStartTime = null;
    this.state.sessionStartTime = Date.now(); // Nova sessão
    
    this.logger.info('✅ Pausa terminada, nova sessão iniciada');
  }

  // Obter tempo restante de pausa
  getBreakTimeRemaining() {
    if (!this.state.isInBreak || !this.state.breakStartTime) {
      return 0;
    }
    
    const elapsed = Date.now() - this.state.breakStartTime;
    return Math.max(0, this.config.session.breakTime - elapsed);
  }

  // Processar mensagem com segurança anti-ban
  async processMessageSafely(client, recipient, message, options = {}) {
    try {
      // Verificar se é seguro enviar
      const safetyCheck = await this.isSafeToSendMessage(message, recipient);
      
      if (!safetyCheck.safe) {
        this.logger.warn(`🚫 Mensagem não enviada: ${safetyCheck.reason}`);
        return {
          success: false,
          reason: safetyCheck.reason,
          waitTime: safetyCheck.waitTime,
          riskLevel: safetyCheck.riskLevel
        };
      }
      
      // Simular comportamento humano antes de enviar
      await this.simulateHumanBehavior(client, recipient, message);
      
      // Aplicar delay recomendado
      if (safetyCheck.recommendedDelay) {
        await this.humanBehavior.sleep(safetyCheck.recommendedDelay);
      }
      
      // Enviar mensagem
      const result = await client.sendText(recipient, message);
      
      // Registrar envio
      this.humanBehavior.registerMessageSent(message);
      this.registerSuccessfulSend(recipient, message);
      
      // Simular comportamento pós-envio
      await this.simulatePostSendBehavior(client, recipient);
      
      this.logger.info(`✅ Mensagem enviada com segurança (risco: ${safetyCheck.riskLevel}%)`);
      
      return {
        success: true,
        messageId: result.id,
        riskLevel: safetyCheck.riskLevel,
        delay: safetyCheck.recommendedDelay
      };
      
    } catch (error) {
      this.logger.error('❌ Erro ao processar mensagem:', error);
      this.registerFailedSend(recipient, message, error);
      
      return {
        success: false,
        error: error.message,
        riskLevel: this.state.riskLevel
      };
    }
  }

  // Simular comportamento humano completo
  async simulateHumanBehavior(client, recipient, message) {
    try {
      // 1. Simular leitura de mensagens anteriores
      await this.humanBehavior.simulateReadBehavior(client, recipient);
      
      // 2. Simular presença online
      await this.humanBehavior.simulateOnlinePresence(client);
      
      // 3. Calcular e aplicar delay de digitação
      const typingDelay = this.humanBehavior.calculateTypingDelay(message);
      
      // 4. Mostrar indicador de digitação
      await this.humanBehavior.simulateTypingIndicator(client, recipient, typingDelay);
      
      // 5. Aguardar tempo de digitação
      await this.humanBehavior.sleep(typingDelay);
      
    } catch (error) {
      this.logger.warn('⚠️ Erro ao simular comportamento humano:', error.message);
    }
  }

  // Simular comportamento pós-envio
  async simulatePostSendBehavior(client, recipient) {
    try {
      // Chance de ficar online por mais um tempo
      if (Math.random() < 0.7) {
        const onlineTime = 5000 + Math.random() * 15000; // 5-20 segundos
        setTimeout(async () => {
          try {
            await client.setPresence('unavailable');
          } catch (error) {
            // Ignorar erros
          }
        }, onlineTime);
      }
      
      // Chance de pausar um pouco antes da próxima ação
      if (Math.random() < 0.5) {
        const pauseTime = 2000 + Math.random() * 8000; // 2-10 segundos
        await this.humanBehavior.sleep(pauseTime);
      }
      
    } catch (error) {
      this.logger.warn('⚠️ Erro no comportamento pós-envio:', error.message);
    }
  }

  // Registrar envio bem-sucedido
  registerSuccessfulSend(recipient, message) {
    this.state.lastActivityTime = Date.now();
    
    // Reduzir ligeiramente o risco por atividade normal
    this.state.riskLevel = Math.max(0, this.state.riskLevel - 1);
  }

  // Registrar falha no envio
  registerFailedSend(recipient, message, error) {
    const suspiciousActivity = {
      timestamp: Date.now(),
      type: 'send_failure',
      recipient,
      error: error.message,
      severity: this.classifyError(error)
    };
    
    this.state.suspiciousActivity.push(suspiciousActivity);
    
    // Aumentar risco baseado na severidade
    if (suspiciousActivity.severity === 'high') {
      this.state.riskLevel += 20;
    } else if (suspiciousActivity.severity === 'medium') {
      this.state.riskLevel += 10;
    }
    
    this.logger.warn('🚨 Falha registrada:', suspiciousActivity);
  }

  // Classificar erro por severidade
  classifyError(error) {
    const errorMessage = error.message.toLowerCase();
    
    if (errorMessage.includes('banned') || 
        errorMessage.includes('blocked') || 
        errorMessage.includes('restricted')) {
      return 'high';
    }
    
    if (errorMessage.includes('rate limit') || 
        errorMessage.includes('too many') || 
        errorMessage.includes('spam')) {
      return 'medium';
    }
    
    return 'low';
  }

  // Iniciar monitoramento de risco
  startRiskMonitoring() {
    setInterval(() => {
      this.performRiskAssessment();
    }, 60000); // A cada minuto
    
    this.logger.info('📊 Monitoramento de risco iniciado');
  }

  // Realizar avaliação de risco
  performRiskAssessment() {
    const currentRisk = this.calculateCurrentRisk();
    const threshold = this.config.riskDetection.thresholds;
    
    if (currentRisk > threshold.high) {
      this.handleHighRisk();
    } else if (currentRisk > threshold.medium) {
      this.handleMediumRisk();
    } else if (currentRisk > threshold.low) {
      this.handleLowRisk();
    }
    
    // Limpeza de atividades antigas
    this.cleanupOldActivities();
  }

  // Lidar com risco alto
  handleHighRisk() {
    this.logger.warn(`🚨 RISCO ALTO detectado: ${this.state.riskLevel}%`);
    this.startMandatoryBreak();
  }

  // Lidar com risco médio
  handleMediumRisk() {
    this.logger.warn(`⚠️ Risco médio detectado: ${this.state.riskLevel}%`);
    // Aumentar delays automaticamente
    this.humanBehavior.updateConfig({
      messaging: {
        min: 5000,
        max: 30000,
        variation: 0.5
      }
    });
  }

  // Lidar com risco baixo
  handleLowRisk() {
    this.logger.info(`ℹ️ Risco baixo: ${this.state.riskLevel}%`);
    // Manter configurações normais
  }

  // Limpeza de atividades antigas
  cleanupOldActivities() {
    const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24 horas
    
    this.state.suspiciousActivity = this.state.suspiciousActivity.filter(
      activity => activity.timestamp > cutoff
    );
    
    this.state.banWarnings = this.state.banWarnings.filter(
      warning => warning.timestamp > cutoff
    );
  }

  // Obter estatísticas anti-ban
  getAntiBanStats() {
    return {
      riskLevel: this.state.riskLevel,
      isInBreak: this.state.isInBreak,
      breakTimeRemaining: this.getBreakTimeRemaining(),
      sessionTime: Date.now() - this.state.sessionStartTime,
      maxSessionTime: this.config.session.maxSessionTime,
      suspiciousActivities: this.state.suspiciousActivity.length,
      banWarnings: this.state.banWarnings.length,
      humanBehaviorScore: this.humanBehavior.calculateBehaviorScore(),
      isHumanActivityTime: this.isHumanActivityTime(),
      safetyLimits: this.config.safetyLimits,
      currentUserAgent: this.state.currentUserAgent
    };
  }

  // Atualizar configurações
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('⚙️ Configurações anti-ban atualizadas');
  }

  // Reset completo
  reset() {
    this.state = {
      riskLevel: 0,
      lastRiskCheck: Date.now(),
      sessionStartTime: Date.now(),
      isInBreak: false,
      breakStartTime: null,
      currentUserAgent: this.getRandomUserAgent(),
      suspiciousActivity: [],
      banWarnings: [],
      lastActivityTime: Date.now()
    };
    
    this.humanBehavior.resetStats();
    this.logger.info('🔄 Anti-Ban Service resetado');
  }
}
