#!/usr/bin/env node

/**
 * Script de teste para a migração WPPConnect Server
 * Vereadora Rafaela de Nilda - Sistema RAG
 */

import axios from 'axios';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

class WPPConnectMigrationTester {
  constructor() {
    this.wppServerUrl = process.env.WPPCONNECT_SERVER_URL || 'http://localhost:21465';
    this.backendUrl = process.env.VITE_WHATSAPP_BACKEND_URL || 'http://localhost:3001';
    this.sessionName = process.env.WHATSAPP_SESSION_NAME || 'vereadora-rafaela';
    this.secretKey = process.env.WPPCONNECT_SECRET_KEY || 'vereadora-rafaela-secret-2024';
    
    this.results = {
      docker: false,
      wppServer: false,
      backend: false,
      session: false,
      webhook: false,
      apis: false
    };
  }

  async runAllTests() {
    console.log('🧪 Iniciando testes da migração WPPConnect Server...\n');
    
    try {
      await this.testDocker();
      await this.testWPPConnectServer();
      await this.testBackend();
      await this.testSession();
      await this.testWebhook();
      await this.testAPIs();
      
      this.showResults();
      
    } catch (error) {
      console.error('❌ Erro durante os testes:', error.message);
      process.exit(1);
    }
  }

  async testDocker() {
    console.log('🐳 Testando Docker...');
    
    try {
      // Verificar se Docker está instalado
      await execAsync('docker --version');
      console.log('  ✅ Docker instalado');
      
      // Verificar se Docker Compose está instalado
      await execAsync('docker-compose --version');
      console.log('  ✅ Docker Compose instalado');
      
      // Verificar se o container está rodando
      const { stdout } = await execAsync('docker ps --filter name=wppconnect-server-rafaela --format "{{.Names}}"');
      
      if (stdout.includes('wppconnect-server-rafaela')) {
        console.log('  ✅ Container WPPConnect Server está rodando');
        this.results.docker = true;
      } else {
        console.log('  ⚠️ Container WPPConnect Server não está rodando');
        console.log('  💡 Execute: docker-compose -f docker-compose.wppconnect.yml up -d');
      }
      
    } catch (error) {
      console.log('  ❌ Erro no Docker:', error.message);
    }
    
    console.log('');
  }

  async testWPPConnectServer() {
    console.log('🔌 Testando WPPConnect Server...');
    
    try {
      // Testar conexão básica
      const response = await axios.get(`${this.wppServerUrl}/api/server/status`, {
        timeout: 5000
      });
      
      console.log('  ✅ WPPConnect Server está respondendo');
      console.log(`  📊 Status: ${response.status}`);
      
      this.results.wppServer = true;
      
    } catch (error) {
      console.log('  ❌ WPPConnect Server não está acessível');
      console.log(`  🔗 URL testada: ${this.wppServerUrl}`);
      console.log('  💡 Verifique se o container está rodando');
    }
    
    console.log('');
  }

  async testBackend() {
    console.log('🏛️ Testando Backend da Vereadora Rafaela...');
    
    try {
      // Testar endpoint de saúde
      const response = await axios.get(`${this.backendUrl}/api/health`, {
        timeout: 5000
      });
      
      console.log('  ✅ Backend está respondendo');
      console.log(`  📊 Status: ${response.status}`);
      
      // Testar endpoint específico do WhatsApp
      try {
        const whatsappResponse = await axios.get(`${this.backendUrl}/api/whatsapp/status`);
        console.log('  ✅ Endpoint WhatsApp acessível');
        this.results.backend = true;
      } catch (whatsappError) {
        console.log('  ⚠️ Endpoint WhatsApp com problemas');
        console.log(`  📝 Erro: ${whatsappError.response?.data?.message || whatsappError.message}`);
      }
      
    } catch (error) {
      console.log('  ❌ Backend não está acessível');
      console.log(`  🔗 URL testada: ${this.backendUrl}`);
      console.log('  💡 Verifique se o backend está rodando');
    }
    
    console.log('');
  }

  async testSession() {
    console.log('📱 Testando Sessão WhatsApp...');
    
    try {
      // Testar status da sessão
      const response = await axios.get(`${this.wppServerUrl}/api/${this.sessionName}/status`, {
        headers: {
          'Authorization': `Bearer ${this.secretKey}`
        },
        timeout: 5000
      });
      
      console.log('  ✅ Endpoint de sessão acessível');
      console.log(`  📊 Status da sessão: ${JSON.stringify(response.data, null, 2)}`);
      
      this.results.session = true;
      
    } catch (error) {
      console.log('  ⚠️ Sessão não inicializada ou com problemas');
      console.log(`  📝 Erro: ${error.response?.data?.message || error.message}`);
      console.log('  💡 A sessão será criada quando o backend for iniciado');
    }
    
    console.log('');
  }

  async testWebhook() {
    console.log('🔗 Testando Webhook...');
    
    try {
      // Testar endpoint de webhook
      const response = await axios.get(`${this.backendUrl}/api/webhook/test`, {
        timeout: 5000
      });
      
      console.log('  ✅ Endpoint de webhook acessível');
      console.log(`  📊 Configuração: ${JSON.stringify(response.data.config, null, 2)}`);
      
      this.results.webhook = true;
      
    } catch (error) {
      console.log('  ❌ Webhook com problemas');
      console.log(`  📝 Erro: ${error.response?.data?.message || error.message}`);
    }
    
    console.log('');
  }

  async testAPIs() {
    console.log('🔌 Testando APIs principais...');
    
    const endpoints = [
      { name: 'Health Check', url: `${this.backendUrl}/api/health` },
      { name: 'WhatsApp Status', url: `${this.backendUrl}/api/whatsapp/status` },
      { name: 'Session Info', url: `${this.backendUrl}/api/session/info` },
      { name: 'Webhook Test', url: `${this.backendUrl}/api/webhook/test` }
    ];
    
    let successCount = 0;
    
    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(endpoint.url, { timeout: 3000 });
        console.log(`  ✅ ${endpoint.name}: OK (${response.status})`);
        successCount++;
      } catch (error) {
        console.log(`  ❌ ${endpoint.name}: ERRO (${error.response?.status || 'timeout'})`);
      }
    }
    
    if (successCount === endpoints.length) {
      this.results.apis = true;
      console.log('  🎉 Todas as APIs estão funcionando!');
    } else {
      console.log(`  ⚠️ ${successCount}/${endpoints.length} APIs funcionando`);
    }
    
    console.log('');
  }

  showResults() {
    console.log('📊 RESULTADOS DOS TESTES\n');
    console.log('='.repeat(50));
    
    const tests = [
      { name: 'Docker & Containers', status: this.results.docker },
      { name: 'WPPConnect Server', status: this.results.wppServer },
      { name: 'Backend Vereadora', status: this.results.backend },
      { name: 'Sessão WhatsApp', status: this.results.session },
      { name: 'Webhook', status: this.results.webhook },
      { name: 'APIs Principais', status: this.results.apis }
    ];
    
    tests.forEach(test => {
      const icon = test.status ? '✅' : '❌';
      const status = test.status ? 'PASSOU' : 'FALHOU';
      console.log(`${icon} ${test.name.padEnd(20)} ${status}`);
    });
    
    console.log('='.repeat(50));
    
    const passedTests = tests.filter(t => t.status).length;
    const totalTests = tests.length;
    
    if (passedTests === totalTests) {
      console.log('🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!');
      console.log('✅ Todos os testes passaram');
      console.log('\n💡 Próximos passos:');
      console.log('   1. Iniciar o backend: npm run dev (na pasta backend)');
      console.log('   2. Iniciar o frontend: npm run dev (na pasta raiz)');
      console.log('   3. Acessar http://localhost:3000');
    } else {
      console.log(`⚠️ MIGRAÇÃO PARCIAL: ${passedTests}/${totalTests} testes passaram`);
      console.log('\n🔧 Ações necessárias:');
      
      if (!this.results.docker) {
        console.log('   - Iniciar WPPConnect Server: docker-compose -f docker-compose.wppconnect.yml up -d');
      }
      if (!this.results.backend) {
        console.log('   - Iniciar backend: cd backend && npm run dev');
      }
      if (!this.results.wppServer) {
        console.log('   - Verificar logs do WPPConnect Server: docker-compose -f docker-compose.wppconnect.yml logs');
      }
    }
    
    console.log('\n📚 Documentação: WPPCONNECT-SERVER-MIGRATION.md');
  }
}

// Executar testes
const tester = new WPPConnectMigrationTester();
tester.runAllTests().catch(console.error);
