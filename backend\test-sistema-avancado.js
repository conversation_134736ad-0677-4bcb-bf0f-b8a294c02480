#!/usr/bin/env node

/**
 * Teste Completo do Sistema Avançado
 * Valida todas as funcionalidades implementadas
 */

import dotenv from 'dotenv';

dotenv.config();

console.log('🧪 TESTE COMPLETO DO SISTEMA AVANÇADO');
console.log('=====================================\n');

async function testEndpoint(url, method = 'GET', body = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(url, options);
    const data = await response.json();
    
    return {
      success: response.ok,
      status: response.status,
      data
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

async function testCompleteSystem() {
  const baseUrl = 'http://localhost:3001';
  
  console.log('🔍 Verificando servidor completo...\n');
  
  const isRunning = await testEndpoint(baseUrl);
  
  if (!isRunning.success) {
    console.log('❌ Servidor não está rodando na porta 3001');
    console.log('\n💡 Para executar os testes:');
    console.log('1. Inicie o servidor completo: node server-complete.js');
    console.log('2. Execute novamente: node test-sistema-avancado.js');
    return;
  }
  
  // Detectar versão do servidor
  const serverInfo = isRunning.data;
  console.log(`✅ Servidor detectado: ${serverInfo.message}`);
  console.log(`📋 Versão: ${serverInfo.version}`);
  console.log(`🌐 Modo: ${serverInfo.mode}`);
  console.log('');
  
  // Testes básicos
  console.log('🔍 TESTES BÁSICOS');
  console.log('==================');
  
  const basicTests = [
    {
      name: 'Health Check Avançado',
      url: `${baseUrl}/api/health`,
      expected: ['server', 'timestamp', 'uptime', 'memory']
    },
    {
      name: 'Configuração Completa',
      url: `${baseUrl}/api/config`,
      expected: ['server', 'whatsapp', 'services', 'features']
    }
  ];
  
  let basicPassed = 0;
  for (const test of basicTests) {
    const result = await testEndpoint(test.url);
    if (result.success && test.expected.every(field => result.data.hasOwnProperty(field))) {
      console.log(`   ✅ ${test.name}`);
      basicPassed++;
    } else {
      console.log(`   ❌ ${test.name}`);
    }
  }
  
  console.log(`\n📊 Testes Básicos: ${basicPassed}/${basicTests.length}\n`);
  
  // Testes de funcionalidades avançadas
  console.log('🚀 TESTES DE FUNCIONALIDADES AVANÇADAS');
  console.log('=======================================');
  
  // Teste WhatsApp
  console.log('📱 WhatsApp:');
  const whatsappStatus = await testEndpoint(`${baseUrl}/api/whatsapp/status`);
  if (whatsappStatus.success) {
    console.log(`   ✅ Status: ${whatsappStatus.data.status}`);
    console.log(`   📱 Conectado: ${whatsappStatus.data.connected ? 'Sim' : 'Não'}`);
    console.log(`   🎤 Suporte a Áudio: ${whatsappStatus.data.features?.audioMessages ? 'Sim' : 'Não'}`);
    
    // Teste QR Code
    const qrTest = await testEndpoint(`${baseUrl}/api/whatsapp/qr`);
    if (qrTest.success) {
      console.log(`   📱 QR Code: ${qrTest.data.hasQR ? 'Disponível' : 'Não disponível'}`);
    }
  } else {
    console.log('   ❌ WhatsApp não disponível');
  }
  
  console.log('');
  
  // Teste RAG Avançado
  console.log('🧠 RAG com IA Avançada:');
  const ragTest = await testEndpoint(
    `${baseUrl}/api/rag/query`,
    'POST',
    { 
      query: 'Olá Vereadora Rafaela, como você pode ajudar com questões de saúde em Parnamirim?',
      context: { messageType: 'question' }
    }
  );
  
  if (ragTest.success) {
    console.log(`   ✅ Resposta gerada`);
    console.log(`   🧠 Modo: ${ragTest.data.metadata?.mode}`);
    console.log(`   ⏱️ Tempo: ${ragTest.data.metadata?.processingTime}ms`);
    console.log(`   🎯 IA Avançada: ${ragTest.data.metadata?.features?.advancedAI ? 'Ativa' : 'Inativa'}`);
    console.log(`   📝 Resposta: "${ragTest.data.response.substring(0, 100)}..."`);
  } else {
    console.log('   ❌ RAG não funcionando');
  }
  
  console.log('');
  
  // Teste Suporte a Áudio
  console.log('🎤 Suporte a Áudio:');
  const audioTest = await testEndpoint(`${baseUrl}/api/audio/test`);
  if (audioTest.success) {
    console.log(`   ✅ Suporte: ${audioTest.data.audioSupport ? 'Ativo' : 'Inativo'}`);
    console.log(`   📁 Formatos: ${audioTest.data.supportedFormats?.join(', ')}`);
    console.log(`   📏 Tamanho máx: ${audioTest.data.maxFileSize}`);
    console.log(`   ⏱️ Duração máx: ${audioTest.data.maxDuration}`);
    console.log(`   🔄 Cache: ${audioTest.data.features?.caching ? 'Ativo' : 'Inativo'}`);
  } else {
    console.log('   ❌ Suporte a áudio não disponível');
  }
  
  console.log('');
  
  // Teste Analytics
  console.log('📊 Analytics:');
  const analyticsTest = await testEndpoint(`${baseUrl}/api/analytics?period=7d`);
  if (analyticsTest.success) {
    if (analyticsTest.data.totalMessages !== undefined) {
      console.log(`   ✅ Analytics funcionando`);
      console.log(`   📈 Mensagens: ${analyticsTest.data.totalMessages}`);
      console.log(`   👥 Usuários únicos: ${analyticsTest.data.uniqueUsers}`);
      console.log(`   ⏱️ Tempo médio: ${Math.round(analyticsTest.data.averageResponseTime)}ms`);
    } else {
      console.log(`   ⚠️ Analytics disponível mas sem dados`);
    }
  } else {
    console.log('   ❌ Analytics não disponível');
  }
  
  console.log('');
  
  // Teste Documentos
  console.log('📄 Documentos:');
  const docsTest = await testEndpoint(`${baseUrl}/api/documents`);
  if (docsTest.success) {
    console.log(`   ✅ Sistema de documentos funcionando`);
    console.log(`   📚 Documentos: ${docsTest.data.count}`);
    console.log(`   🗄️ Fonte: ${docsTest.data.source}`);
  } else {
    console.log('   ❌ Sistema de documentos não disponível');
  }
  
  console.log('');
  
  // Verificar configurações
  console.log('🔧 CONFIGURAÇÕES');
  console.log('=================');
  
  const configTest = await testEndpoint(`${baseUrl}/api/config`);
  if (configTest.success) {
    const services = configTest.data.services;
    const features = configTest.data.features;
    
    console.log('🧠 Gemini AI:');
    console.log(`   Status: ${services.gemini?.initialized ? '✅ Ativo' : '❌ Inativo'}`);
    if (services.gemini?.initialized) {
      console.log(`   Modelo: ${services.gemini.model || 'N/A'}`);
      console.log(`   Cache: ${services.gemini.cacheSize || 0} entradas`);
    }
    
    console.log('');
    console.log('🗄️ Supabase:');
    console.log(`   Status: ${services.supabase?.initialized ? '✅ Ativo' : '❌ Inativo'}`);
    if (services.supabase?.initialized) {
      console.log(`   Cache: ${services.supabase.cacheSize || 0} entradas`);
      console.log(`   URL: ${services.supabase.url === 'configured' ? 'Configurada' : 'Não configurada'}`);
    }
    
    console.log('');
    console.log('🎤 Audio Service:');
    console.log(`   Status: ${services.audio?.initialized ? '✅ Ativo' : '❌ Inativo'}`);
    if (services.audio?.initialized) {
      console.log(`   Processados: ${services.audio.totalProcessed || 0}`);
      console.log(`   Sucessos: ${services.audio.successfulTranscriptions || 0}`);
      console.log(`   FFmpeg: ${services.audio.hasFFmpeg ? 'Disponível' : 'Não disponível'}`);
    }
  }
  
  console.log('');
  
  // Resumo final
  console.log('🎯 RESUMO FINAL');
  console.log('===============');
  
  const features = configTest.data?.features || {};
  const totalFeatures = Object.keys(features).length;
  const activeFeatures = Object.values(features).filter(Boolean).length;
  
  console.log(`Funcionalidades: ${activeFeatures}/${totalFeatures} ativas`);
  console.log(`Taxa de Sucesso: ${((activeFeatures / totalFeatures) * 100).toFixed(1)}%`);
  console.log('');
  
  console.log('📋 Status das Funcionalidades:');
  console.log(`   🧠 IA Avançada: ${features.advancedAI ? '✅' : '❌'}`);
  console.log(`   🗄️ Persistência: ${features.dataPeristence ? '✅' : '❌'}`);
  console.log(`   🎤 Suporte a Áudio: ${features.audioSupport ? '✅' : '❌'}`);
  console.log(`   📱 Chat em Tempo Real: ${features.realTimeChat ? '✅' : '❌'}`);
  console.log('');
  
  // Recomendações
  if (activeFeatures === totalFeatures) {
    console.log('🎉 PARABÉNS!');
    console.log('✅ Sistema completamente funcional!');
    console.log('🚀 Todas as funcionalidades avançadas estão ativas!');
    console.log('💖 Vereadora Rafaela pronta para servir Parnamirim!');
  } else if (activeFeatures >= totalFeatures * 0.75) {
    console.log('🎊 MUITO BOM!');
    console.log('✅ Sistema funcionando com a maioria das funcionalidades!');
    console.log('🔧 Configure as funcionalidades restantes para experiência completa');
  } else {
    console.log('⚠️ Sistema funcionando com funcionalidades limitadas');
    console.log('🔧 Configure Gemini AI e Supabase para funcionalidades completas');
  }
  
  console.log('');
  console.log('📖 Para configurar funcionalidades pendentes:');
  console.log('- Gemini AI: Veja CONFIGURAR_RAG.md');
  console.log('- Supabase: Execute database/supabase_setup.sql');
  console.log('- Teste configurações: node test-rag-config.js');
  console.log('');
}

// Executar teste
testCompleteSystem().catch(console.error);
