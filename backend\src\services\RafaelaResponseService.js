import { Logger } from '../utils/Logger.js';

// Importar configuração centralizada da persona
// Nota: Como este é um arquivo .js, vamos recriar as configurações aqui
// mas mantendo consistência com o arquivo TypeScript principal

class RafaelaResponseService {
  constructor() {
    this.logger = new Logger();

    // Configuração centralizada da Vereadora Rafaela
    this.vereadoraConfig = this.loadVereadoraConfig();

    // Emojis favoritos da Vereadora Rafaela (centralizados)
    this.emojis = this.vereadoraConfig.emojis.favoritos;

    // Padrões de resposta da Vereadora Rafaela (centralizados)
    this.responsePatterns = this.initializePatterns();
  }

  loadVereadoraConfig() {
    // Configuração centralizada da Vereadora Rafaela
    // Mantém consistência com config/vereadoraPersona.ts
    return {
      info: {
        nome: '<PERSON><PERSON>',
        nomeCompleto: 'Vereadora Rafaela de <PERSON>lda',
        municipio: '<PERSON><PERSON><PERSON><PERSON>',
        estado: 'RN',
        contatos: {
          telefone: '(84) 99999-9999',
          email: '<EMAIL>'
        }
      },
      emojis: {
        favoritos: {
          prayer: '🙏🏽',
          heart: '💖',
          excited: '🤩',
          love: '😍',
          clap: '👏'
        },
        combinacoes: {
          saudacaoCarinhosa: '🙏🏽💖',
          motivacao: '🤩💖👏'
        }
      },
      padroes: {
        cestaBasica: `Já tem o cadastro no CRAS? 🙏🏽`,
        medicacao: `Já tentou ver na farmácia da UBS que você é atendida? 🙏🏽`,
        emprego: `Vou ver o que posso fazer! Manda seu currículo aqui 🙏🏽`,
        consulta: `Já tentou marcar na UBS? 🙏🏽`,
        precisoFalar: `Oii, pode ser por aqui mesmo ou só pessoalmente? Estou à disposição! 🙏🏽💖`,
        gratidao: `De nada! Estou sempre à disposição! 🙏🏽💖`,
        elogios: `Muito obrigada! 🤩💖 Isso me motiva ainda mais a trabalhar por nossa cidade! 👏`
      }
    };
  }

  initializePatterns() {
    const { emojis, padroes } = this.vereadoraConfig;

    return {
      // Saudações sem número salvo
      unknownGreeting: {
        patterns: [
          /^(bom dia|boa tarde|boa noite|oi|olá|oie)/i,
        ],
        response: `Oi! Como posso te ajudar? ${emojis.combinacoes.saudacaoCarinhosa}`
      },

      // Pedidos de cesta básica
      cestaBasica: {
        patterns: [
          /cesta\s*básica/i,
          /cesta\s*de\s*alimento/i,
          /ajuda\s*alimentar/i,
          /alimento/i,
          /comida/i,
          /fome/i
        ],
        response: padroes.cestaBasica
      },

      // Medicação
      medicacao: {
        patterns: [
          /medicação/i,
          /medicamento/i,
          /remédio/i,
          /farmácia/i,
          /receita/i,
          /tratamento/i
        ],
        response: padroes.medicacao
      },

      // Pedido de emprego
      emprego: {
        patterns: [
          /emprego/i,
          /trabalho/i,
          /vaga/i,
          /contratação/i,
          /oportunidade/i,
          /concurso/i
        ],
        response: padroes.emprego
      },

      // Cirurgias e exames
      cirurgiaExame: {
        patterns: [
          /cirurgia/i,
          /exame/i,
          /consulta/i,
          /médico/i,
          /hospital/i,
          /especialista/i,
          /regulação/i
        ],
        response: padroes.consulta
      },

      // Canal dentário
      canalDentario: {
        patterns: [
          /canal/i,
          /dentista/i,
          /dente/i,
          /dental/i,
          /odonto/i
        ],
        response: `Já pegou o encaminhamento com o dentista da UBS que você é atendida? ${this.emojis.prayer}`
      },

      // Mensagens de bom dia e de Deus
      bomDiaDeus: {
        patterns: [
          /bom\s*dia.*deus/i,
          /deus.*bom\s*dia/i,
          /glória.*deus/i,
          /deus.*abençoe/i,
          /amém/i,
          /aleluia/i,
          /graças.*deus/i
        ],
        response: `Bom diaaa, amém! ${this.emojis.prayer}${this.emojis.heart}`
      },

      // Agenda
      agenda: {
        patterns: [
          /agenda/i,
          /reunião/i,
          /encontro/i,
          /conversar/i,
          /marcar/i,
          /horário/i,
          /atendimento/i
        ],
        response: `Qual seria a pauta? Vou verificar com a pessoa que cuida da minha agenda o melhor dia e horário e te passo em seguida! ${this.emojis.prayer}${this.emojis.heart}`
      },

      // Vereadora preciso falar contigo
      precisoFalar: {
        patterns: [
          /preciso\s*falar/i,
          /quero\s*falar/i,
          /conversar.*você/i,
          /falar.*contigo/i,
          /falar.*com\s*você/i
        ],
        response: padroes.precisoFalar
      },

      // Mensagens de gratidão
      gratidao: {
        patterns: [
          /obrigad[oa]/i,
          /valeu/i,
          /agradeço/i,
          /muito\s*grat[oa]/i
        ],
        response: padroes.gratidao
      },

      // Elogios
      elogios: {
        patterns: [
          /parabéns/i,
          /excelente/i,
          /ótim[oa]/i,
          /maravilhos[oa]/i,
          /incrível/i
        ],
        response: padroes.elogios
      }
    };
  }

  // Analisar mensagem e gerar resposta
  generateResponse(message, contact = null) {
    try {
      this.logger.info(`🤖 Analisando mensagem: "${message}"`);
      
      const cleanMessage = message.trim().toLowerCase();
      
      // Verificar se é um contato conhecido
      const isKnownContact = contact && contact.name && contact.name !== 'Sem nome';
      
      // Verificar padrões em ordem de prioridade
      for (const [patternName, config] of Object.entries(this.responsePatterns)) {
        for (const pattern of config.patterns) {
          if (pattern.test(cleanMessage)) {
            this.logger.info(`✅ Padrão encontrado: ${patternName}`);
            
            // Para saudações, verificar se é contato conhecido
            if (patternName === 'unknownGreeting' && isKnownContact) {
              return `Oiii, ${contact.name}! Como você está? ${this.emojis.prayer}${this.emojis.heart}`;
            }
            
            return config.response;
          }
        }
      }

      // Resposta padrão se nenhum padrão for encontrado
      this.logger.info('ℹ️ Nenhum padrão específico encontrado, usando resposta padrão');
      
      if (isKnownContact) {
        return `Oi, ${contact.name}! Como posso te ajudar hoje? ${this.emojis.prayer}${this.emojis.heart}`;
      } else {
        return `Olá! Como posso te ajudar? Se puder me dizer seu nome e bairro, fica mais fácil para eu te atender melhor! ${this.emojis.prayer}${this.emojis.heart}`;
      }

    } catch (error) {
      this.logger.error('❌ Erro ao gerar resposta:', error);
      return `Oi! Desculpe, tive um probleminha aqui. Pode repetir sua mensagem? ${this.emojis.prayer}`;
    }
  }

  // Verificar se deve responder automaticamente
  shouldAutoRespond(message, contact = null) {
    try {
      // Não responder para mensagens muito curtas (menos de 3 caracteres)
      if (message.trim().length < 3) {
        return false;
      }

      // Não responder para mensagens que parecem ser respostas automáticas
      const autoResponsePatterns = [
        /esta\s*mensagem\s*foi\s*enviada\s*automaticamente/i,
        /auto\s*resposta/i,
        /mensagem\s*automática/i
      ];

      for (const pattern of autoResponsePatterns) {
        if (pattern.test(message)) {
          return false;
        }
      }

      // Não responder para mídias sem texto
      if (message.trim() === '' || message === '[Mídia]') {
        return false;
      }

      return true;

    } catch (error) {
      this.logger.error('❌ Erro ao verificar auto-resposta:', error);
      return false;
    }
  }

  // Adicionar contexto personalizado à resposta
  addPersonalizedContext(response, contact = null, additionalInfo = {}) {
    try {
      let personalizedResponse = response;

      // Adicionar nome se disponível
      if (contact && contact.name && contact.name !== 'Sem nome') {
        // Se a resposta não contém o nome, adicionar no início
        if (!personalizedResponse.includes(contact.name)) {
          personalizedResponse = `${contact.name}, ${personalizedResponse.toLowerCase()}`;
        }
      }

      // Adicionar informações de horário se relevante
      const now = new Date();
      const hour = now.getHours();
      
      if (hour < 12 && !personalizedResponse.includes('bom dia')) {
        // Manhã
      } else if (hour < 18 && !personalizedResponse.includes('boa tarde')) {
        // Tarde
      } else if (!personalizedResponse.includes('boa noite')) {
        // Noite
      }

      return personalizedResponse;

    } catch (error) {
      this.logger.error('❌ Erro ao personalizar resposta:', error);
      return response;
    }
  }

  // Obter estatísticas de uso
  getStats() {
    return {
      totalPatterns: Object.keys(this.responsePatterns).length,
      availableEmojis: Object.keys(this.emojis).length,
      lastUpdate: new Date().toISOString()
    };
  }
}

export default RafaelaResponseService;
