import React, { useState } from 'react';
import { DocumentInput } from './DocumentInput';
import type { Document } from '../types';

interface DocumentManagerProps {
  documents: Document[];
  onDeleteDocument: (id: string) => void;
  onUpload: (file: File) => void;
}

export const DocumentManager: React.FC<DocumentManagerProps> = ({
  documents,
  onDeleteDocument,
  onUpload
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date | string | number) => {
    try {
      // Converter para Date se não for
      const dateObj = date instanceof Date ? date : new Date(date);

      // Verificar se é uma data válida
      if (isNaN(dateObj.getTime())) {
        return 'Data inválida';
      }

      return dateObj.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return 'Data inválida';
    }
  };

  const getStatusIcon = (status: Document['status']) => {
    switch (status) {
      case 'uploading':
        return (
          <div className="status-indicator status-info">
            <div className="loading-spinner"></div>
            <span>Enviando</span>
          </div>
        );
      case 'processing':
        return (
          <div className="status-indicator status-warning">
            <div className="loading-spinner"></div>
            <span>Processando</span>
          </div>
        );
      case 'ready':
        return (
          <div className="status-indicator status-success">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Pronto</span>
          </div>
        );
      case 'error':
        return (
          <div className="status-indicator status-danger">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Erro</span>
          </div>
        );
      default:
        return (
          <div className="status-indicator">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Desconhecido</span>
          </div>
        );
    }
  };

  const sortedDocuments = [...documents].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'date':
        try {
          const dateA = a.upload_date instanceof Date ? a.upload_date : new Date(a.upload_date);
          const dateB = b.upload_date instanceof Date ? b.upload_date : new Date(b.upload_date);
          comparison = dateA.getTime() - dateB.getTime();
        } catch (error) {
          comparison = 0;
        }
        break;
      case 'size':
        comparison = a.size - b.size;
        break;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const handleSort = (field: 'name' | 'date' | 'size') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const handleDelete = (id: string) => {
    if (showDeleteConfirm === id) {
      onDeleteDocument(id);
      setShowDeleteConfirm(null);
    } else {
      setShowDeleteConfirm(id);
    }
  };

  const getSortIcon = (field: 'name' | 'date' | 'size') => {
    if (sortBy !== field) {
      return (
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
        </svg>
      );
    }
    
    return sortOrder === 'asc' ? (
      <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
      </svg>
    ) : (
      <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
      </svg>
    );
  };

  return (
    <div className="h-full flex flex-col bg-gh-bg">
      {/* Header */}
      <div className="p-6 border-b border-gh-border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gh-text">Gerenciamento de Documentos</h2>
          <div className="status-indicator status-info">
            <span>{documents.length} documento{documents.length !== 1 ? 's' : ''}</span>
          </div>
        </div>
        <DocumentInput onUpload={onUpload} />
      </div>

      {/* Lista de documentos */}
      <div className="flex-1 overflow-hidden flex flex-col">
        {documents.length === 0 ? (
          <div className="flex-1 flex items-center justify-center animate-fade-in">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-gh-link rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-base font-medium text-gh-text mb-2">Nenhum documento carregado</h3>
              <p className="text-gh-text-secondary text-sm">Faça upload de documentos para começar a usar o sistema RAG</p>
            </div>
          </div>
        ) : (
          <>
            {/* Header da tabela */}
            <div className="px-6 py-3 bg-gh-sidebar border-b border-gh-border">
              <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gh-text">
                <button
                  onClick={() => handleSort('name')}
                  className="col-span-5 flex items-center space-x-1 hover:text-gh-link transition-colors"
                >
                  <span>Nome</span>
                  {getSortIcon('name')}
                </button>

                <div className="col-span-2 text-center">Status</div>

                <button
                  onClick={() => handleSort('size')}
                  className="col-span-2 flex items-center justify-center space-x-1 hover:text-gh-link transition-colors"
                >
                  <span>Tamanho</span>
                  {getSortIcon('size')}
                </button>

                <button
                  onClick={() => handleSort('date')}
                  className="col-span-2 flex items-center justify-center space-x-1 hover:text-gh-link transition-colors"
                >
                  <span>Data</span>
                  {getSortIcon('date')}
                </button>

                <div className="col-span-1 text-center">Ações</div>
              </div>
            </div>

            {/* Lista de documentos */}
            <div className="flex-1 overflow-y-auto">
              {sortedDocuments.map((document) => (
                <div
                  key={document.id}
                  className="px-6 py-4 border-b border-gh-border hover:bg-gh-hover transition-colors"
                >
                  <div className="grid grid-cols-12 gap-4 items-center">
                    {/* Nome */}
                    <div className="col-span-5">
                      <div className="flex items-center space-x-3">
                        <div className="w-6 h-6 bg-gh-link rounded flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <div>
                          <p className="font-medium text-gh-text truncate text-sm">{document.name}</p>
                          {document.chunks_count && document.chunks_count > 0 && (
                            <p className="text-xs text-gh-text-secondary">{document.chunks_count} chunks processados</p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Status */}
                    <div className="col-span-2 text-center">
                      {getStatusIcon(document.status)}
                    </div>

                    {/* Tamanho */}
                    <div className="col-span-2 text-center text-sm text-gh-text-secondary">
                      {formatFileSize(document.size)}
                    </div>

                    {/* Data */}
                    <div className="col-span-2 text-center text-sm text-gh-text-secondary">
                      {formatDate(document.upload_date)}
                    </div>

                    {/* Ações */}
                    <div className="col-span-1 text-center">
                      <button
                        onClick={() => handleDelete(document.id)}
                        className={`btn-secondary p-1 ${
                          showDeleteConfirm === document.id ? 'btn-danger' : ''
                        }`}
                        title={showDeleteConfirm === document.id ? 'Clique novamente para confirmar' : 'Deletar documento'}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Footer com estatísticas */}
      <div className="p-4 border-t border-gh-border bg-gh-sidebar">
        <div className="flex justify-between items-center text-sm text-gh-text-secondary">
          <span>
            {documents.length} documento{documents.length !== 1 ? 's' : ''}
          </span>
          <span>
            {formatFileSize(documents.reduce((total, doc) => total + doc.size, 0))} total
          </span>
        </div>
      </div>
    </div>
  );
};
