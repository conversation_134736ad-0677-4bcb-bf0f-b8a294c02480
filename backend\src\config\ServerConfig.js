import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '../../.env') });

/**
 * Configuração Centralizada do Servidor
 * Gerencia todas as configurações do backend de forma unificada
 */
export class ServerConfig {
  constructor() {
    this.loadConfiguration();
  }

  loadConfiguration() {
    // Configurações da aplicação
    this.app = {
      name: 'Vereadora Rafaela Backend',
      version: '2.0.0',
      environment: process.env.NODE_ENV || 'development',
      isDevelopment: (process.env.NODE_ENV || 'development') === 'development',
      isProduction: (process.env.NODE_ENV || 'development') === 'production'
    };

    // Configurações do servidor
    this.server = {
      port: parseInt(process.env.PORT) || 3001,
      host: process.env.HOST || 'localhost',
      bodyLimit: process.env.BODY_LIMIT || '10mb',
      timeout: parseInt(process.env.SERVER_TIMEOUT) || 30000
    };

    // Configurações de CORS
    this.cors = {
      allowedOrigins: this.parseArray(process.env.CORS_ORIGINS) || [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        process.env.FRONTEND_URL
      ].filter(Boolean),
      credentials: process.env.CORS_CREDENTIALS === 'true',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    };

    // Configurações de segurança
    this.security = {
      enableCSP: process.env.ENABLE_CSP !== 'false',
      enableOriginVerification: process.env.ENABLE_ORIGIN_VERIFICATION === 'true',
      apiKeys: {
        admin: process.env.ADMIN_API_KEY || 'admin-key-2024',
        whatsapp: process.env.WHATSAPP_API_KEY || 'whatsapp-key-2024'
      }
    };

    // Configurações de rate limiting (mais permissivo para desenvolvimento)
    this.rateLimit = {
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60000, // 1 minuto
      maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 1000, // 1000 requisições por minuto
      skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESS === 'true'
    };

    // Configurações do WhatsApp
    this.whatsapp = {
      sessionName: process.env.WHATSAPP_SESSION_NAME || 'vereadora-rafaela',
      autoClose: process.env.WHATSAPP_AUTO_CLOSE === 'true',
      disableWelcome: process.env.WHATSAPP_DISABLE_WELCOME === 'true',
      
      // Seleção do serviço WhatsApp
      useSimulator: process.env.WHATSAPP_USE_SIMULATOR === 'true',
      useReal: process.env.WHATSAPP_USE_REAL === 'true',
      useHttp: process.env.WHATSAPP_USE_HTTP === 'true' || 
               (!process.env.WHATSAPP_USE_SIMULATOR && !process.env.WHATSAPP_USE_REAL),
      
      // Configurações de reconexão
      maxConnectionAttempts: parseInt(process.env.WHATSAPP_MAX_CONNECTION_ATTEMPTS) || 5,
      reconnectDelay: parseInt(process.env.WHATSAPP_RECONNECT_DELAY) || 30000,
      maxReconnectAttempts: parseInt(process.env.WHATSAPP_MAX_RECONNECT_ATTEMPTS) || 10
    };

    // Configurações do WPPConnect Server
    this.wppconnect = {
      serverUrl: process.env.WPPCONNECT_SERVER_URL || 'http://localhost:21465',
      port: parseInt(process.env.WPPCONNECT_PORT) || 21465,
      secretKey: process.env.WPPCONNECT_SECRET_KEY || 'vereadora-rafaela-secret-2024',
      webhookUrl: process.env.WEBHOOK_URL || `http://localhost:${this.server.port}/api/webhook/whatsapp`,
      
      // Configurações de timeout
      requestTimeout: parseInt(process.env.WPPCONNECT_REQUEST_TIMEOUT) || 30000,
      healthCheckInterval: parseInt(process.env.WPPCONNECT_HEALTH_CHECK_INTERVAL) || 60000
    };

    // Configurações do RAG
    this.rag = {
      enabled: process.env.RAG_ENABLED !== 'false',
      cacheEnabled: process.env.RAG_CACHE_ENABLED !== 'false',
      maxDocuments: parseInt(process.env.RAG_MAX_DOCUMENTS) || 100,
      chunkSize: parseInt(process.env.RAG_CHUNK_SIZE) || 1000,
      chunkOverlap: parseInt(process.env.RAG_CHUNK_OVERLAP) || 200
    };

    // Configurações do Gemini
    this.gemini = {
      apiKey: process.env.GEMINI_API_KEY,
      model: process.env.GEMINI_MODEL || 'gemini-pro',
      maxTokens: parseInt(process.env.GEMINI_MAX_TOKENS) || 2048,
      temperature: parseFloat(process.env.GEMINI_TEMPERATURE) || 0.7
    };

    // Configurações do Supabase
    this.supabase = {
      url: process.env.SUPABASE_URL,
      anonKey: process.env.SUPABASE_ANON_KEY,
      serviceKey: process.env.SUPABASE_SERVICE_KEY
    };

    // Configurações de logging
    this.logging = {
      level: process.env.LOG_LEVEL || 'info',
      enableFileLogging: process.env.ENABLE_FILE_LOGGING !== 'false',
      logDirectory: process.env.LOG_DIRECTORY || 'logs',
      maxFileSize: process.env.LOG_MAX_FILE_SIZE || '10m',
      maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5
    };

    // Configurações de cache
    this.cache = {
      enabled: process.env.CACHE_ENABLED !== 'false',
      ttl: parseInt(process.env.CACHE_TTL) || 3600, // 1 hora
      maxSize: parseInt(process.env.CACHE_MAX_SIZE) || 1000
    };

    // Configurações de monitoramento
    this.monitoring = {
      enabled: process.env.MONITORING_ENABLED !== 'false',
      healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 30000,
      metricsEnabled: process.env.METRICS_ENABLED === 'true'
    };

    // Configurações de anti-ban
    this.antiban = {
      enabled: process.env.ANTIBAN_ENABLED !== 'false',
      minDelay: parseInt(process.env.ANTIBAN_MIN_DELAY) || 1000,
      maxDelay: parseInt(process.env.ANTIBAN_MAX_DELAY) || 5000,
      riskThreshold: parseInt(process.env.ANTIBAN_RISK_THRESHOLD) || 70
    };

    // Configurações de backup
    this.backup = {
      enabled: process.env.BACKUP_ENABLED !== 'false',
      interval: parseInt(process.env.BACKUP_INTERVAL) || 86400000, // 24 horas
      maxBackups: parseInt(process.env.BACKUP_MAX_FILES) || 7,
      directory: process.env.BACKUP_DIRECTORY || 'data/backups'
    };

    // Configurações de sessão
    this.session = {
      directory: process.env.SESSION_DIRECTORY || 'data/sessions',
      autoCleanup: process.env.SESSION_AUTO_CLEANUP !== 'false',
      cleanupInterval: parseInt(process.env.SESSION_CLEANUP_INTERVAL) || 3600000, // 1 hora
      maxAge: parseInt(process.env.SESSION_MAX_AGE) || 2592000000 // 30 dias
    };
  }

  /**
   * Parse array from environment variable
   */
  parseArray(value) {
    if (!value) return null;
    return value.split(',').map(item => item.trim()).filter(Boolean);
  }

  /**
   * Validar configurações essenciais
   */
  validate() {
    const errors = [];

    // Validar porta
    if (isNaN(this.server.port) || this.server.port < 1 || this.server.port > 65535) {
      errors.push('Porta do servidor inválida');
    }

    // Validar configurações do WhatsApp se habilitado
    if (this.whatsapp.useHttp && !this.wppconnect.serverUrl) {
      errors.push('URL do WPPConnect Server é obrigatória quando usando HTTP service');
    }

    // Validar configurações do Gemini se RAG habilitado
    if (this.rag.enabled && !this.gemini.apiKey) {
      console.warn('⚠️ Gemini API Key não configurada - RAG funcionará em modo limitado');
    }

    // Validar configurações do Supabase
    if (!this.supabase.url || !this.supabase.anonKey) {
      console.warn('⚠️ Supabase não configurado - algumas funcionalidades podem não funcionar');
    }

    if (errors.length > 0) {
      throw new Error(`Erros de configuração: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Obter configuração específica por caminho
   */
  get(path) {
    return path.split('.').reduce((obj, key) => obj?.[key], this);
  }

  /**
   * Verificar se está em modo de desenvolvimento
   */
  isDev() {
    return this.app.isDevelopment;
  }

  /**
   * Verificar se está em modo de produção
   */
  isProd() {
    return this.app.isProduction;
  }

  /**
   * Obter resumo da configuração (sem dados sensíveis)
   */
  getSummary() {
    return {
      app: this.app,
      server: this.server,
      whatsapp: {
        sessionName: this.whatsapp.sessionName,
        useSimulator: this.whatsapp.useSimulator,
        useReal: this.whatsapp.useReal,
        useHttp: this.whatsapp.useHttp
      },
      rag: this.rag,
      cache: this.cache,
      monitoring: this.monitoring
    };
  }
}
