import { Profiler } from '../utils/profiler.js';

export class MessageHandler {
  // ... outros métodos ...

  async processMessage(message) {
    Profiler.startTime('processMessage');
    
    // Otimização: Usar um cache para mensagens frequentes
    const cachedResponse = await this.checkCache(message);
    if (cachedResponse) {
      Profiler.endTime('processMessage');
      return cachedResponse;
    }
    
    // Resto da lógica de processamento
    
    Profiler.endTime('processMessage');
  }
  
  async checkCache(message) {
    // Implementar lógica de cache
  }
}

// File: /home/<USER>/project/src/services/RAGService.js
import { Profiler } from '../utils/profiler.js';

export class RAGService {
  // ... outros métodos ...

  async generateResponse(query) {
    Profiler.startTime('ragGeneration');
    
    // Lógica existente de geração de resposta
    
    Profiler.endTime('ragGeneration');
  }
}