#!/usr/bin/env node

/**
 * Servidor de Teste Simplificado
 * Para verificar se a estrutura básica funciona
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '.env') });

console.log('🧪 Iniciando servidor de teste...');

const app = express();
const port = process.env.PORT || 3001;

// Middlewares básicos
app.use(cors());
app.use(express.json());

// Rota de teste
app.get('/', (req, res) => {
  res.json({
    message: '🏛️ Backend WhatsApp - Vereadora Rafaela <PERSON> (TESTE)',
    status: 'online',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Rota de health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: '2.0.0-test'
  });
});

// Rota de teste de configuração
app.get('/api/config/test', (req, res) => {
  res.json({
    port: port,
    nodeEnv: process.env.NODE_ENV,
    hasGeminiKey: !!process.env.GEMINI_API_KEY,
    hasSupabaseUrl: !!process.env.SUPABASE_URL,
    whatsappMode: {
      useSimulator: process.env.WHATSAPP_USE_SIMULATOR === 'true',
      useReal: process.env.WHATSAPP_USE_REAL === 'true',
      useHttp: process.env.WHATSAPP_USE_HTTP === 'true'
    }
  });
});

// Tratamento de erros
app.use((error, req, res, next) => {
  console.error('❌ Erro no servidor:', error);
  res.status(500).json({
    error: 'Erro interno do servidor',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

// Iniciar servidor
try {
  const server = app.listen(port, () => {
    console.log(`✅ Servidor de teste iniciado na porta ${port}`);
    console.log(`🌐 URL: http://localhost:${port}`);
    console.log(`📊 Ambiente: ${process.env.NODE_ENV || 'development'}`);
    console.log('');
    console.log('🧪 Endpoints de teste:');
    console.log(`   GET  /                    - Informações básicas`);
    console.log(`   GET  /api/health          - Health check`);
    console.log(`   GET  /api/config/test     - Teste de configuração`);
    console.log('');
    console.log('✅ Servidor funcionando corretamente!');
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('🛑 Recebido SIGTERM, parando servidor...');
    server.close(() => {
      console.log('✅ Servidor parado');
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    console.log('🛑 Recebido SIGINT, parando servidor...');
    server.close(() => {
      console.log('✅ Servidor parado');
      process.exit(0);
    });
  });

} catch (error) {
  console.error('❌ Erro ao iniciar servidor:', error);
  process.exit(1);
}
