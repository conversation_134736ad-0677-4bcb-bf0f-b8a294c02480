import { performance, PerformanceObserver } from 'perf_hooks';

export class Profiler {
  static startTime(label) {
    performance.mark(`${label}-start`);
  }

  static endTime(label) {
    performance.mark(`${label}-end`);
    performance.measure(label, `${label}-start`, `${label}-end`);
  }

  static setupObserver() {
    const obs = new PerformanceObserver((items) => {
      const entry = items.getEntries()[0];
      console.log(`${entry.name}: ${entry.duration.toFixed(2)}ms`);
    });
    obs.observe({ entryTypes: ['measure'] });
  }
}