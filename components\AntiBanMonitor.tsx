import React, { useState, useEffect } from 'react';

interface AntiBanStats {
  riskLevel: number;
  isInBreak: boolean;
  breakTimeRemaining: number;
  sessionTime: number;
  maxSessionTime: number;
  suspiciousActivities: number;
  banWarnings: number;
  humanBehaviorScore: number;
  isHumanActivityTime: boolean;
  safetyLimits: {
    maxDailyMessages: number;
    maxHourlyMessages: number;
    maxConsecutiveMessages: number;
  };
}

interface HumanBehaviorStats {
  messageCount: {
    minute: number;
    hour: number;
    day: number;
  };
  burstCount: number;
  behaviorScore: number;
  suspiciousPatterns: number;
}

interface RiskAssessment {
  riskScore: number;
  riskLevel: 'low' | 'medium' | 'high';
  safeToSend: boolean;
  nextSafeTime: number;
  recommendations: string[];
}

export const AntiBanMonitor: React.FC = () => {
  const [antiBanStats, setAntiBanStats] = useState<AntiBanStats | null>(null);
  const [humanBehaviorStats, setHumanBehaviorStats] = useState<HumanBehaviorStats | null>(null);
  const [riskAssessment, setRiskAssessment] = useState<RiskAssessment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    loadAntiBanData();
    
    // Atualizar a cada 10 segundos
    const interval = setInterval(loadAntiBanData, 10000);
    
    return () => clearInterval(interval);
  }, []);

  const loadAntiBanData = async () => {
    try {
      setError(null);
      
      // Simular dados anti-ban (em produção, viria da API)
      const mockAntiBanStats: AntiBanStats = {
        riskLevel: Math.floor(Math.random() * 100),
        isInBreak: Math.random() < 0.1,
        breakTimeRemaining: Math.random() < 0.1 ? Math.floor(Math.random() * 1800000) : 0,
        sessionTime: Math.floor(Math.random() * 28800000),
        maxSessionTime: 28800000,
        suspiciousActivities: Math.floor(Math.random() * 5),
        banWarnings: Math.floor(Math.random() * 2),
        humanBehaviorScore: 70 + Math.floor(Math.random() * 30),
        isHumanActivityTime: new Date().getHours() >= 8 && new Date().getHours() <= 18,
        safetyLimits: {
          maxDailyMessages: 800,
          maxHourlyMessages: 100,
          maxConsecutiveMessages: 5
        }
      };

      const mockHumanBehaviorStats: HumanBehaviorStats = {
        messageCount: {
          minute: Math.floor(Math.random() * 8),
          hour: Math.floor(Math.random() * 100),
          day: Math.floor(Math.random() * 800)
        },
        burstCount: Math.floor(Math.random() * 5),
        behaviorScore: 70 + Math.floor(Math.random() * 30),
        suspiciousPatterns: Math.floor(Math.random() * 3)
      };

      const mockRiskAssessment: RiskAssessment = {
        riskScore: mockAntiBanStats.riskLevel,
        riskLevel: mockAntiBanStats.riskLevel > 70 ? 'high' : 
                  mockAntiBanStats.riskLevel > 40 ? 'medium' : 'low',
        safeToSend: mockAntiBanStats.riskLevel < 70,
        nextSafeTime: mockAntiBanStats.riskLevel > 70 ? 1800000 : 0,
        recommendations: generateRecommendations(mockAntiBanStats)
      };

      setAntiBanStats(mockAntiBanStats);
      setHumanBehaviorStats(mockHumanBehaviorStats);
      setRiskAssessment(mockRiskAssessment);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar dados anti-ban');
    } finally {
      setLoading(false);
    }
  };

  const generateRecommendations = (stats: AntiBanStats): string[] => {
    const recommendations = [];
    
    if (stats.riskLevel > 70) {
      recommendations.push('Nível de risco alto - faça uma pausa');
    }
    
    if (!stats.isHumanActivityTime) {
      recommendations.push('Fora do horário de atividade humana');
    }
    
    if (stats.suspiciousActivities > 2) {
      recommendations.push('Atividades suspeitas detectadas');
    }
    
    return recommendations;
  };

  const getRiskColor = (riskLevel: number) => {
    if (riskLevel > 70) return 'text-red-500';
    if (riskLevel > 40) return 'text-orange-500';
    return 'text-green-500';
  };

  const getRiskIcon = (riskLevel: number) => {
    if (riskLevel > 70) return '🚨';
    if (riskLevel > 40) return '⚠️';
    return '✅';
  };

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  const handleForceBreak = async () => {
    try {
      // Em produção, chamaria a API
      console.log('Forçando pausa de segurança...');
      // await fetch('/api/antiban/force-break', { method: 'POST' });
      await loadAntiBanData();
    } catch (error) {
      console.error('Erro ao forçar pausa:', error);
    }
  };

  const handleReset = async () => {
    try {
      // Em produção, chamaria a API
      console.log('Resetando sistema anti-ban...');
      // await fetch('/api/antiban/reset', { method: 'POST' });
      await loadAntiBanData();
    } catch (error) {
      console.error('Erro ao resetar:', error);
    }
  };

  if (loading) {
    return (
      <div className="glass-card p-4 mb-6">
        <div className="flex items-center space-x-3">
          <div className="loading-modern">
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
          </div>
          <div>
            <h3 className="font-semibold text-gray-800">🤖 Anti-Ban</h3>
            <p className="text-sm text-gray-600">Carregando dados anti-ban...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="glass-card p-4 mb-6">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">❌</span>
          <div>
            <h3 className="font-semibold text-gray-800">🤖 Anti-Ban</h3>
            <p className="text-sm text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="glass-card p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{getRiskIcon(antiBanStats?.riskLevel || 0)}</span>
          <div>
            <h3 className="font-semibold text-gray-800">🤖 Sistema Anti-Ban</h3>
            <p className={`text-sm font-medium ${getRiskColor(antiBanStats?.riskLevel || 0)}`}>
              Risco: {antiBanStats?.riskLevel || 0}% | 
              Comportamento: {humanBehaviorStats?.behaviorScore || 0}/100
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {antiBanStats?.isInBreak && (
            <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">
              Em pausa
            </span>
          )}
          
          {!riskAssessment?.safeToSend && (
            <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
              Não seguro
            </span>
          )}
          
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="px-3 py-1 text-sm bg-white/30 hover:bg-white/50 rounded-lg transition-colors"
          >
            {showDetails ? 'Ocultar' : 'Detalhes'}
          </button>
        </div>
      </div>

      {showDetails && antiBanStats && humanBehaviorStats && riskAssessment && (
        <div className="space-y-4 pt-4 border-t border-white/20">
          {/* Indicadores de Risco */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Nível de Risco */}
            <div className="bg-white/30 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">🚨 Nível de Risco</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Score Atual</span>
                  <span className={getRiskColor(antiBanStats.riskLevel)}>{antiBanStats.riskLevel}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-500 ${
                      antiBanStats.riskLevel > 70 ? 'bg-red-500' :
                      antiBanStats.riskLevel > 40 ? 'bg-orange-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${antiBanStats.riskLevel}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-600">
                  Status: {riskAssessment.riskLevel.toUpperCase()}
                </div>
              </div>
            </div>

            {/* Comportamento Humano */}
            <div className="bg-white/30 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">👤 Comportamento</h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Score Humano</span>
                  <span className={humanBehaviorStats.behaviorScore > 80 ? 'text-green-600' : 'text-orange-500'}>
                    {humanBehaviorStats.behaviorScore}/100
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Padrões Suspeitos</span>
                  <span className={humanBehaviorStats.suspiciousPatterns > 0 ? 'text-red-500' : 'text-green-600'}>
                    {humanBehaviorStats.suspiciousPatterns}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Burst Atual</span>
                  <span className={humanBehaviorStats.burstCount > 3 ? 'text-red-500' : 'text-green-600'}>
                    {humanBehaviorStats.burstCount}
                  </span>
                </div>
              </div>
            </div>

            {/* Status da Sessão */}
            <div className="bg-white/30 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">⏱️ Sessão</h4>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Tempo Ativo</span>
                  <span>{formatTime(antiBanStats.sessionTime)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Limite Máximo</span>
                  <span>{formatTime(antiBanStats.maxSessionTime)}</span>
                </div>
                {antiBanStats.isInBreak && (
                  <div className="flex justify-between">
                    <span>Pausa Restante</span>
                    <span className="text-orange-600">{formatTime(antiBanStats.breakTimeRemaining)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Contadores de Mensagens */}
          <div className="bg-white/30 rounded-lg p-3">
            <h4 className="font-medium text-gray-800 mb-2">📊 Contadores de Mensagens</h4>
            <div className="grid grid-cols-3 gap-4 text-center text-sm">
              <div>
                <div className="text-lg font-bold text-gray-700">{humanBehaviorStats.messageCount.minute}</div>
                <div className="text-gray-600">Por Minuto</div>
                <div className="text-xs text-gray-500">Limite: 8</div>
              </div>
              <div>
                <div className="text-lg font-bold text-gray-700">{humanBehaviorStats.messageCount.hour}</div>
                <div className="text-gray-600">Por Hora</div>
                <div className="text-xs text-gray-500">Limite: {antiBanStats.safetyLimits.maxHourlyMessages}</div>
              </div>
              <div>
                <div className="text-lg font-bold text-gray-700">{humanBehaviorStats.messageCount.day}</div>
                <div className="text-gray-600">Por Dia</div>
                <div className="text-xs text-gray-500">Limite: {antiBanStats.safetyLimits.maxDailyMessages}</div>
              </div>
            </div>
          </div>

          {/* Recomendações */}
          {riskAssessment.recommendations.length > 0 && (
            <div className="bg-white/30 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">💡 Recomendações</h4>
              <div className="space-y-1">
                {riskAssessment.recommendations.map((rec, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <span className="text-orange-500">⚠️</span>
                    <span className="text-gray-700">{rec}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Status Atual */}
          <div className="bg-white/30 rounded-lg p-3">
            <h4 className="font-medium text-gray-800 mb-2">📍 Status Atual</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className={`flex items-center space-x-2 ${riskAssessment.safeToSend ? 'text-green-600' : 'text-red-500'}`}>
                <span>{riskAssessment.safeToSend ? '✅' : '❌'}</span>
                <span>Seguro para enviar</span>
              </div>
              <div className={`flex items-center space-x-2 ${antiBanStats.isHumanActivityTime ? 'text-green-600' : 'text-orange-500'}`}>
                <span>{antiBanStats.isHumanActivityTime ? '🟢' : '🟡'}</span>
                <span>Horário humano</span>
              </div>
              <div className={`flex items-center space-x-2 ${antiBanStats.suspiciousActivities === 0 ? 'text-green-600' : 'text-red-500'}`}>
                <span>{antiBanStats.suspiciousActivities === 0 ? '✅' : '🚨'}</span>
                <span>Atividades suspeitas: {antiBanStats.suspiciousActivities}</span>
              </div>
              <div className={`flex items-center space-x-2 ${antiBanStats.banWarnings === 0 ? 'text-green-600' : 'text-red-500'}`}>
                <span>{antiBanStats.banWarnings === 0 ? '✅' : '⚠️'}</span>
                <span>Avisos de ban: {antiBanStats.banWarnings}</span>
              </div>
            </div>
          </div>

          {/* Ações */}
          <div className="flex space-x-2 pt-3">
            <button
              onClick={loadAntiBanData}
              className="px-3 py-1 text-xs bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              🔄 Atualizar
            </button>
            
            <button
              onClick={handleForceBreak}
              className="px-3 py-1 text-xs bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors"
            >
              ☕ Forçar Pausa
            </button>
            
            <button
              onClick={handleReset}
              className="px-3 py-1 text-xs bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
            >
              🔄 Reset Sistema
            </button>
            
            <button
              className="px-3 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              📊 Análise Completa
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
