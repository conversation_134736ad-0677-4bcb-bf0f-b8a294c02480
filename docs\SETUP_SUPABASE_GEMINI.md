# 🏛️ Guia de Configuração: Supabase + Gemini

Guia completo para configurar Supabase e Google Gemini no Sistema RAG da Vereadora Rafaela de Nilda.

## 📋 Índice

1. [Configuração do Supabase](#-configuração-do-supabase)
2. [Configuração do Google Gemini](#-configuração-do-google-gemini)
3. [Configuração das Variáveis de Ambiente](#-configuração-das-variáveis-de-ambiente)
4. [Teste das Configurações](#-teste-das-configurações)
5. [Troubleshooting](#-troubleshooting)

## 🗄️ Configuração do Supabase

### Passo 1: Criar Conta e Projeto

1. **Acesse o Supabase:**
   - Vá para [supabase.com](https://supabase.com)
   - Clique em "Start your project"
   - Faça login com GitHub, Google ou email

2. **Criar Novo Projeto:**
   - Clique em "New Project"
   - Escolha sua organização
   - Preencha os dados:
     - **Name**: `vereadora-rafaela-rag`
     - **Database Password**: Crie uma senha forte
     - **Region**: `South America (São Paulo)` (mais próximo do Brasil)
   - Clique em "Create new project"

3. **Aguardar Provisionamento:**
   - O processo leva 2-3 minutos
   - Você receberá um email quando estiver pronto

### Passo 2: Configurar o Banco de Dados

1. **Acessar SQL Editor:**
   - No dashboard do projeto, vá para "SQL Editor"
   - Clique em "New query"

2. **Executar Schema:**
   - Copie todo o conteúdo do arquivo `database/schema.sql`
   - Cole no editor SQL
   - Clique em "Run" para executar

3. **Verificar Tabelas:**
   - Vá para "Table Editor"
   - Confirme que as tabelas foram criadas:
     - `conversations`
     - `messages`
     - `documents`
     - `document_chunks`
     - `interaction_logs`

### Passo 3: Configurar Storage (Opcional)

1. **Criar Bucket:**
   - Vá para "Storage"
   - Clique em "Create bucket"
   - Nome: `documents`
   - Público: `false`
   - Clique em "Create bucket"

2. **Configurar Políticas:**
   ```sql
   -- Permitir upload de documentos
   CREATE POLICY "Allow document uploads" ON storage.objects
   FOR INSERT WITH CHECK (bucket_id = 'documents');
   
   -- Permitir leitura de documentos
   CREATE POLICY "Allow document downloads" ON storage.objects
   FOR SELECT USING (bucket_id = 'documents');
   ```

### Passo 4: Obter Credenciais

1. **Acessar Settings:**
   - Vá para "Settings" > "API"

2. **Copiar Informações:**
   - **Project URL**: `https://seu-projeto.supabase.co`
   - **anon public key**: Chave que começa com `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **service_role key**: (Opcional) Para operações administrativas

## 🤖 Configuração do Google Gemini

### Passo 1: Acessar Google AI Studio

1. **Acesse o Google AI Studio:**
   - Vá para [makersuite.google.com](https://makersuite.google.com)
   - Faça login com sua conta Google

2. **Aceitar Termos:**
   - Leia e aceite os termos de uso
   - Confirme que está ciente das limitações

### Passo 2: Criar API Key

1. **Gerar API Key:**
   - Clique em "Get API key"
   - Clique em "Create API key in new project"
   - Ou selecione um projeto existente do Google Cloud

2. **Copiar API Key:**
   - Copie a chave gerada (começa com `AIzaSy...`)
   - **IMPORTANTE**: Guarde em local seguro, não será mostrada novamente

3. **Configurar Restrições (Recomendado):**
   - No Google Cloud Console, vá para "APIs & Services" > "Credentials"
   - Clique na API key criada
   - Configure restrições:
     - **Application restrictions**: HTTP referrers
     - **API restrictions**: Generative Language API

### Passo 3: Testar API

1. **Teste Básico:**
   ```bash
   curl -H 'Content-Type: application/json' \
        -d '{"contents":[{"parts":[{"text":"Olá, como você está?"}]}]}' \
        -X POST 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=SUA_API_KEY'
   ```

2. **Verificar Resposta:**
   - Deve retornar uma resposta JSON com o texto gerado
   - Se houver erro, verifique a API key e permissões

## ⚙️ Configuração das Variáveis de Ambiente

### Passo 1: Criar Arquivo .env

1. **Copiar Template:**
   ```bash
   cp .env.example .env
   ```

2. **Editar Variáveis:**
   ```env
   # Supabase
   VITE_SUPABASE_URL=https://seu-projeto.supabase.co
   VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   
   # Gemini
   VITE_GEMINI_API_KEY=AIzaSyD...
   
   # Outras configurações
   VITE_VEREADORA_NAME=Rafaela de Nilda
   VITE_MUNICIPIO=Parnamirim
   VITE_ESTADO=RN
   ```

### Passo 2: Verificar Configurações

1. **Variáveis Obrigatórias:**
   - `VITE_SUPABASE_URL`
   - `VITE_SUPABASE_ANON_KEY`
   - `VITE_GEMINI_API_KEY`

2. **Variáveis Opcionais:**
   - `VITE_GABINETE_TELEFONE`
   - `VITE_GABINETE_EMAIL`
   - `VITE_HORARIO_INICIO`
   - `VITE_HORARIO_FIM`

## 🧪 Teste das Configurações

### Teste 1: Conexão Supabase

1. **Abrir Console do Navegador:**
   - Pressione F12
   - Vá para a aba "Console"

2. **Executar Teste:**
   ```javascript
   // No console do navegador
   fetch('https://seu-projeto.supabase.co/rest/v1/', {
     headers: {
       'apikey': 'sua-anon-key',
       'Authorization': 'Bearer sua-anon-key'
     }
   }).then(r => r.json()).then(console.log)
   ```

3. **Resultado Esperado:**
   - Deve retornar informações sobre as tabelas
   - Se houver erro 401, verifique a API key

### Teste 2: Conexão Gemini

1. **Teste via Aplicação:**
   - Inicie a aplicação: `npm run dev`
   - Abra o chat
   - Digite uma mensagem de teste
   - Verifique se a resposta é gerada

2. **Verificar Logs:**
   - No console do navegador, procure por:
     - `✅ Gemini configurado`
     - `🤖 Resposta gerada pelo Gemini`

### Teste 3: Integração Completa

1. **Teste de Upload:**
   - Faça upload de um documento PDF
   - Verifique se aparece na aba "Documentos"
   - Confirme que o status muda para "Pronto"

2. **Teste de Chat:**
   - Faça uma pergunta sobre o documento
   - Verifique se a resposta usa informações do documento
   - Confirme que as fontes são mostradas

## 🔧 Troubleshooting

### Problema: Supabase não conecta

**Sintomas:**
- Erro "Failed to fetch" no console
- Dados não são salvos
- Aplicação funciona apenas offline

**Soluções:**
1. Verificar URL do projeto (sem barra no final)
2. Confirmar anon key (deve ser a chave pública)
3. Verificar se RLS está configurado corretamente
4. Testar conexão direta via curl

### Problema: Gemini não responde

**Sintomas:**
- Respostas genéricas/fallback
- Erro "Gemini não configurado"
- Timeout nas requisições

**Soluções:**
1. Verificar API key (deve começar com AIzaSy)
2. Confirmar que a API está habilitada no Google Cloud
3. Verificar cotas e limites de uso
4. Testar API key diretamente

### Problema: Embeddings não funcionam

**Sintomas:**
- Busca semântica não encontra resultados
- Erro ao processar documentos
- Chunks sem embeddings

**Soluções:**
1. Verificar modelo de embedding (text-embedding-004)
2. Confirmar dimensões do vetor (768)
3. Verificar extensão pgvector no Supabase
4. Testar geração de embedding isoladamente

### Problema: Performance lenta

**Sintomas:**
- Respostas demoram muito
- Interface trava
- Timeouts frequentes

**Soluções:**
1. Verificar região do Supabase (usar São Paulo)
2. Otimizar queries com índices
3. Implementar cache mais agressivo
4. Reduzir tamanho dos chunks

## 📊 Monitoramento

### Supabase Dashboard

1. **Métricas:**
   - Database > Reports
   - API > Logs
   - Auth > Users

2. **Logs:**
   - Logs > API
   - Logs > Database
   - Logs > Auth

### Gemini Usage

1. **Google Cloud Console:**
   - APIs & Services > Enabled APIs
   - Generative Language API > Quotas
   - Generative Language API > Metrics

2. **Monitorar:**
   - Requests per day
   - Tokens per minute
   - Error rate

## 🔒 Segurança

### Supabase

1. **RLS (Row Level Security):**
   - Sempre habilitado em produção
   - Políticas específicas por tabela
   - Teste com usuários diferentes

2. **API Keys:**
   - Nunca expor service_role key no frontend
   - Usar anon key apenas para operações públicas
   - Rotacionar keys periodicamente

### Gemini

1. **API Key:**
   - Nunca commitar no código
   - Usar variáveis de ambiente
   - Configurar restrições de domínio

2. **Rate Limiting:**
   - Implementar cache local
   - Limitar requisições por usuário
   - Monitorar uso da quota

## 📝 Próximos Passos

1. **Configurar Backup:**
   - Backup automático do Supabase
   - Export regular dos dados
   - Versionamento do schema

2. **Otimização:**
   - Índices customizados
   - Cache Redis (opcional)
   - CDN para assets

3. **Monitoramento:**
   - Alertas de erro
   - Métricas de performance
   - Dashboard de uso

---

🏛️ **Sistema RAG - Vereadora Rafaela de Nilda**  
📍 Câmara Municipal de Parnamirim/RN
