import express from 'express';
import { WhatsAppSimulatorService } from './src/services/WhatsAppSimulatorService.js';

const app = express();
const port = 3002;

app.use(express.json());

// Criar instância do simulador
const simulator = new WhatsAppSimulatorService();

// Rota de teste
app.get('/test', (req, res) => {
  res.json({ 
    message: 'Servidor de teste funcionando',
    simulator: {
      isConnected: simulator.isConnected,
      simulationMode: simulator.simulationMode
    }
  });
});

// Rota para status do simulador
app.get('/status', (req, res) => {
  const status = simulator.getConnectionStatus();
  res.json(status);
});

// Rota para conectar simulador
app.post('/connect', async (req, res) => {
  try {
    await simulator.simulateConnection();
    res.json({ success: true, message: 'Conexão simulada iniciada' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Inicializar simulador
async function init() {
  try {
    console.log('🔄 Inicializando simulador...');
    await simulator.initialize();
    console.log('✅ Simulador inicializado');
    
    // Iniciar servidor
    app.listen(port, () => {
      console.log(`🚀 Servidor de teste rodando na porta ${port}`);
      console.log(`📱 Teste: http://localhost:${port}/test`);
      console.log(`📊 Status: http://localhost:${port}/status`);
    });
    
  } catch (error) {
    console.error('❌ Erro ao inicializar:', error);
    process.exit(1);
  }
}

init();
