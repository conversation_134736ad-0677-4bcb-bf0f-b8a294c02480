#!/usr/bin/env node

/**
 * Script de gerenciamento do WPPConnect Server
 * Vereadora Rafaela de Nilda - Sistema RAG
 */

import { spawn, exec } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

class WPPConnectServerManager {
  constructor() {
    this.containerName = 'wppconnect-server-rafaela';
    this.composeFile = 'docker-compose.wppconnect.yml';
    this.sessionName = 'vereadora-rafaela';
    this.serverUrl = 'http://localhost:21465';
  }

  async checkDocker() {
    return new Promise((resolve) => {
      exec('docker --version', (error) => {
        resolve(!error);
      });
    });
  }

  async checkDockerCompose() {
    return new Promise((resolve) => {
      exec('docker-compose --version', (error) => {
        resolve(!error);
      });
    });
  }

  async createDirectories() {
    const dirs = [
      'wppconnect-data',
      'wppconnect-data/sessions',
      'wppconnect-data/tokens',
      'wppconnect-data/downloads',
      'wppconnect-data/uploads'
    ];

    for (const dir of dirs) {
      const fullPath = path.join(rootDir, dir);
      try {
        await fs.mkdir(fullPath, { recursive: true });
        console.log(`✅ Diretório criado: ${dir}`);
      } catch (error) {
        console.log(`ℹ️ Diretório já existe: ${dir}`);
      }
    }
  }

  async start() {
    console.log('🚀 Iniciando WPPConnect Server...');
    
    // Verificar Docker
    if (!(await this.checkDocker())) {
      console.error('❌ Docker não encontrado. Instale o Docker primeiro.');
      process.exit(1);
    }

    if (!(await this.checkDockerCompose())) {
      console.error('❌ Docker Compose não encontrado. Instale o Docker Compose primeiro.');
      process.exit(1);
    }

    // Criar diretórios necessários
    await this.createDirectories();

    // Iniciar container
    return new Promise((resolve, reject) => {
      const process = spawn('docker-compose', [
        '-f', this.composeFile,
        'up', '-d'
      ], {
        cwd: rootDir,
        stdio: 'inherit'
      });

      process.on('close', (code) => {
        if (code === 0) {
          console.log('✅ WPPConnect Server iniciado com sucesso!');
          console.log(`🌐 API disponível em: ${this.serverUrl}`);
          console.log(`📱 Sessão: ${this.sessionName}`);
          resolve();
        } else {
          console.error(`❌ Erro ao iniciar WPPConnect Server (código: ${code})`);
          reject(new Error(`Processo terminou com código ${code}`));
        }
      });
    });
  }

  async stop() {
    console.log('🛑 Parando WPPConnect Server...');
    
    return new Promise((resolve, reject) => {
      const process = spawn('docker-compose', [
        '-f', this.composeFile,
        'down'
      ], {
        cwd: rootDir,
        stdio: 'inherit'
      });

      process.on('close', (code) => {
        if (code === 0) {
          console.log('✅ WPPConnect Server parado com sucesso!');
          resolve();
        } else {
          console.error(`❌ Erro ao parar WPPConnect Server (código: ${code})`);
          reject(new Error(`Processo terminou com código ${code}`));
        }
      });
    });
  }

  async restart() {
    console.log('🔄 Reiniciando WPPConnect Server...');
    await this.stop();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Aguardar 2s
    await this.start();
  }

  async status() {
    console.log('📊 Verificando status do WPPConnect Server...');
    
    return new Promise((resolve) => {
      exec(`docker ps --filter name=${this.containerName} --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"`, (error, stdout) => {
        if (error) {
          console.log('❌ Container não está rodando');
          resolve(false);
        } else {
          console.log(stdout);
          resolve(stdout.includes(this.containerName));
        }
      });
    });
  }

  async logs() {
    console.log('📋 Logs do WPPConnect Server:');
    
    const process = spawn('docker-compose', [
      '-f', this.composeFile,
      'logs', '-f', '--tail=100'
    ], {
      cwd: rootDir,
      stdio: 'inherit'
    });

    // Ctrl+C para sair
    process.on('SIGINT', () => {
      console.log('\n👋 Saindo dos logs...');
      process.kill();
    });
  }

  async health() {
    console.log('🏥 Verificando saúde do WPPConnect Server...');
    
    try {
      const response = await fetch(`${this.serverUrl}/api/${this.sessionName}/status`);
      const data = await response.json();
      
      console.log('✅ Servidor respondendo:');
      console.log(JSON.stringify(data, null, 2));
      return true;
    } catch (error) {
      console.log('❌ Servidor não está respondendo:', error.message);
      return false;
    }
  }

  showHelp() {
    console.log(`
🏛️ WPPConnect Server Manager - Vereadora Rafaela de Nilda

Comandos disponíveis:
  start     - Iniciar o WPPConnect Server
  stop      - Parar o WPPConnect Server  
  restart   - Reiniciar o WPPConnect Server
  status    - Verificar status do container
  logs      - Visualizar logs em tempo real
  health    - Verificar saúde da API
  help      - Mostrar esta ajuda

Exemplos:
  node scripts/wppconnect-server.js start
  node scripts/wppconnect-server.js logs
  node scripts/wppconnect-server.js health
    `);
  }
}

// Executar comando
const manager = new WPPConnectServerManager();
const command = process.argv[2];

switch (command) {
  case 'start':
    manager.start().catch(console.error);
    break;
  case 'stop':
    manager.stop().catch(console.error);
    break;
  case 'restart':
    manager.restart().catch(console.error);
    break;
  case 'status':
    manager.status().catch(console.error);
    break;
  case 'logs':
    manager.logs().catch(console.error);
    break;
  case 'health':
    manager.health().catch(console.error);
    break;
  case 'help':
  default:
    manager.showHelp();
    break;
}
