import fs from 'fs/promises';
import path from 'path';
import pdf from 'pdf-parse';
import mammoth from 'mammoth';
import xlsx from 'xlsx';
import { parse as csvParse } from 'csv-parse/sync';
import { JSDOM } from 'jsdom';
import { marked } from 'marked';
import { Logger } from '../utils/Logger.js';
import { createClient } from '@supabase/supabase-js';

export class DocumentProcessorService {
  constructor() {
    this.logger = new Logger();
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );
    
    // Configurações de processamento
    this.supportedFormats = {
      'pdf': { processor: 'processPDF', maxSize: 50 * 1024 * 1024 }, // 50MB
      'docx': { processor: 'processDOCX', maxSize: 25 * 1024 * 1024 }, // 25MB
      'doc': { processor: 'processDOC', maxSize: 25 * 1024 * 1024 }, // 25MB
      'txt': { processor: 'processTXT', maxSize: 10 * 1024 * 1024 }, // 10MB
      'md': { processor: 'processMarkdown', maxSize: 10 * 1024 * 1024 }, // 10MB
      'html': { processor: 'processHTML', maxSize: 10 * 1024 * 1024 }, // 10MB
      'xlsx': { processor: 'processXLSX', maxSize: 15 * 1024 * 1024 }, // 15MB
      'xls': { processor: 'processXLS', maxSize: 15 * 1024 * 1024 }, // 15MB
      'csv': { processor: 'processCSV', maxSize: 10 * 1024 * 1024 }, // 10MB
      'json': { processor: 'processJSON', maxSize: 10 * 1024 * 1024 }, // 10MB
      'xml': { processor: 'processXML', maxSize: 10 * 1024 * 1024 }, // 10MB
      'rtf': { processor: 'processRTF', maxSize: 10 * 1024 * 1024 } // 10MB
    };

    // Configurações de chunks
    this.chunkConfig = {
      defaultSize: 1000,
      overlap: 200,
      minChunkSize: 100,
      maxChunkSize: 2000
    };
  }

  /**
   * Processa um arquivo baseado em sua extensão
   */
  async processFile(filePath, metadata = {}) {
    try {
      this.logger.info(`📄 Iniciando processamento do arquivo: ${filePath}`);
      
      // 1. Validar arquivo
      const fileInfo = await this.validateFile(filePath);
      
      // 2. Determinar processador
      const processor = this.supportedFormats[fileInfo.extension]?.processor;
      if (!processor) {
        throw new Error(`Formato n&#227;o suportado: ${fileInfo.extension}`);
      }

      // 3. Processar conte&#250;do
      const content = await this[processor](filePath, fileInfo);
      
      // 4. Salvar documento no banco
      const document = await this.saveDocument(filePath, content, fileInfo, metadata);
      
      // 5. Processar chunks
      await this.processDocumentChunks(document.id, content);
      
      // 6. Atualizar status
      await this.updateDocumentStatus(document.id, 'completed');
      
      this.logger.info(`✅ Arquivo processado com sucesso: ${document.id}`);
      return document;
      
    } catch (error) {
      this.logger.error(`❌ Erro ao processar arquivo ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Valida arquivo antes do processamento
   */
  async validateFile(filePath) {
    try {
      const stats = await fs.stat(filePath);
      const extension = path.extname(filePath).toLowerCase().slice(1);
      const fileName = path.basename(filePath);
      
      // Verificar se formato &#233; suportado
      if (!this.supportedFormats[extension]) {
        throw new Error(`Formato n&#227;o suportado: ${extension}`);
      }
      
      // Verificar tamanho do arquivo
      const maxSize = this.supportedFormats[extension].maxSize;
      if (stats.size &gt; maxSize) {
        throw new Error(`Arquivo muito grande. M&#225;ximo: ${maxSize / 1024 / 1024}MB`);
      }
      
      return {
        fileName,
        extension,
        size: stats.size,
        path: filePath,
        mimeType: this.getMimeType(extension)
      };
      
    } catch (error) {
      this.logger.error('❌ Erro na valida&#231;&#227;o do arquivo:', error);
      throw error;
    }
  }

  /**
   * Processadores espec&#237;ficos por formato
   */
  
  // PDF
  async processPDF(filePath) {
    try {
      const buffer = await fs.readFile(filePath);
      const data = await pdf(buffer);
      
      return {
        text: data.text,
        metadata: {
          pages: data.numpages,
          info: data.info,
          version: data.version
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar PDF: ${error.message}`);
    }
  }

  // DOCX
  async processDOCX(filePath) {
    try {
      const buffer = await fs.readFile(filePath);
      const result = await mammoth.extractRawText({ buffer });
      
      return {
        text: result.value,
        metadata: {
          warnings: result.messages,
          wordCount: result.value.split(/\s+/).length
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar DOCX: ${error.message}`);
    }
  }

  // DOC (usando mammoth tamb&#233;m)
  async processDOC(filePath) {
    return await this.processDOCX(filePath);
  }

  // TXT
  async processTXT(filePath) {
    try {
      const text = await fs.readFile(filePath, 'utf-8');
      
      return {
        text: text,
        metadata: {
          encoding: 'utf-8',
          lineCount: text.split(&#39;\n&#39;).length,
          wordCount: text.split(/\s+/).length
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar TXT: ${error.message}`);
    }
  }

  // Markdown
  async processMarkdown(filePath) {
    try {
      const markdown = await fs.readFile(filePath, 'utf-8');
      const html = marked(markdown);
      const text = this.stripHTML(html);
      
      return {
        text: text,
        metadata: {
          originalFormat: 'markdown',
          htmlLength: html.length,
          wordCount: text.split(/\s+/).length
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar Markdown: ${error.message}`);
    }
  }

  // HTML
  async processHTML(filePath) {
    try {
      const html = await fs.readFile(filePath, 'utf-8');
      const text = this.stripHTML(html);
      
      return {
        text: text,
        metadata: {
          originalFormat: 'html',
          htmlLength: html.length,
          wordCount: text.split(/\s+/).length
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar HTML: ${error.message}`);
    }
  }

  // XLSX
  async processXLSX(filePath) {
    try {
      const workbook = xlsx.readFile(filePath);
      let allText = '';
      const sheetsData = {};
      
      workbook.SheetNames.forEach(sheetName =&gt; {
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
        
        // Converter dados para texto
        const sheetText = jsonData
          .map(row =&gt; row.join(' | '))
          .join('\n');
        
        sheetsData[sheetName] = {
          text: sheetText,
          rows: jsonData.length,
          columns: Math.max(...jsonData.map(row =&gt; row.length))
        };
        
        allText += `\n=== ${sheetName} ===\n${sheetText}\n`;
      });
      
      return {
        text: allText.trim(),
        metadata: {
          sheets: Object.keys(sheetsData),
          sheetsData: sheetsData,
          totalSheets: workbook.SheetNames.length
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar XLSX: ${error.message}`);
    }
  }

  // XLS
  async processXLS(filePath) {
    return await this.processXLSX(filePath);
  }

  // CSV
  async processCSV(filePath) {
    try {
      const csvContent = await fs.readFile(filePath, 'utf-8');
      const records = csvParse(csvContent, {
        skip_empty_lines: true,
        trim: true
      });
      
      // Converter para texto estruturado
      const text = records
        .map(row =&gt; row.join(' | '))
        .join('\n');
      
      return {
        text: text,
        metadata: {
          rows: records.length,
          columns: records[0]?.length || 0,
          delimiter: this.detectCSVDelimiter(csvContent)
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar CSV: ${error.message}`);
    }
  }

  // JSON
  async processJSON(filePath) {
    try {
      const jsonContent = await fs.readFile(filePath, 'utf-8');
      const data = JSON.parse(jsonContent);
      
      // Converter JSON para texto leg&#237;vel
      const text = this.jsonToText(data);
      
      return {
        text: text,
        metadata: {
          originalFormat: 'json',
          dataType: Array.isArray(data) ? 'array' : typeof data,
          size: Object.keys(data).length
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar JSON: ${error.message}`);
    }
  }

  // XML
  async processXML(filePath) {
    try {
      const xmlContent = await fs.readFile(filePath, 'utf-8');
      
      // Remover tags XML e extrair texto
      const text = xmlContent
        .replace(/&lt;[^&gt;]*&gt;/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
      
      return {
        text: text,
        metadata: {
          originalFormat: 'xml',
          xmlLength: xmlContent.length,
          wordCount: text.split(/\s+/).length
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar XML: ${error.message}`);
    }
  }

  // RTF
  async processRTF(filePath) {
    try {
      const rtfContent = await fs.readFile(filePath, 'utf-8');
      const dom = new JSDOM(rtfContent);
      const text = dom.window.document.body.textContent.trim();
      
      return {
        text: text,
        metadata: {
          originalFormat: 'rtf',
          textLength: text.length,
          wordCount: text.split(/\s+/).length
        }
      };
    } catch (error) {
      throw new Error(`Erro ao processar RTF: ${error.message}`);
    }
  }

  /**
   * Auxiliar para detectar delimitador CSV
   */
  detectCSVDelimiter(csvContent) {
    const firstLine = csvContent.split('\n')[0];
    const possibleDelimiters = [',', ';', '\t', '|'];
    let delimiter = ',';
    let maxMatches = 0;

    possibleDelimiters.forEach(delim => {
      const matches = firstLine.split(delim).length - 1;
      if (matches &gt; maxMatches) {
        maxMatches = matches;
        delimiter = delim;
      }
    });

    return delimiter;
  }

  /**
   * Auxiliar para converter JSON para texto
   */
  jsonToText(data) {
    if (Array.isArray(data)) {
      return data.map(item => this.jsonToText(item)).join('\n');
    } else if (typeof data === 'object' &amp;&amp; data !== null) {
      return Object.entries(data).map(([key, value]) => `${key}: ${this.jsonToText(value)}`).join(', ');
    } else {
      return String(data);
    }
  }

  /**
   * Auxiliar para remover HTML
   */
  stripHTML(html) {
    const doc = new JSDOM(html);
    return doc.window.document.body.textContent.trim();
  }

  /**
   * Salva documento no banco de dados
   */
  async saveDocument(filePath, content, fileInfo, metadata) {
    try {
      const { data, error } = await this.supabase
        .from('documents')
        .insert([
          {
            file_name: fileInfo.fileName,
            file_path: fileInfo.path,
            mime_type: fileInfo.mimeType,
            size: fileInfo.size,
            content: content.text,
            metadata: { ...metadata, ...content.metadata }
          }
        ])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      this.logger.error('❌ Erro ao salvar documento:', error);
      throw error;
    }
  }

  /**
   * Processa chunks do documento
   */
  async processDocumentChunks(documentId, content) {
    try {
      const text = content.text;
      const chunkSize = this.chunkConfig.defaultSize;
      const overlap = this.chunkConfig.overlap;
      const minChunkSize = this.chunkConfig.minChunkSize;
      const maxChunkSize = this.chunkConfig.maxChunkSize;

      for (let i = 0; i &lt; text.length; i += chunkSize - overlap) {
        let chunk = text.substring(i, i + chunkSize);

        // Ajustar tamanho do chunk se necessário
        while (chunk.length &gt; maxChunkSize) {
          chunk = chunk.substring(0, maxChunkSize);
        }
        while (chunk.length &lt; minChunkSize &amp;&amp; i + chunk.length &lt; text.length) {
          chunk += text.substring(i + chunk.length, i + chunkSize + 1);
        }

        await this.saveChunk(documentId, chunk);
      }
    } catch (error) {
      this.logger.error('❌ Erro ao processar chunks:', error);
      throw error;
    }
  }

  /**
   * Salva um chunk no banco de dados
   */
  async saveChunk(documentId, chunk) {
    try {
      const { error } = await this.supabase
        .from('document_chunks')
        .insert([
          {
            document_id: documentId,
            content: chunk
          }
        ]);

      if (error) {
        throw error;
      }
    } catch (error) {
      this.logger.error('❌ Erro ao salvar chunk:', error);
      throw error;
    }
  }

  /**
   * Atualiza o status do documento
   */
  async updateDocumentStatus(documentId, status) {
    try {
      const { error } = await this.supabase
        .from('documents')
        .update({ status })
        .eq('id', documentId);

      if (error) {
        throw error;
      }
    } catch (error) {
      this.logger.error('❌ Erro ao atualizar status do documento:', error);
      throw error;
    }
  }

  /**
   * Obtém o tipo MIME baseado na extensão do arquivo
   */
  getMimeType(extension) {
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'docx':
      case 'doc':
        return 'application/msword';
      case 'txt':
        return 'text/plain';
      case 'md':
        return 'text/markdown';
      case 'html':
        return 'text/html';
      case 'xlsx':
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'csv':
        return 'text/csv';
      case 'json':
        return 'application/json';
      case 'xml':
        return 'application/xml';
      case 'rtf':
        return 'application/rtf';
      default:
        return 'application/octet-stream';
    }
  }
}