@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Modern GitHub-inspired Design System */
:root {
  /* GitHub Colors */
  --gh-bg: #ffffff;
  --gh-text: #24292f;
  --gh-text-secondary: #656d76;
  --gh-link: #0969da;
  --gh-border: #d0d7de;
  --gh-code-bg: #0d1117;
  --gh-code-text: #c9d1d9;
  --gh-sidebar: #f6f8fa;
  --gh-hover: #f3f4f6;

  /* Semantic Colors */
  --success: #22c55e;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #0969da;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Clean modern body - GitHub style */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';
  line-height: 1.5;
  color: var(--gh-text);
  background-color: var(--gh-bg);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern scrollbar - GitHub style */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--gh-sidebar);
}

::-webkit-scrollbar-thumb {
  background: var(--gh-border);
  border-radius: var(--radius);
  border: 2px solid var(--gh-sidebar);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gh-text-secondary);
}

/* Modern animations */
.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.15s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.15s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(4px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Modern card components - GitHub style */
.card-modern {
  background: var(--gh-bg);
  border: 1px solid var(--gh-border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow);
  transition: all 0.15s ease;
}

.card-modern:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--gh-border);
  background: var(--gh-sidebar);
}

.card-content {
  padding: var(--space-lg);
}

/* Modern message bubbles - Clean GitHub style */
.message-bubble {
  max-width: 85%;
  word-wrap: break-word;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.15s ease;
  animation: slideUp 0.15s ease-out;
  margin-bottom: var(--space);
}

.user-message {
  background: var(--gh-link);
  color: white;
  margin-left: auto;
  border-bottom-right-radius: var(--radius-sm);
}

.bot-message {
  background: var(--gh-sidebar);
  color: var(--gh-text);
  border: 1px solid var(--gh-border);
  border-bottom-left-radius: var(--radius-sm);
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

/* Modern buttons - GitHub style */
.btn-primary {
  background: var(--gh-link);
  color: white;
  border: 1px solid transparent;
  border-radius: var(--radius);
  padding: 8px 16px;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.15s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
}

.btn-primary:hover {
  background: #0550ae;
  box-shadow: var(--shadow);
}

.btn-secondary {
  background: var(--gh-bg);
  color: var(--gh-text);
  border: 1px solid var(--gh-border);
  border-radius: var(--radius);
  padding: 8px 16px;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.15s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
}

.btn-secondary:hover {
  background: var(--gh-hover);
  border-color: var(--gh-text-secondary);
}

.btn-danger {
  background: var(--danger);
  color: white;
  border: 1px solid transparent;
  border-radius: var(--radius);
  padding: 8px 16px;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.15s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
}

.btn-danger:hover {
  background: #dc2626;
  box-shadow: var(--shadow);
}

/* Modern inputs - GitHub style */
.input-modern {
  background: var(--gh-bg);
  border: 1px solid var(--gh-border);
  border-radius: var(--radius);
  padding: 8px 12px;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.15s ease;
  color: var(--gh-text);
}

.input-modern:focus {
  border-color: var(--gh-link);
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
  outline: none;
}

.input-modern::placeholder {
  color: var(--gh-text-secondary);
}

/* Modern tabs - GitHub style */
.tab-button {
  background: transparent;
  border: none;
  border-radius: var(--radius);
  padding: 8px 16px;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.15s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--gh-text-secondary);
}

.tab-button:hover {
  background: var(--gh-hover);
  color: var(--gh-text);
}

.tab-button.active {
  background: var(--gh-link);
  color: white;
}

/* Code blocks - GitHub style */
.code-block {
  background: var(--gh-code-bg);
  color: var(--gh-code-text);
  border-radius: var(--radius);
  padding: var(--space);
  font-family: var(--font-mono);
  font-size: 13px;
  line-height: 1.45;
  overflow-x: auto;
  border: 1px solid #30363d;
}

.code-inline {
  background: rgba(175, 184, 193, 0.2);
  color: var(--gh-text);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--font-mono);
  font-size: 85%;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
}

.status-success {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-info {
  background: rgba(9, 105, 218, 0.1);
  color: var(--info);
  border: 1px solid rgba(9, 105, 218, 0.2);
}

/* Modern loading spinner - GitHub style */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--gh-border);
  border-radius: 50%;
  border-top-color: var(--gh-link);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.loading-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: var(--gh-text-secondary);
  animation: loadingDots 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Modern sidebar - GitHub style */
.sidebar {
  background: var(--gh-sidebar);
  border-right: 1px solid var(--gh-border);
  min-height: 100vh;
}

.sidebar-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--gh-border);
  font-weight: 600;
  color: var(--gh-text);
}

.sidebar-nav {
  padding: var(--space);
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space);
  border-radius: var(--radius);
  color: var(--gh-text-secondary);
  text-decoration: none;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.15s ease;
}

.sidebar-nav-item:hover {
  background: var(--gh-hover);
  color: var(--gh-text);
}

.sidebar-nav-item.active {
  background: var(--gh-link);
  color: white;
}

/* Modern alerts */
.alert {
  padding: var(--space);
  border-radius: var(--radius);
  border: 1px solid;
  font-size: 14px;
  line-height: 20px;
}

.alert-info {
  background: rgba(9, 105, 218, 0.05);
  border-color: rgba(9, 105, 218, 0.2);
  color: var(--info);
}

.alert-success {
  background: rgba(34, 197, 94, 0.05);
  border-color: rgba(34, 197, 94, 0.2);
  color: var(--success);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.05);
  border-color: rgba(245, 158, 11, 0.2);
  color: var(--warning);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
  color: var(--danger);
}

/* WhatsApp specific styles */
.whatsapp-message {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100%;
}

.whatsapp-message-container {
  max-width: calc(100% - 2rem);
}

/* Responsive design */
@media (max-width: 768px) {
  :root {
    --space: 0.75rem;
    --space-lg: 1rem;
    --space-xl: 1.5rem;
  }

  .message-bubble {
    max-width: 90%;
  }

  .card-modern {
    margin: var(--space-sm);
  }

  .whatsapp-message-container {
    max-width: calc(100% - 1rem);
  }

  .sidebar {
    min-height: auto;
  }
}

/* Sidebar Moderna */
.sidebar-modern {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border-left: 1px solid var(--gh-border);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.08);
}

.sidebar-nav-item {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.6s ease;
}

.sidebar-nav-item:hover::before {
  left: 100%;
}

.sidebar-nav-item.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.sidebar-nav-item:hover {
  transform: translateX(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sidebar-nav-item .icon {
  transition: all 0.3s ease;
}

.sidebar-nav-item:hover .icon {
  transform: scale(1.1) rotate(5deg);
}

.sidebar-nav-item.active .icon {
  transform: scale(1.15);
  color: #3b82f6;
}

/* Indicador de atividade */
.sidebar-activity-indicator {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0);
  }
}

/* Efeito de gradiente no hover */
.sidebar-gradient-hover {
  background: linear-gradient(135deg, transparent, rgba(59, 130, 246, 0.05));
  transition: all 0.3s ease;
}

.sidebar-gradient-hover:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(147, 51, 234, 0.05));
}

@media (max-width: 480px) {
  .btn-primary,
  .btn-secondary,
  .btn-danger {
    padding: 6px 12px;
    font-size: 13px;
  }

  .input-modern {
    padding: 6px 10px;
    font-size: 13px;
  }

  /* Sidebar responsiva */
  .sidebar-modern {
    width: 100%;
    height: auto;
    position: relative;
    border-left: none;
    border-top: 1px solid var(--gh-border);
  }
}
