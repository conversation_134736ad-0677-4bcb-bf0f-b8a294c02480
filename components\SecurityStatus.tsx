import React, { useState, useEffect } from 'react';

interface SecurityStatus {
  authentication: {
    enabled: boolean;
    methods: string[];
    rateLimiting: boolean;
    originVerification: boolean;
  };
  encryption: {
    sessionData: boolean;
    algorithm: string;
    keyRotation: boolean;
  };
  monitoring: {
    auditLogs: boolean;
    securityEvents: boolean;
    intrusionDetection: boolean;
  };
  backup: {
    encrypted: boolean;
    automated: boolean;
    retention: string;
  };
  compliance: {
    dataProtection: boolean;
    accessControl: boolean;
    auditTrail: boolean;
  };
}

interface ThreatSummary {
  total: number;
  high: number;
  medium: number;
  low: number;
  blocked: number;
}

export const SecurityStatus: React.FC = () => {
  const [securityStatus, setSecurityStatus] = useState<SecurityStatus | null>(null);
  const [threats, setThreats] = useState<ThreatSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    loadSecurityData();
    
    // Atualizar a cada 30 segundos
    const interval = setInterval(loadSecurityData, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const loadSecurityData = async () => {
    try {
      setError(null);
      
      // Simular dados de segurança (em produção, viria da API)
      const mockSecurityStatus: SecurityStatus = {
        authentication: {
          enabled: true,
          methods: ['api-key', 'jwt'],
          rateLimiting: true,
          originVerification: true
        },
        encryption: {
          sessionData: true,
          algorithm: 'AES-256-GCM',
          keyRotation: false
        },
        monitoring: {
          auditLogs: true,
          securityEvents: true,
          intrusionDetection: false
        },
        backup: {
          encrypted: true,
          automated: true,
          retention: '30 days'
        },
        compliance: {
          dataProtection: true,
          accessControl: true,
          auditTrail: true
        }
      };

      const mockThreats: ThreatSummary = {
        total: 2,
        high: 0,
        medium: 1,
        low: 1,
        blocked: 1
      };

      setSecurityStatus(mockSecurityStatus);
      setThreats(mockThreats);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar dados de segurança');
    } finally {
      setLoading(false);
    }
  };

  const getSecurityScore = () => {
    if (!securityStatus) return 0;
    
    let score = 0;
    let total = 0;
    
    // Autenticação (peso 25%)
    if (securityStatus.authentication.enabled) score += 25;
    if (securityStatus.authentication.rateLimiting) score += 10;
    if (securityStatus.authentication.originVerification) score += 10;
    total += 45;
    
    // Criptografia (peso 20%)
    if (securityStatus.encryption.sessionData) score += 15;
    if (securityStatus.encryption.keyRotation) score += 5;
    total += 20;
    
    // Monitoramento (peso 15%)
    if (securityStatus.monitoring.auditLogs) score += 5;
    if (securityStatus.monitoring.securityEvents) score += 5;
    if (securityStatus.monitoring.intrusionDetection) score += 5;
    total += 15;
    
    // Backup (peso 20%)
    if (securityStatus.backup.encrypted) score += 10;
    if (securityStatus.backup.automated) score += 10;
    total += 20;
    
    return Math.round((score / total) * 100);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-orange-500';
    return 'text-red-500';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 90) return '🛡️';
    if (score >= 70) return '⚠️';
    return '🚨';
  };

  if (loading) {
    return (
      <div className="glass-card p-4 mb-6">
        <div className="flex items-center space-x-3">
          <div className="loading-modern">
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
          </div>
          <div>
            <h3 className="font-semibold text-gray-800">🔒 Segurança</h3>
            <p className="text-sm text-gray-600">Verificando status de segurança...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="glass-card p-4 mb-6">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">❌</span>
          <div>
            <h3 className="font-semibold text-gray-800">🔒 Segurança</h3>
            <p className="text-sm text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  const securityScore = getSecurityScore();

  return (
    <div className="glass-card p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{getScoreIcon(securityScore)}</span>
          <div>
            <h3 className="font-semibold text-gray-800">🔒 Segurança do Sistema</h3>
            <p className={`text-sm font-medium ${getScoreColor(securityScore)}`}>
              Score: {securityScore}/100
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {threats && threats.total > 0 && (
            <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">
              {threats.total} ameaças
            </span>
          )}
          
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="px-3 py-1 text-sm bg-white/30 hover:bg-white/50 rounded-lg transition-colors"
          >
            {showDetails ? 'Ocultar' : 'Detalhes'}
          </button>
        </div>
      </div>

      {showDetails && securityStatus && (
        <div className="space-y-4 pt-4 border-t border-white/20">
          {/* Score Visual */}
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="flex justify-between text-sm mb-1">
                <span>Score de Segurança</span>
                <span className={getScoreColor(securityScore)}>{securityScore}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-500 ${
                    securityScore >= 90 ? 'bg-green-500' :
                    securityScore >= 70 ? 'bg-orange-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${securityScore}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Categorias de Segurança */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Autenticação */}
            <div className="bg-white/30 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">🔐 Autenticação</h4>
              <div className="space-y-1 text-xs">
                <div className={`flex items-center space-x-1 ${securityStatus.authentication.enabled ? 'text-green-600' : 'text-red-500'}`}>
                  <span>{securityStatus.authentication.enabled ? '✅' : '❌'}</span>
                  <span>Autenticação habilitada</span>
                </div>
                <div className={`flex items-center space-x-1 ${securityStatus.authentication.rateLimiting ? 'text-green-600' : 'text-red-500'}`}>
                  <span>{securityStatus.authentication.rateLimiting ? '✅' : '❌'}</span>
                  <span>Rate limiting</span>
                </div>
                <div className={`flex items-center space-x-1 ${securityStatus.authentication.originVerification ? 'text-green-600' : 'text-red-500'}`}>
                  <span>{securityStatus.authentication.originVerification ? '✅' : '❌'}</span>
                  <span>Verificação de origem</span>
                </div>
              </div>
            </div>

            {/* Criptografia */}
            <div className="bg-white/30 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">🔒 Criptografia</h4>
              <div className="space-y-1 text-xs">
                <div className={`flex items-center space-x-1 ${securityStatus.encryption.sessionData ? 'text-green-600' : 'text-red-500'}`}>
                  <span>{securityStatus.encryption.sessionData ? '✅' : '❌'}</span>
                  <span>Dados de sessão criptografados</span>
                </div>
                <div className="flex items-center space-x-1 text-gray-600">
                  <span>🔑</span>
                  <span>Algoritmo: {securityStatus.encryption.algorithm}</span>
                </div>
                <div className={`flex items-center space-x-1 ${securityStatus.encryption.keyRotation ? 'text-green-600' : 'text-orange-500'}`}>
                  <span>{securityStatus.encryption.keyRotation ? '✅' : '⚠️'}</span>
                  <span>Rotação de chaves</span>
                </div>
              </div>
            </div>

            {/* Monitoramento */}
            <div className="bg-white/30 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">📊 Monitoramento</h4>
              <div className="space-y-1 text-xs">
                <div className={`flex items-center space-x-1 ${securityStatus.monitoring.auditLogs ? 'text-green-600' : 'text-red-500'}`}>
                  <span>{securityStatus.monitoring.auditLogs ? '✅' : '❌'}</span>
                  <span>Logs de auditoria</span>
                </div>
                <div className={`flex items-center space-x-1 ${securityStatus.monitoring.securityEvents ? 'text-green-600' : 'text-red-500'}`}>
                  <span>{securityStatus.monitoring.securityEvents ? '✅' : '❌'}</span>
                  <span>Eventos de segurança</span>
                </div>
                <div className={`flex items-center space-x-1 ${securityStatus.monitoring.intrusionDetection ? 'text-green-600' : 'text-orange-500'}`}>
                  <span>{securityStatus.monitoring.intrusionDetection ? '✅' : '⚠️'}</span>
                  <span>Detecção de intrusão</span>
                </div>
              </div>
            </div>

            {/* Backup */}
            <div className="bg-white/30 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">💾 Backup</h4>
              <div className="space-y-1 text-xs">
                <div className={`flex items-center space-x-1 ${securityStatus.backup.encrypted ? 'text-green-600' : 'text-red-500'}`}>
                  <span>{securityStatus.backup.encrypted ? '✅' : '❌'}</span>
                  <span>Backups criptografados</span>
                </div>
                <div className={`flex items-center space-x-1 ${securityStatus.backup.automated ? 'text-green-600' : 'text-red-500'}`}>
                  <span>{securityStatus.backup.automated ? '✅' : '❌'}</span>
                  <span>Backup automático</span>
                </div>
                <div className="flex items-center space-x-1 text-gray-600">
                  <span>📅</span>
                  <span>Retenção: {securityStatus.backup.retention}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Ameaças */}
          {threats && (
            <div className="bg-white/30 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">🚨 Ameaças Detectadas</h4>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-xs">
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-700">{threats.total}</div>
                  <div className="text-gray-600">Total</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-red-500">{threats.high}</div>
                  <div className="text-gray-600">Alta</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-orange-500">{threats.medium}</div>
                  <div className="text-gray-600">Média</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-yellow-500">{threats.low}</div>
                  <div className="text-gray-600">Baixa</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-500">{threats.blocked}</div>
                  <div className="text-gray-600">Bloqueadas</div>
                </div>
              </div>
            </div>
          )}

          {/* Ações */}
          <div className="flex space-x-2 pt-3">
            <button
              onClick={loadSecurityData}
              className="px-3 py-1 text-xs bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              🔄 Atualizar
            </button>
            
            <button
              className="px-3 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              📊 Relatório Completo
            </button>
            
            <button
              className="px-3 py-1 text-xs bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
            >
              💾 Backup Manual
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
