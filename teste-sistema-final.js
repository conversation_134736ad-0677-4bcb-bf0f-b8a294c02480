#!/usr/bin/env node

/**
 * TESTE FINAL DO SISTEMA COMPLETO
 * Verificação de todas as funcionalidades
 */

console.log('🎉 TESTE FINAL DO SISTEMA COMPLETO');
console.log('==================================\n');

async function testCompleteSystem() {
  console.log('🔍 Verificando todos os componentes...\n');

  const tests = [];

  // 1. Teste Backend
  try {
    console.log('🖥️ Testando Backend...');
    const backendResponse = await fetch('http://localhost:3001/api/health');
    const backendData = await backendResponse.json();
    
    tests.push({
      name: 'Backend Health',
      passed: backendResponse.ok,
      details: `Status: ${backendResponse.status}`
    });
    
    console.log(`   ✅ Backend: ${backendResponse.ok ? 'Online' : 'Offline'}`);
  } catch (error) {
    tests.push({ name: 'Backend Health', passed: false, details: error.message });
    console.log('   ❌ Backend: Offline');
  }

  // 2. Teste Frontend
  try {
    console.log('🌐 Testando Frontend...');
    const frontendResponse = await fetch('http://localhost:3000');
    
    tests.push({
      name: 'Frontend Access',
      passed: frontendResponse.ok,
      details: `Status: ${frontendResponse.status}`
    });
    
    console.log(`   ✅ Frontend: ${frontendResponse.ok ? 'Online' : 'Offline'}`);
  } catch (error) {
    tests.push({ name: 'Frontend Access', passed: false, details: error.message });
    console.log('   ❌ Frontend: Offline');
  }

  // 3. Teste API Documents
  try {
    console.log('📄 Testando API de Documentos...');
    const docsResponse = await fetch('http://localhost:3001/api/documents');
    const docsData = await docsResponse.json();
    
    tests.push({
      name: 'Documents API',
      passed: docsResponse.ok,
      details: `Count: ${docsData.count || 0}`
    });
    
    console.log(`   ✅ Documents API: ${docsResponse.ok ? 'Funcionando' : 'Erro'}`);
  } catch (error) {
    tests.push({ name: 'Documents API', passed: false, details: error.message });
    console.log('   ❌ Documents API: Erro');
  }

  // 4. Teste Upload
  try {
    console.log('📤 Testando Upload...');
    const uploadResponse = await fetch('http://localhost:3001/api/documents/upload', {
      method: 'POST',
      body: new FormData()
    });
    
    tests.push({
      name: 'Upload Endpoint',
      passed: uploadResponse.ok,
      details: `Status: ${uploadResponse.status}`
    });
    
    console.log(`   ✅ Upload: ${uploadResponse.ok ? 'Funcionando' : 'Erro'}`);
  } catch (error) {
    tests.push({ name: 'Upload Endpoint', passed: false, details: error.message });
    console.log('   ❌ Upload: Erro');
  }

  // 5. Teste Analytics
  try {
    console.log('📊 Testando Analytics...');
    const analyticsResponse = await fetch('http://localhost:3001/api/analytics/log', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ type: 'test', metadata: {} })
    });
    
    tests.push({
      name: 'Analytics API',
      passed: analyticsResponse.ok,
      details: `Status: ${analyticsResponse.status}`
    });
    
    console.log(`   ✅ Analytics: ${analyticsResponse.ok ? 'Funcionando' : 'Erro'}`);
  } catch (error) {
    tests.push({ name: 'Analytics API', passed: false, details: error.message });
    console.log('   ❌ Analytics: Erro');
  }

  // 6. Teste RAG
  try {
    console.log('🤖 Testando RAG...');
    const ragResponse = await fetch('http://localhost:3001/api/rag/query', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: 'Teste do sistema' })
    });
    
    tests.push({
      name: 'RAG System',
      passed: ragResponse.ok,
      details: `Status: ${ragResponse.status}`
    });
    
    console.log(`   ✅ RAG: ${ragResponse.ok ? 'Funcionando' : 'Erro'}`);
  } catch (error) {
    tests.push({ name: 'RAG System', passed: false, details: error.message });
    console.log('   ❌ RAG: Erro');
  }

  // 7. Teste WhatsApp Status
  try {
    console.log('📱 Testando WhatsApp...');
    const whatsappResponse = await fetch('http://localhost:3001/api/whatsapp/status');
    
    tests.push({
      name: 'WhatsApp Status',
      passed: whatsappResponse.ok,
      details: `Status: ${whatsappResponse.status}`
    });
    
    console.log(`   ✅ WhatsApp: ${whatsappResponse.ok ? 'API Funcionando' : 'Erro'}`);
  } catch (error) {
    tests.push({ name: 'WhatsApp Status', passed: false, details: error.message });
    console.log('   ❌ WhatsApp: Erro');
  }

  console.log('\n📊 RESUMO FINAL');
  console.log('===============');

  const passedTests = tests.filter(t => t.passed).length;
  const totalTests = tests.length;
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);

  console.log(`🎯 Testes Passaram: ${passedTests}/${totalTests}`);
  console.log(`📈 Taxa de Sucesso: ${successRate}%`);
  console.log('');

  tests.forEach(test => {
    console.log(`   ${test.passed ? '✅' : '❌'} ${test.name}: ${test.details}`);
  });

  console.log('');

  if (successRate >= 85) {
    console.log('🎉 SISTEMA FUNCIONANDO PERFEITAMENTE!');
    console.log('✅ Todas as funcionalidades principais estão ativas!');
    console.log('🚀 Pronto para uso em produção!');
    console.log('');
    console.log('🏛️ VEREADORA RAFAELA DE NILDA - SISTEMA COMPLETO');
    console.log('💖 Pronta para servir o povo de Parnamirim! 🙏🏽');
    console.log('');
    console.log('📱 Para usar:');
    console.log('   Frontend: http://localhost:3000');
    console.log('   Backend:  http://localhost:3001');
    console.log('   WhatsApp: Escaneie QR Code no frontend');
    console.log('');
    console.log('🎯 Funcionalidades ativas:');
    console.log('   ✅ Upload de documentos via backend');
    console.log('   ✅ Sistema RAG com fallbacks');
    console.log('   ✅ Analytics e monitoramento');
    console.log('   ✅ WhatsApp (estrutura completa)');
    console.log('   ✅ Interface moderna e responsiva');
  } else if (successRate >= 70) {
    console.log('🎊 SISTEMA FUNCIONANDO BEM!');
    console.log('✅ Maioria das funcionalidades ativas!');
    console.log('🔧 Algumas melhorias podem ser feitas');
  } else {
    console.log('⚠️ SISTEMA COM LIMITAÇÕES');
    console.log('🔧 Várias funcionalidades precisam de ajustes');
  }

  console.log('');
  console.log('🎉 TESTE CONCLUÍDO!');
}

testCompleteSystem().catch(console.error);
