#!/usr/bin/env node

/**
 * Script de Teste para Servidor Centralizado
 * Vereadora Rafaela de Nilda - Sistema RAG
 */

import axios from 'axios';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

class ServerTester {
  constructor() {
    this.serverUrl = 'http://localhost:3001';
    this.serverProcess = null;
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🧪 Iniciando testes do servidor centralizado...\n');
    
    try {
      // Iniciar servidor
      await this.startServer();
      
      // Aguardar servidor ficar pronto
      await this.waitForServer();
      
      // Executar testes
      await this.testBasicEndpoints();
      await this.testHealthChecks();
      await this.testServiceManagement();
      await this.testWhatsAppEndpoints();
      await this.testRAGEndpoints();
      await this.testSecurityFeatures();
      
      // Mostrar resultados
      this.showResults();
      
    } catch (error) {
      console.error('❌ Erro durante os testes:', error.message);
      process.exit(1);
    } finally {
      // Parar servidor
      await this.stopServer();
    }
  }

  async startServer() {
    console.log('🚀 Iniciando servidor centralizado...');
    
    return new Promise((resolve, reject) => {
      this.serverProcess = spawn('node', ['server-centralized.js'], {
        cwd: rootDir,
        stdio: 'pipe',
        env: {
          ...process.env,
          NODE_ENV: 'test',
          PORT: '3001',
          WHATSAPP_USE_SIMULATOR: 'true',
          LOG_LEVEL: 'error'
        }
      });

      let output = '';
      
      this.serverProcess.stdout.on('data', (data) => {
        output += data.toString();
        if (output.includes('Servidor iniciado na porta')) {
          resolve();
        }
      });

      this.serverProcess.stderr.on('data', (data) => {
        const error = data.toString();
        if (error.includes('Error') && !error.includes('Warning')) {
          reject(new Error(`Erro ao iniciar servidor: ${error}`));
        }
      });

      this.serverProcess.on('error', (error) => {
        reject(new Error(`Falha ao iniciar processo: ${error.message}`));
      });

      // Timeout de 30 segundos
      setTimeout(() => {
        reject(new Error('Timeout ao iniciar servidor'));
      }, 30000);
    });
  }

  async waitForServer(timeout = 30000) {
    console.log('⏳ Aguardando servidor ficar disponível...');
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await axios.get(`${this.serverUrl}/api/health`, {
          timeout: 2000
        });
        
        if (response.status === 200) {
          console.log('✅ Servidor disponível\n');
          return;
        }
      } catch (error) {
        // Servidor ainda não está pronto
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error('Timeout aguardando servidor ficar disponível');
  }

  async testBasicEndpoints() {
    console.log('📋 Testando endpoints básicos...');
    
    const tests = [
      {
        name: 'Root endpoint',
        method: 'GET',
        path: '/',
        expectedStatus: 200
      },
      {
        name: 'Health check',
        method: 'GET',
        path: '/api/health',
        expectedStatus: 200
      },
      {
        name: '404 handling',
        method: 'GET',
        path: '/api/nonexistent',
        expectedStatus: 404
      }
    ];
    
    for (const test of tests) {
      await this.runTest(test);
    }
  }

  async testHealthChecks() {
    console.log('💚 Testando health checks...');
    
    const tests = [
      {
        name: 'Basic health check',
        method: 'GET',
        path: '/api/health',
        expectedStatus: 200,
        validateResponse: (data) => data.status === 'ok'
      },
      {
        name: 'Detailed health check',
        method: 'GET',
        path: '/api/health/detailed',
        expectedStatus: 200
      }
    ];
    
    for (const test of tests) {
      await this.runTest(test);
    }
  }

  async testServiceManagement() {
    console.log('🔧 Testando gerenciamento de serviços...');
    
    const tests = [
      {
        name: 'Services status',
        method: 'GET',
        path: '/api/services/status',
        expectedStatus: 200,
        headers: { 'X-API-Key': 'admin-key-2024' }
      },
      {
        name: 'Services health',
        method: 'GET',
        path: '/api/services/health',
        expectedStatus: [200, 503], // Pode retornar 503 se alguns serviços estão down
        headers: { 'X-API-Key': 'admin-key-2024' }
      }
    ];
    
    for (const test of tests) {
      await this.runTest(test);
    }
  }

  async testWhatsAppEndpoints() {
    console.log('📱 Testando endpoints WhatsApp...');
    
    const tests = [
      {
        name: 'WhatsApp status',
        method: 'GET',
        path: '/api/whatsapp/status',
        expectedStatus: 200
      },
      {
        name: 'WhatsApp QR code',
        method: 'GET',
        path: '/api/whatsapp/qr',
        expectedStatus: [200, 404] // Pode não ter QR se já conectado
      }
    ];
    
    for (const test of tests) {
      await this.runTest(test);
    }
  }

  async testRAGEndpoints() {
    console.log('🧠 Testando endpoints RAG...');
    
    const tests = [
      {
        name: 'RAG status',
        method: 'GET',
        path: '/api/rag/status',
        expectedStatus: 200
      },
      {
        name: 'RAG query (simple)',
        method: 'POST',
        path: '/api/rag/query',
        data: { query: 'Olá, como você pode me ajudar?' },
        expectedStatus: 200
      }
    ];
    
    for (const test of tests) {
      await this.runTest(test);
    }
  }

  async testSecurityFeatures() {
    console.log('🛡️ Testando recursos de segurança...');
    
    const tests = [
      {
        name: 'Protected endpoint without auth',
        method: 'GET',
        path: '/api/security/config',
        expectedStatus: 401
      },
      {
        name: 'Protected endpoint with auth',
        method: 'GET',
        path: '/api/security/config',
        headers: { 'X-API-Key': 'admin-key-2024' },
        expectedStatus: 200
      },
      {
        name: 'Rate limiting test',
        method: 'GET',
        path: '/api/health',
        repeat: 5,
        expectedStatus: 200
      }
    ];
    
    for (const test of tests) {
      await this.runTest(test);
    }
  }

  async runTest(test) {
    try {
      const config = {
        method: test.method,
        url: `${this.serverUrl}${test.path}`,
        timeout: 5000,
        validateStatus: () => true, // Não lançar erro para status HTTP
        headers: test.headers || {}
      };
      
      if (test.data) {
        config.data = test.data;
      }
      
      const repeat = test.repeat || 1;
      let lastResponse;
      
      for (let i = 0; i < repeat; i++) {
        lastResponse = await axios(config);
        if (repeat > 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      const response = lastResponse;
      const expectedStatuses = Array.isArray(test.expectedStatus) 
        ? test.expectedStatus 
        : [test.expectedStatus];
      
      const statusOk = expectedStatuses.includes(response.status);
      const validationOk = test.validateResponse 
        ? test.validateResponse(response.data) 
        : true;
      
      if (statusOk && validationOk) {
        console.log(`   ✅ ${test.name} - Status: ${response.status}`);
        this.testResults.push({ name: test.name, status: 'PASS', details: `Status: ${response.status}` });
      } else {
        console.log(`   ❌ ${test.name} - Status: ${response.status} (esperado: ${test.expectedStatus})`);
        this.testResults.push({ 
          name: test.name, 
          status: 'FAIL', 
          details: `Status: ${response.status}, esperado: ${test.expectedStatus}` 
        });
      }
      
    } catch (error) {
      console.log(`   ❌ ${test.name} - Erro: ${error.message}`);
      this.testResults.push({ 
        name: test.name, 
        status: 'ERROR', 
        details: error.message 
      });
    }
  }

  async stopServer() {
    if (this.serverProcess) {
      console.log('\n🛑 Parando servidor...');
      this.serverProcess.kill('SIGTERM');
      
      // Aguardar processo terminar
      await new Promise((resolve) => {
        this.serverProcess.on('close', resolve);
        setTimeout(resolve, 5000); // Timeout de 5s
      });
    }
  }

  showResults() {
    console.log('\n📊 Resultados dos Testes:');
    console.log('=' .repeat(50));
    
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const errors = this.testResults.filter(r => r.status === 'ERROR').length;
    const total = this.testResults.length;
    
    console.log(`\n📈 Resumo:`);
    console.log(`   ✅ Passou: ${passed}/${total}`);
    console.log(`   ❌ Falhou: ${failed}/${total}`);
    console.log(`   🔥 Erros: ${errors}/${total}`);
    
    if (failed > 0 || errors > 0) {
      console.log('\n❌ Testes com falha:');
      this.testResults
        .filter(r => r.status !== 'PASS')
        .forEach(r => {
          console.log(`   • ${r.name}: ${r.details}`);
        });
    }
    
    const successRate = (passed / total) * 100;
    console.log(`\n🎯 Taxa de sucesso: ${successRate.toFixed(1)}%`);
    
    if (successRate >= 90) {
      console.log('\n🎉 Servidor centralizado funcionando corretamente!');
    } else if (successRate >= 70) {
      console.log('\n⚠️ Servidor funcionando com algumas limitações.');
    } else {
      console.log('\n❌ Servidor com problemas significativos.');
      process.exit(1);
    }
  }
}

// Executar testes
const tester = new ServerTester();
tester.runAllTests().catch(console.error);
