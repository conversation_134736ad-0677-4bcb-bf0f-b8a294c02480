#!/usr/bin/env node

/**
 * Servidor Backend Centralizado - Versão Mínima
 * Vereadora Rafaela de Nilda - Sistema RAG
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Importar configuração centralizada
import { ServerConfig } from './src/config/ServerConfig.js';
import { Logger } from './src/utils/Logger.js';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '.env') });

/**
 * Servidor Backend Centralizado - Versão Mínima
 */
class MinimalServer {
  constructor() {
    this.app = express();
    this.config = new ServerConfig();
    this.logger = new Logger();
    
    // Estado do servidor
    this.isInitialized = false;
    this.server = null;
    
    this.setupMiddlewares();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddlewares() {
    this.logger.info('🔧 Configurando middlewares...');

    // Segurança
    this.app.use(helmet({
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false
    }));

    // CORS
    this.app.use(cors({
      origin: this.config.cors.allowedOrigins,
      credentials: this.config.cors.credentials,
      methods: this.config.cors.methods,
      allowedHeaders: this.config.cors.allowedHeaders
    }));

    // Compressão
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: this.config.rateLimit.windowMs,
      max: this.config.rateLimit.maxRequests,
      message: {
        error: 'Muitas requisições. Tente novamente em alguns minutos.',
        code: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api/', limiter);

    // Logging
    this.app.use(morgan('combined', {
      stream: { write: (message) => this.logger.info(message.trim()) }
    }));

    // Body parsing
    this.app.use(express.json({ limit: this.config.server.bodyLimit }));
    this.app.use(express.urlencoded({ 
      extended: true, 
      limit: this.config.server.bodyLimit 
    }));

    // Servir arquivos estáticos
    this.app.use('/static', express.static(join(__dirname, 'public')));
    
    this.logger.info('✅ Middlewares configurados');
  }

  setupRoutes() {
    this.logger.info('🛣️ Configurando rotas...');

    // Rota de boas-vindas
    this.app.get('/', (req, res) => {
      res.json({
        message: '🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda (Centralizado)',
        version: this.config.app.version,
        status: 'online',
        timestamp: new Date().toISOString(),
        environment: this.config.app.environment,
        mode: 'minimal',
        endpoints: {
          health: '/api/health',
          config: '/api/config',
          status: '/api/status'
        }
      });
    });

    // Health check
    this.app.get('/api/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: this.config.app.version,
        environment: this.config.app.environment,
        mode: 'minimal'
      });
    });

    // Status da configuração
    this.app.get('/api/config', (req, res) => {
      res.json({
        server: {
          port: this.config.server.port,
          environment: this.config.app.environment
        },
        whatsapp: {
          useSimulator: this.config.whatsapp.useSimulator,
          useReal: this.config.whatsapp.useReal,
          useHttp: this.config.whatsapp.useHttp,
          sessionName: this.config.whatsapp.sessionName
        },
        integrations: {
          hasGeminiKey: !!this.config.gemini.apiKey,
          hasSupabaseUrl: !!this.config.supabase.url,
          ragEnabled: this.config.rag.enabled,
          cacheEnabled: this.config.cache.enabled
        },
        timestamp: new Date().toISOString()
      });
    });

    // Status dos serviços (simulado)
    this.app.get('/api/status', (req, res) => {
      res.json({
        server: 'online',
        services: {
          whatsapp: 'not_initialized',
          rag: 'not_initialized',
          persistence: 'not_initialized',
          session: 'not_initialized'
        },
        message: 'Servidor em modo mínimo - serviços não inicializados',
        timestamp: new Date().toISOString()
      });
    });

    // Rota de teste RAG (simulada)
    this.app.post('/api/rag/query', (req, res) => {
      const { query } = req.body;
      
      if (!query) {
        return res.status(400).json({
          error: 'Query é obrigatória',
          code: 'MISSING_QUERY'
        });
      }

      // Resposta simulada
      res.json({
        query,
        response: `Olá! Esta é uma resposta simulada para: "${query}". O sistema RAG completo será ativado quando todos os serviços estiverem funcionando.`,
        sources: [],
        metadata: {
          mode: 'simulated',
          timestamp: new Date().toISOString(),
          processingTime: Math.random() * 1000
        }
      });
    });
    
    // Rota 404
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint não encontrado',
        message: 'A rota solicitada não existe neste servidor',
        availableEndpoints: [
          '/',
          '/api/health',
          '/api/config',
          '/api/status',
          '/api/rag/query'
        ]
      });
    });
    
    this.logger.info('✅ Rotas configuradas');
  }

  setupErrorHandling() {
    this.logger.info('⚠️ Configurando tratamento de erros...');

    // Middleware de tratamento de erros
    this.app.use((error, req, res, next) => {
      this.logger.error('❌ Erro no servidor:', error);
      
      res.status(500).json({
        error: 'Erro interno do servidor',
        message: error.message,
        timestamp: new Date().toISOString(),
        mode: 'minimal'
      });
    });

    // Tratamento de promises rejeitadas
    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Promise rejeitada não tratada:', reason);
    });

    // Tratamento de exceções não capturadas
    process.on('uncaughtException', (error) => {
      this.logger.error('Exceção não capturada:', error);
      setTimeout(() => process.exit(1), 1000);
    });
    
    this.logger.info('✅ Tratamento de erros configurado');
  }

  async start() {
    try {
      this.logger.info('🚀 Iniciando servidor centralizado (modo mínimo)...');

      // Iniciar servidor HTTP
      this.server = this.app.listen(this.config.server.port, () => {
        this.logger.info(`✅ Servidor iniciado na porta ${this.config.server.port}`);
        this.logger.info(`🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda`);
        this.logger.info(`📱 Ambiente: ${this.config.app.environment}`);
        this.logger.info(`🌐 URL: http://localhost:${this.config.server.port}`);
        this.logger.info(`⚡ Modo: Mínimo (sem serviços complexos)`);
        
        console.log('\n🎯 Endpoints disponíveis:');
        console.log(`   GET  /                    - Informações do servidor`);
        console.log(`   GET  /api/health          - Health check`);
        console.log(`   GET  /api/config          - Configuração atual`);
        console.log(`   GET  /api/status          - Status dos serviços`);
        console.log(`   POST /api/rag/query       - Query RAG (simulada)`);
        console.log('\n✅ Servidor funcionando em modo mínimo!');
      });

      // Configurar graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      this.logger.error('❌ Erro ao iniciar servidor:', error);
      throw error;
    }
  }

  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      this.logger.info(`🛑 Recebido sinal ${signal}, iniciando shutdown graceful...`);
      
      try {
        if (this.server) {
          this.server.close();
        }
        this.logger.info('✅ Shutdown graceful concluído');
        process.exit(0);
      } catch (error) {
        this.logger.error('❌ Erro durante shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
  }
}

// Se executado diretamente, iniciar servidor
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new MinimalServer();
  
  server.start().catch((error) => {
    console.error('Falha ao iniciar servidor:', error);
    process.exit(1);
  });
}
