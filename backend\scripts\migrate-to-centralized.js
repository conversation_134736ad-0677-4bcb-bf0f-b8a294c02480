#!/usr/bin/env node

/**
 * Script de Migração para Servidor Centralizado
 * Vereadora Rafaela de Nilda - Sistema RAG
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

class MigrationManager {
  constructor() {
    this.backupDir = path.join(rootDir, 'migration-backup');
    this.steps = [
      'checkPrerequisites',
      'createBackup',
      'validateConfiguration',
      'installDependencies',
      'migrateConfiguration',
      'testCentralizedServer',
      'updatePM2Config',
      'finalValidation'
    ];
    this.currentStep = 0;
  }

  async run() {
    console.log('🚀 Iniciando migração para servidor centralizado...\n');
    
    try {
      for (const step of this.steps) {
        this.currentStep++;
        console.log(`📋 Passo ${this.currentStep}/${this.steps.length}: ${step}`);
        await this[step]();
        console.log(`✅ ${step} concluído\n`);
      }
      
      console.log('🎉 Migração concluída com sucesso!');
      this.showNextSteps();
      
    } catch (error) {
      console.error(`❌ Erro na migração (${this.steps[this.currentStep - 1]}):`, error.message);
      console.log('\n🔄 Para reverter as mudanças, execute:');
      console.log('node scripts/migrate-to-centralized.js --rollback');
      process.exit(1);
    }
  }

  async checkPrerequisites() {
    console.log('   Verificando pré-requisitos...');
    
    // Verificar Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      throw new Error(`Node.js 18+ é necessário. Versão atual: ${nodeVersion}`);
    }
    
    // Verificar se os arquivos necessários existem
    const requiredFiles = [
      'package.json',
      'server-centralized.js',
      'src/config/ServerConfig.js',
      'src/services/ServiceManager.js'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(rootDir, file);
      try {
        await fs.access(filePath);
      } catch (error) {
        throw new Error(`Arquivo necessário não encontrado: ${file}`);
      }
    }
    
    console.log('   ✓ Node.js version OK');
    console.log('   ✓ Arquivos necessários encontrados');
  }

  async createBackup() {
    console.log('   Criando backup dos arquivos atuais...');
    
    // Criar diretório de backup
    await fs.mkdir(this.backupDir, { recursive: true });
    
    // Arquivos para backup
    const filesToBackup = [
      'server.js',
      'src/server.js',
      'package.json',
      'ecosystem.config.cjs',
      '.env'
    ];
    
    for (const file of filesToBackup) {
      const sourcePath = path.join(rootDir, file);
      const backupPath = path.join(this.backupDir, file.replace('/', '_'));
      
      try {
        await fs.copyFile(sourcePath, backupPath);
        console.log(`   ✓ Backup criado: ${file}`);
      } catch (error) {
        if (error.code !== 'ENOENT') {
          throw error;
        }
        console.log(`   ⚠️ Arquivo não encontrado (ignorado): ${file}`);
      }
    }
    
    // Salvar timestamp do backup
    const backupInfo = {
      timestamp: new Date().toISOString(),
      version: '2.0.0',
      files: filesToBackup
    };
    
    await fs.writeFile(
      path.join(this.backupDir, 'backup-info.json'),
      JSON.stringify(backupInfo, null, 2)
    );
  }

  async validateConfiguration() {
    console.log('   Validando configuração...');
    
    // Verificar se .env existe
    const envPath = path.join(rootDir, '.env');
    const envExamplePath = path.join(rootDir, '.env.example');
    
    try {
      await fs.access(envPath);
      console.log('   ✓ Arquivo .env encontrado');
    } catch (error) {
      console.log('   ⚠️ Arquivo .env não encontrado, criando a partir do exemplo...');
      await fs.copyFile(envExamplePath, envPath);
    }
    
    // Ler configuração atual
    const envContent = await fs.readFile(envPath, 'utf8');
    
    // Verificar configurações essenciais
    const requiredConfigs = [
      'PORT',
      'WHATSAPP_SESSION_NAME',
      'WPPCONNECT_SECRET_KEY'
    ];
    
    const missingConfigs = [];
    
    for (const config of requiredConfigs) {
      if (!envContent.includes(`${config}=`)) {
        missingConfigs.push(config);
      }
    }
    
    if (missingConfigs.length > 0) {
      console.log(`   ⚠️ Configurações faltantes: ${missingConfigs.join(', ')}`);
      console.log('   📝 Adicionando configurações padrão...');
      
      let newContent = envContent;
      
      for (const config of missingConfigs) {
        switch (config) {
          case 'PORT':
            newContent += '\nPORT=3001';
            break;
          case 'WHATSAPP_SESSION_NAME':
            newContent += '\nWHATSAPP_SESSION_NAME=vereadora-rafaela';
            break;
          case 'WPPCONNECT_SECRET_KEY':
            newContent += '\nWPPCONNECT_SECRET_KEY=vereadora-rafaela-secret-2024';
            break;
        }
      }
      
      await fs.writeFile(envPath, newContent);
    }
    
    console.log('   ✓ Configuração validada');
  }

  async installDependencies() {
    console.log('   Instalando dependências atualizadas...');
    
    try {
      const { stdout, stderr } = await execAsync('npm install', { cwd: rootDir });
      
      if (stderr && !stderr.includes('WARN')) {
        console.log('   ⚠️ Avisos durante instalação:', stderr);
      }
      
      console.log('   ✓ Dependências instaladas');
    } catch (error) {
      throw new Error(`Falha ao instalar dependências: ${error.message}`);
    }
  }

  async migrateConfiguration() {
    console.log('   Migrando configurações...');
    
    // Verificar se há configurações específicas nos servidores antigos
    const oldServerPath = path.join(rootDir, 'server.js');
    const oldSrcServerPath = path.join(rootDir, 'src/server.js');
    
    try {
      const oldServerContent = await fs.readFile(oldServerPath, 'utf8');
      
      // Extrair porta se diferente do padrão
      const portMatch = oldServerContent.match(/PORT.*?(\d+)/);
      if (portMatch && portMatch[1] !== '3001') {
        console.log(`   📝 Porta customizada detectada: ${portMatch[1]}`);
        
        const envPath = path.join(rootDir, '.env');
        let envContent = await fs.readFile(envPath, 'utf8');
        envContent = envContent.replace(/PORT=\d+/, `PORT=${portMatch[1]}`);
        await fs.writeFile(envPath, envContent);
      }
      
    } catch (error) {
      // Arquivo não existe, continuar
    }
    
    console.log('   ✓ Configurações migradas');
  }

  async testCentralizedServer() {
    console.log('   Testando servidor centralizado...');
    
    // Teste básico de sintaxe
    try {
      await execAsync('node --check server-centralized.js', { cwd: rootDir });
      console.log('   ✓ Sintaxe do servidor centralizado OK');
    } catch (error) {
      throw new Error(`Erro de sintaxe no servidor centralizado: ${error.message}`);
    }
    
    // Teste de importação dos módulos
    try {
      await execAsync('node -e "import(\'./server-centralized.js\')"', { cwd: rootDir });
      console.log('   ✓ Importação de módulos OK');
    } catch (error) {
      console.log('   ⚠️ Aviso na importação (pode ser normal):', error.message.split('\n')[0]);
    }
  }

  async updatePM2Config() {
    console.log('   Atualizando configuração do PM2...');
    
    const pm2ConfigPath = path.join(rootDir, 'ecosystem.config.cjs');
    
    try {
      await fs.access(pm2ConfigPath);
      console.log('   ✓ Configuração PM2 já atualizada');
    } catch (error) {
      throw new Error('Configuração PM2 não encontrada. Execute a migração novamente.');
    }
  }

  async finalValidation() {
    console.log('   Executando validação final...');
    
    // Verificar estrutura de diretórios
    const requiredDirs = [
      'src/config',
      'src/services',
      'src/routes',
      'src/middleware',
      'src/utils',
      'data',
      'logs'
    ];
    
    for (const dir of requiredDirs) {
      const dirPath = path.join(rootDir, dir);
      try {
        await fs.access(dirPath);
        console.log(`   ✓ Diretório OK: ${dir}`);
      } catch (error) {
        console.log(`   📁 Criando diretório: ${dir}`);
        await fs.mkdir(dirPath, { recursive: true });
      }
    }
    
    // Verificar permissões de escrita
    const testFile = path.join(rootDir, 'logs', 'migration-test.log');
    await fs.writeFile(testFile, 'test');
    await fs.unlink(testFile);
    
    console.log('   ✓ Permissões de escrita OK');
    console.log('   ✅ Validação final concluída');
  }

  showNextSteps() {
    console.log(`
🎯 Próximos Passos:

1. 📝 Revisar configurações:
   nano .env

2. 🚀 Testar servidor centralizado:
   npm run dev

3. 📊 Verificar status dos serviços:
   npm run services:status

4. 🔄 Iniciar com PM2 (produção):
   npm run pm2:start

5. 📋 Monitorar logs:
   npm run pm2:logs

6. 🗑️ Remover arquivos antigos (após validação):
   rm server.js src/server.js

📚 Documentação completa:
   cat README-CENTRALIZED.md

🔄 Para reverter (se necessário):
   node scripts/migrate-to-centralized.js --rollback
    `);
  }

  async rollback() {
    console.log('🔄 Iniciando rollback da migração...\n');
    
    try {
      const backupInfoPath = path.join(this.backupDir, 'backup-info.json');
      const backupInfo = JSON.parse(await fs.readFile(backupInfoPath, 'utf8'));
      
      console.log(`📅 Restaurando backup de: ${backupInfo.timestamp}`);
      
      for (const file of backupInfo.files) {
        const backupPath = path.join(this.backupDir, file.replace('/', '_'));
        const targetPath = path.join(rootDir, file);
        
        try {
          await fs.copyFile(backupPath, targetPath);
          console.log(`✅ Restaurado: ${file}`);
        } catch (error) {
          console.log(`⚠️ Não foi possível restaurar: ${file}`);
        }
      }
      
      console.log('\n✅ Rollback concluído!');
      console.log('🔄 Execute npm install para restaurar dependências antigas se necessário.');
      
    } catch (error) {
      console.error('❌ Erro durante rollback:', error.message);
      process.exit(1);
    }
  }
}

// Executar migração
const manager = new MigrationManager();

if (process.argv.includes('--rollback')) {
  manager.rollback();
} else {
  manager.run();
}
