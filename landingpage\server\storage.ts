import { users, sessions, type User, type InsertUser, type Session, type InsertSession } from "@shared/schema";
import { eq } from "drizzle-orm";
import bcrypt from "bcrypt";
import crypto from "crypto";

// For demonstration purposes, we'll use an in-memory storage
// In production, you would use your actual database connection
const inMemoryUsers: User[] = [];
const inMemorySessions: Session[] = [];

// Create a demo user for testing
const demoUser: User = {
  id: 1,
  email: "<EMAIL>",
  password: "$2b$10$e0JmVwdVTPLT6dB09ANL3umEGvkX8bKddgbDSa/Upzo4yQWu1YLKC", // password: "123456"
  createdAt: new Date(),
  updatedAt: new Date(),
};

inMemoryUsers.push(demoUser);

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean>;
  createSession(userId: number, rememberMe?: boolean): Promise<Session>;
  getSession(token: string): Promise<Session | undefined>;
  deleteSession(token: string): Promise<void>;
  getUserBySession(token: string): Promise<User | undefined>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    return inMemoryUsers.find(u => u.id === id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return inMemoryUsers.find(u => u.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const hashedPassword = await bcrypt.hash(insertUser.password, 10);
    const newUser: User = {
      id: inMemoryUsers.length + 1,
      email: insertUser.email,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    inMemoryUsers.push(newUser);
    return newUser;
  }

  async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  async createSession(userId: number, rememberMe: boolean = false): Promise<Session> {
    const token = crypto.randomBytes(32).toString("hex");
    const expiresAt = new Date();
    
    // Set expiration: 7 days if remember me, 1 day otherwise
    expiresAt.setDate(expiresAt.getDate() + (rememberMe ? 7 : 1));

    const newSession: Session = {
      id: inMemorySessions.length + 1,
      userId,
      token,
      expiresAt,
      createdAt: new Date(),
    };
    
    inMemorySessions.push(newSession);
    return newSession;
  }

  async getSession(token: string): Promise<Session | undefined> {
    const session = inMemorySessions.find(s => s.token === token);
    if (!session) return undefined;
    
    // Check if session is expired
    if (new Date() > session.expiresAt) {
      await this.deleteSession(token);
      return undefined;
    }
    
    return session;
  }

  async deleteSession(token: string): Promise<void> {
    const index = inMemorySessions.findIndex(s => s.token === token);
    if (index > -1) {
      inMemorySessions.splice(index, 1);
    }
  }

  async getUserBySession(token: string): Promise<User | undefined> {
    const session = await this.getSession(token);
    if (!session) return undefined;
    
    return await this.getUser(session.userId);
  }
}

export const storage = new DatabaseStorage();
